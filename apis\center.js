import {
  ReqClient
} from '../utils/request'
//保证金查询
export const deposit_search = (data) => {
  return ReqClient('/api/deposit/account/get', 'POST', {
    ...data
  })
}

//查询集中仓信息
export const upload_sign = (id) => {
  return ReqClient(`/api/warehouse/${id}`, 'GET', )
}


//提现申请
export const withdraw_apply = (data) => {
  return ReqClient('/api/withdraw/apply', 'POST', {
    ...data
  })
}

//提现订单记录
export const withdraw_record = (data) => {
  return ReqClient('/api/withdraw/apply/list', 'POST', {
    ...data
  })
}

export const supplier_search = (userId) => {
  return ReqClient(`/api/supplier/user/${userId}`, 'GET', {

  })
}

// 更新上一条接口地址
export const get_supplier_search = (data) => {
  return ReqClient(`/api/supplier/get/user`, 'POST', {
    ...data
  })
}

//查询用户信息
export const search_user_info = (userId) => {
  return ReqClient(`/api/user/${userId}`, 'GET', {})
}

export const get_user_info = (data) => {
  return ReqClient(`/api/user/get`, 'POST', {
    ...data
  })
}