import { ReqClient } from '../utils/request'

// 商品详情
export const product_detail = (data) => {
  return ReqClient("/api/product/get", 'POST',{
  ...data
})
}

// 查询统计-月销-回头率等
export const statistics = (data) => {
  return ReqClient('/api/supplier/stats/for/user', 'POST',{
    ...data
  })
}


// 商品查询-根据供应商和上下架状态
export const product_list = (data) => {
  return ReqClient('/api/product/list/supplier/for/user', 'POST', {
    ...data
  })
}

// 新增收藏
export const collect_add = (data) => {
  return ReqClient('/api/product/collect/upsert', 'POST', {
    ...data
  })
}

// 商品详情提示图片列表
export const desc_tips_img = () => {
  return ReqClient(`/api/sys/desc/img/list`, 'GET',)
}

// 创建浏览记录
export const create_browse_history = (data) => {
  return ReqClient('/api/product/browse/create', 'POST', {
    ...data
  })
}

// 查询 达到时间-商品详情
export const arrive_time = (data) => {
  return ReqClient('/api/route/service/point/arrive', 'POST', {
    ...data
  })
}

//商品评论
export const product_comment = (data) => {
  return ReqClient('/api/comment/list/product', 'POST', {
    ...data
  })
}

// 查询服务点列表
export const service_point_list = (data) => {
  return ReqClient('/api/service/point/list/location', 'POST', {
    ...data
  })
}

// 添加和更新
export const cart_updata = (data) => {
  return ReqClient('/api/product/cart/upsert', 'POST', {
    ...data
  })
}

// 供应商详情
export const supplier_detail = (id) => {
  return ReqClient(`/api/supplier/${id}`, 'GET', )
}

// 更新接口
export const get_supplier = (data) => {
  return ReqClient('/api/supplier/get', 'POST', {
    ...data
  })
}

// 新建关联
export const product_link = (data) => {
  return ReqClient('/api/product/link/create', 'POST', {
    ...data
  })
}

// 解除关联
export const remove_link = (data) => {
  return ReqClient('/api/product/link/remove', 'POST', {
    ...data
  })
}

// 更新价格调整值
export const update_link = (data) => {
  return ReqClient('/api/product/link/price/change/update', 'POST', {
    ...data
  })
}


// 更新关联推荐
export const recommend_update = (data) => {
  return ReqClient('/api/product/recommend/update', 'POST', {
    ...data
  })
}

// 更新关联推荐列表
export const recommend_list = (data) => {
  return ReqClient('/api/product/recommend/list', 'POST', {
    ...data
  })
}

// 更新关联推荐列表
export const product_user_type = (data) => {
  return ReqClient('/api/product/user/type/update', 'POST', {
    ...data
  })
}

// 更新关联推荐列表
export const purchase_note_update = (data) => {
  return ReqClient('/api/product/purchase/note/update', 'POST', {
    ...data
  })
}
