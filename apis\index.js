import { ReqClient } from '../utils/request'

//待处理反馈
export const feedback_count = () => {
  return ReqClient('/api/addr/feedback/count','POST', {
  })
}

//售后审核数
export const refund_count = (data) => {
  return ReqClient('/api/admin/order/refund/audit/count','POST', {
    ...data
  })
}

//会员审核和地址审核总数
export const all_count = (data) => {
  return ReqClient('/api/admin/buyer/count/audit','POST', {
    ...data
  })
}

//会员审核和地址审核总数
export const product_count = (data) => {
  return ReqClient('/api/product/count/audit','POST', {
    ...data
  })
}
// 积分订单数
export const integral_num = () => {
  return ReqClient('/api/integral/order/count/ship','POST', {})
}

// 品控中心 待确认数量
export const count_not_confirm = (data) => {
  return ReqClient('/api/order/count/not/confirm', 'POST', {
      ...data
  })
}

// 发票开具
export const invoice_count = () => {
  return ReqClient('/api/invoice/status/count','POST', {
  })
}

// 是否为配送员
export const is_delivery_man = (data) => {
  return ReqClient('/api/service/point/delivery/man/get/by/user', 'POST', {
      ...data
  })
}

export const getManager   = (data) => {
  return ReqClient('/api/buyer/manager/get/by/user', 'POST', {
      ...data
  })
}


// 站点
export const station_user = (data) => {
  return ReqClient('/api/station/get/by/user', 'POST', {
      ...data
  })
}

// 站点会员
export const station_buyer = (data) => {
  return ReqClient('/api/buyer/list/by/station', 'POST', {
      ...data
  })
}

// 站点订单
export const list_order_station = (data) => {
  return ReqClient('/api/order/list/by/station', 'POST', {
      ...data
  })
}

// 站点管理余额
export const  station_balance= (data) => {
  return ReqClient('/api/pay/account/user/balance/by/station', 'POST', {
      ...data
  })
}

// 站点支付信息
export const  station_authentication= (data) => {
  return ReqClient('/api/authentication/get/by/station', 'POST', {
      ...data
  })
}

// 站点提现
export const  station_withdraw= (data) => {
  return ReqClient('/api/withdraw/apply/list/by/station', 'POST', {
      ...data
  })
}

// 交付中心警告
export const  serviceWarning= (data) => {
  return ReqClient('/api/order/service/point/warning/center', 'POST', {
      ...data
  })
}


// 交付中心警告
export const product_offline = (data) => {
  return ReqClient('/api/product/list/offline', 'POST', {
      ...data
  })
}