import {
  ReqClient
} from '../utils/request'

//客户经理新建
export const manager_create = (data) => {
  return ReqClient('/api/buyer/manager/create', 'POST', {
    ...data
  })
}


//客户经理列表
export const manager_list = (data) => {
  return ReqClient('/api/buyer/manager/list', 'POST', {
    ...data
  })
}


//客户经理删除
export const manager_delete = (data) => {
  return ReqClient('/api/buyer/manager/delete', 'POST', {
    ...data
  })
}

//会员关联列表
export const manager_link_list = (data) => {
  return ReqClient('/api/buyer/manager/link/list/by/user', 'POST', {
    ...data
  })
}

//模糊搜索
export const manager_link_search = (data) => {
  return ReqClient('/api/buyer/search', 'POST', {
    ...data
  })
}

//模糊搜索
export const manager_link_create = (data) => {
  return ReqClient('/api/buyer/manager/link/create', 'POST', {
    ...data
  })
}

//解绑
export const manager_link_delete = (data) => {
  return ReqClient('/api/buyer/manager/link/delete', 'POST', {
    ...data
  })
}

//绑定会员订单列表
export const manager_buyer_list = (data) => {
  return ReqClient('/api/order/list/buyer', 'POST', {
    ...data
  })
}