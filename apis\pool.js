import { ReqClient } from '../utils/request'


//分类列表——一级
export const class_list = (data) => {
  return ReqClient('/api/category/list/first/2','GET',)
}

//一级分类
export const next_class = (data) => {
  return ReqClient(`/api/category/next/${data}/2`,'GET',)
}


//商品列表
export const goods_list = (data) => {
  return ReqClient("/api/product/list/category", 'POST', {
    ...data
  })
}


export const fruit_grade = (data) => {
  return ReqClient("/api/product/fruit/class/list/category", 'POST', {
    ...data
  })
}