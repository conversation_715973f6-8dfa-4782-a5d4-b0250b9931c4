import { ReqClient } from '../utils/request'
//商品库
export const product_list_external = (data) => {
  return ReqClient('/api/product/list/external/sale', 'POST', {
      ...data
  })
}

//商品分类标签
export const category_list_id = (data) => {
  return ReqClient('/api/category/second/list/by/id', 'POST', {
      ...data
  })
}

//已关联商品
export const product_link = () => {
  return ReqClient('/api/product/link/list/has', 'POST', {
  })
}

// 购物车更新
export const cart_update = (data) => {
  return ReqClient('/api/purchase/cart/update', 'POST', {
    ...data
  })
}

// 购物车更新
export const cart_list = (data) => {
  return ReqClient('/api/purchase/cart/list', 'POST', {
    ...data
  })
}

// 计算价格和数量
export const order_calc = (data) => {
  return ReqClient('/api/purchase/order/calc', 'POST', {
    ...data
  })
}

// 创建
export const purchase_create = (data) => {
  return ReqClient('/api/purchase/order/create', 'POST', {
      ...data
  })
}