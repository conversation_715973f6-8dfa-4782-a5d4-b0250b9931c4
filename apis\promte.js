import { ReqClient } from '../utils/request'

// 创建
export const promte_create = (data) => {
  return ReqClient('/api/promote/new/create', 'POST', {
      ...data
  })
}

// 列表
export const promte_list = (data) => {
  return ReqClient('/api/promote/new/list/by/point', 'POST', {
      ...data
  })
}

// 删除
export const promte_delete = (data) => {
  return ReqClient('/api/promote/new/delete', 'POST', {
      ...data
  })
}