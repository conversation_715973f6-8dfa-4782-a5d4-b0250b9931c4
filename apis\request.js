import { baseUrl } from './http.js'

module.exports = {
  request : function(url, method, data){
    if (method === 'GET') {
      var header = {
        'content-type': "application/x-www-form-urlencoded",
        "X-Env": "2",
        "Authorization":wx.getStorageSync('token')
      }
    } else if (method === 'POST') {
      var header = {
        'content-type': 'application/json',
        "X-Env": "2",
        "Authorization":wx.getStorageSync('token')
      }
    }else{
      
    }
    let fullUrl = `${baseUrl}${url}`
    // let token = wx.getStorageSync('token') ? wx.getStorageSync('token')  : ''
    wx.showLoading({ title: "数据请求中"  });
    return new Promise((resolve,reject)=>{
      wx.request({
        url: fullUrl,
        method: method,
        data,
        header: header,
        success(res){
          console.log(res.data.status)
          if (res.data.code === 0) {
            resolve(res.data)
            wx.hideLoading()
          }else{
            wx.hideLoading()
            wx.showToast({
              title: res.data.msg,
              icon:'none'
            })
            reject(res.data.message)
          }
        },
        fail(){
          wx.showToast({
            title: '接口请求错误',
            icon:'none'
          })
          reject('接口请求错误')
        }
      })
    })
  }
}