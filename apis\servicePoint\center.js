import { ReqClient } from '../../utils/request'

// 查询服务仓
export const get_service_point_detail = (data) => {
  return ReqClient('/api/service/point/get/by/user', 'POST', {
    ...data
  })
}

//用户余额
export const user_balance = (data) => {
  return ReqClient('/api/pay/account/user/balance', 'POST', {
    ...data
  })
}

// 账户-查询-中心仓
export const merchant_balance = (data) => {
  return ReqClient('/api/yee/merchant/balance/query/by/service/point', 'POST', {
    ...data
  })
}

// 账户-查询-供应商
export const merchant_balance_supplier = (data) => {
  return ReqClient('/api/yee/merchant/balance/query/by/supplier', 'POST', {
    ...data
  })
}

//待结算金额
export const pay_not_amount = (data) => {
  return ReqClient('/api/order/agent/pay/not/amount', 'POST', {
    ...data
  })
}

//待结算列表
export const pay_not_amount_list = (data) => {
  return ReqClient('/api/order/agent/pay/not/list', 'POST', {
    ...data
  })
}

//提现申请
export const withdraw_apply = (data) => {
  return ReqClient('/api/withdraw/apply', 'POST', {
    ...data
  })
}

// 提现供应商
export const withdraw_by_supplier = (data) => {
  return ReqClient('/api/yee/merchant/account/withdraw/by/supplier', 'POST', {
    ...data
  })
}

// 提现中心仓
export const withdraw_by_point = (data) => {
  return ReqClient('/api/yee/merchant/account/withdraw/by/point', 'POST', {
    ...data
  })
}

// 支付绑定-发送验证码
export const send_code = (data) => {
  return ReqClient('/api/captcha/send/pay/bind/mobile', 'POST', {
    ...data
  })
}

// 重置密码
export const reset_pwd = (data) => {
  return ReqClient('/api/user/pwd/reset', 'POST', {
    ...data
  })
}

// 支付绑定手机号---采购商
export const bind_phone = (data) => {
  return ReqClient('/api/authentication/bind/pay/phone', 'POST', {
    ...data
  })
}


// 提现协议前端回调
export const back_url = (data) => {
  return ReqClient('/api/sys/sign/back/url', 'POST', {
    ...data
  })
}


// 数据统计
export const bill_stats = (data) => {
  return ReqClient('/api/order/agent/pay/service/point/bill/stats', 'POST', {
    ...data
  })
}

// 提现卡查询供应商
export const merchant_Withdrawal = (data) => {
  return ReqClient('/api/yee/merchant/account/withdraw/card/query/by/supplier', 'POST', {
    ...data
  })
}


// 提现卡查询中心仓
export const merchant_Withdrawal_point = (data) => {
  return ReqClient('/api/yee/merchant/account/withdraw/card/query/by/point', 'POST', {
    ...data
  })
}
// 供应商入网信息
export const supplier_network_info = (data) => {
  return ReqClient('/api/yee/merchant/get/by/supplier', 'POST', {
    ...data
  })
}

// 
export const withdraw_bind = (data) => {
  return ReqClient('/api/yee/merchant/account/withdraw/card/bind/by/supplier', 'POST', {
    ...data
  })
}


export const point_order_list = (data) => {
  return ReqClient('/api/order/service/point/list/order', 'POST', {
    ...data
  })
}


export const coupon_supplier = (data) => {
  return ReqClient('/api/coupon/stats/subsidy/by/supplier', 'POST', {
    ...data
  })
}


// 利润
export const profit_supplier = (data) => {
  return ReqClient('/api/order/final/settle/stats/monthly', 'POST', {
    ...data
  })
}

// 利润结算记录
export const profit_list = (data) => {
  return ReqClient('/api/order/final/settle/list/transfer/by/supplier', 'POST', {
    ...data
  })
}


// 订单调价记录
export const adjust_settle_list = (data) => {
  return ReqClient('/api/order/adjust/settle/list/by/supplier', 'POST', {
    ...data
  })
}

//订单调价查询
export const adjust_settle_get = (data) => {
  return ReqClient('/api/order/adjust/settle/get/by/order', 'POST', {
    ...data
  })
}

//配送费明细
export const deliver_detail = (data) => {
  return ReqClient('/api/deliver/fee/detail/stats', 'POST', {
    ...data
  })
}

//配送费明细记录
export const deliver_detail_monthly = (data) => {
  return ReqClient('/api/deliver/fee/detail/list/daily', 'POST', {
    ...data
  })
}
