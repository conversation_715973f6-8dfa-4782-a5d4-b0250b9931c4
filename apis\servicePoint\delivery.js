import { ReqClient } from '../../utils/request'

//配送列表
// export const delivery_list = (data) => {
//   return ReqClient('/api/order/service/point/arrive/to/deliver','POST', {
//     ...data
//   })
// }

// 配送-待分配列表
export const to_do_allot_list = (data) => {
  return ReqClient('/api/order/service/point/to/allot','POST', {
    ...data
  })
}

// 配送-采购商订单详情查询
export const buyer_order_detail = (data) => {
  return ReqClient('/api/order/service/point/list/buyer','POST', {
    ...data
  })
}


// 配送-分配
export const allot_list = (data) => {
  return ReqClient('/api/order/service/point/allot','POST', {
    ...data
  })
}

// 已分配列表
export const allot_order_list = (data) => {
  return ReqClient('/api/order/service/point/allot/list','POST', {
    ...data
  })
}

// 已分配列表
export const warn_receive = (data) => {
  return ReqClient('/api/order/service/point/warning/receive','POST', {
    ...data
  })
}

// 配送指派-码
export const deliver_assign_qr = (data) => {
  return ReqClient('/api/order/deliver/assign/qr/get','POST', {
    ...data
  })
}

//  配送列表
export const deliver_assign_list = (data) => {
  return ReqClient('/api/order/deliver/assign/buyer/list','POST', {
    ...data
  })
}

//  配送列表-订单
export const deliver_assign_list_order = (data) => {
  return ReqClient('/api/order/deliver/assign/order/list','POST', {
    ...data
  })
}

//  配送创建
export const deliver_assign_create = (data) => {
  return ReqClient('/api/order/deliver/assign/create','POST', {
    ...data
  })
}

//  配送-删除
export const deliver_assign_delete  = (data) => {
  return ReqClient('/api/order/deliver/assign/delete','POST', {
    ...data
  })
}


//确认到货
export const confirm_receive = (data) => {
  return ReqClient('/api/order/service/point/receive','POST', {
    ...data
  })
}

// 配送单
export const deliver_note_by_buyer = (data) => {
  return ReqClient('/api/order/deliver/note/get/by/buyer','POST', {
    ...data
  })
}


// 物流单上传
export const logistics_create = (data) => {
  return ReqClient('/api/order/service/point/logistics/create','POST', {
    ...data
  })
}
