import { ReqClient } from '../../utils/request'

// 配送员列表
export const delivery_man_list = (data) => {
  return ReqClient('/api/service/point/delivery/man/list','POST', {
    ...data
  })
}

// 配送员添加
export const delivery_man_create = (data) => {
  return ReqClient('/api/service/point/delivery/man/create','POST', {
    ...data
  })
}

// 配送员更新
export const delivery_man_update = (data) => {
  return ReqClient('/api/service/point/delivery/man/update','POST', {
    ...data
  })
}

// 配送员删除
export const delivery_man_delete = (data) => {
  return ReqClient('/api/service/point/delivery/man/delete','POST', {
    ...data
  })
}