import {
  ReqClient
} from '../../utils/request'

//  品控部分


//服务点详情
export const confirm_stats = (data) => {
  return ReqClient(`/api/order/service/point/confirm/stats`, 'POST', {
    ...data
  })
}
// 待确认订单
export const confirm_not = (data) => {
  return ReqClient(`/api/order/service/point/confirm/not`, 'POST', {
    ...data
  })
}
// 服务仓备注

export const point_note_update = (data) => {
  return ReqClient(`/api/order/service/point/note/update`, 'POST', {
    ...data
  })
}

// 地址详情
export const addr_detail = (data) => {
  return ReqClient("/api/user/addr/get", 'POST', {
    ...data
  })
}


// 结算价订单
export const order_ship = (data) => {
  return ReqClient("/api/order/ship/list/by/buyer", 'POST', {
    ...data
  })
}

// 结算价订单
export const order_ship_adjust = (data) => {
  return ReqClient("/api/order/ship/adjust", 'POST', {
    ...data
  })
}