import { ReqClient } from '../../utils/request'

//自提点
// export const take_delivery_list = (data) => {
//   return ReqClient('/api/order/service/point/arrive/to/self/get','POST', {
//     ...data
//   })
// }

//自提订单详情
export const take_delivery_Detail = (data) => {
  return ReqClient('/api/order/service/point/list/buyer/self/get','POST', {
    ...data
  })
}

// 自提列表
export const take_delivery_list = (data) => {
  return ReqClient('/api/order/service/point/list/self/get','POST', {
    ...data
  })
}



// 物流列表
export const logistics_list = (data) => {
  return ReqClient('/api/order/service/point/list/logistics','POST', {
    ...data
  })
}

//  物流订单详情
export const logistics_Detail = (data) => {
  return ReqClient('/api/order/service/point/list/buyer/logistics','POST', {
    ...data
  })
}

//  即时配送列表
export const instant_list = (data) => {
  return ReqClient('/api/order/service/point/list/instant','POST', {
    ...data
  })
}

//  即时配送详情
export const instant_detail = (data) => {
  return ReqClient('/api/order/service/point/list/buyer/instant','POST', {
    ...data
  })
}