import { ReqClient } from '../../utils/request'

//备货列表
export const stock_up_list = (data) => {
  return ReqClient('/api/order/stock/up/list','POST', {
    ...data
  })
}
//查询供应商
export const supplier_search = (userId) => {
  return ReqClient(`/api/supplier/user/${userId}`,'GET',{
    
  } )
}

// 更新上一条接口地址
export const get_supplier_search = (data) => {
  return ReqClient(`/api/supplier/get/user`, 'POST', {
    ...data
  })
}

// 更新头像
export const update_avatar = (data) => {
  return ReqClient('/api/supplier/update/avatar','POST', {
    ...data
  })
}

// tabbar数据
export const tabbar_tip = (data) => {
  return ReqClient('/api/supplier/tab/bar/tip','POST', {
    ...data
  })
}


// 个人中心统计
export const supplierStats = (data) => {
  return ReqClient('/api/supplier/stats/for/user','POST', {
    ...data
  })
}
