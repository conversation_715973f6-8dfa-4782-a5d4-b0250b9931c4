import { ReqClient } from '../../utils/request'

// 审核列表
export const list = (data) => {
  return ReqClient('/api/comment/list/supplier','POST', {
    ...data
  })
}

// 审核
export const reason = (data) => {
  return ReqClient('/api/comment/audit','POST', {
    ...data
  })
}

// 回复
export const reply = (data) => {
  return ReqClient('/api/comment/reply','POST', {
    ...data
  })
}

// 售后列表
export const after_sale_list = (data) => {
  return ReqClient('/api/order/after/sale/list/supplier','POST', {
    ...data
  })
}




