import { ReqClient } from '../../utils/request'

// //分类列表——一级
// export const category_list_main = (data) => {
//   return ReqClient('/api/category/list/first/','GET', {
//     ...data
//   })
// }

//删除审核不通过
export const audit_delete = (data) => {
  return ReqClient(`/api/product/audit/delete`,'POST', {
    ...data
  })
}

//供应商商品列表
export const supplier_product = (data) => {
  return ReqClient('/api/product/list/supplier','POST', {
    ...data
  })
}

//供应商商品列表
export const category_used_supplier = (data) => {
  return ReqClient('/api/product/category/used/supplier','POST', {
    ...data
  })
}

// 搜索产品
export const search_product = (data) => {
  return ReqClient('/api/product/list/search/supplier','POST', {
    ...data
  })
}

// 审核中的产品

export const product_audit = (data) => {
  return ReqClient('/api/product/audit/supplier/list','POST', {
    ...data
  })
}

// 商品数
export const get_product_num = (data) => {
  return ReqClient('/api/product/num/by/supplier','POST', {
    ...data
  })
}

//新增商品
export const add_product = (data) => {
  return ReqClient('/api/product/','POST', {
    ...data
  })
}

//修改商品
export const updata_product = (data) => {
  return ReqClient('/api/product/update/','POST', {
    ...data
  })
}

//库存修改
export const stock_updata_product = (data) => {
  return ReqClient('/api/product/stock/update','POST', {
    ...data
  })
}

// 更新价格-单一
export const updata_price_single = (data) => {
  return ReqClient('/api/product/price/single/update','POST', {
    ...data
  })
}

// 更新标题
export const update_title = (data) => {
  return ReqClient('/api/product/title/update','POST', {
    ...data
  })
}

// 更新描述
export const update_desc = (data) => {
  return ReqClient('/api/product/desc/update','POST', {
    ...data
  })
}

//审核列表
export const audit_product = (data) => {
  return ReqClient('/api/product/list/supplier/audit','POST', {
    ...data
  })
}


//商品上架下架
export const sale_product = (data) => {
  return ReqClient('/api/product/sale/update','POST', {
    ...data
  })
}

export const updateProductLimit = (data) => {
  return ReqClient('/api/product/buy/limit/update','POST', {
    ...data
  })
}

//商品删除
export const delete_product = (data) => {
  return ReqClient('/api/product/delete','POST', {
    ...data
  })
}

//检查是否存在本产品待审核信息
export const check_audit = (data) => {
  return ReqClient('/api/product/update/check/audit','POST', {
    ...data
  })
}

// 加入专区申请
export const part_product_apply = (data) => {
  return ReqClient('/api/index/part/product/apply','POST', {
    ...data
  })
}


// 加入专区申请
export const part_product_apply_check = (data) => {
  return ReqClient('/api/index/part/product/apply/check','POST', {
    ...data
  })
}

// 低库存数量
export const low_product_num = (data) => {
  return ReqClient('/api/product/low/stock/count/supplier','POST', {
    ...data
  })
}

// 低库存商品查询
export const low_product_list = (data) => {
  return ReqClient('/api/product/low/stock/list/supplier','POST', {
    ...data
  })
}


// 低库存商品查询
export const product_update = (data) => {
  return ReqClient('/api/product/list/to/update/supplier','POST', {
    ...data
  })
}
