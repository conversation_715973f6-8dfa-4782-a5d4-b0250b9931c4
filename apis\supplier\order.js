import { ReqClient } from '../../utils/request'

//备货列表
export const supplier_order = (data) => {
  return ReqClient('/api/order/list/supplier','POST', {
    ...data
  })
}

// 订单详情
export const get_order = (data) => {
  return ReqClient('/api/order/get','POST', {
    ...data
  })
}

// 售后退款详情
export const refund_detail_for_after_sale = (data) => {
  return ReqClient('/api/order/refund/after/sale/for/product', 'POST', {
    ...data
  })
}

// 分拣退款-详情
export const sort_refund_detail = (data) => {
  return ReqClient('/api/order/refund/ship/for/product', 'POST', {
    ...data
  })
}
//查询补差订单
export const serch_debt_order = (data) => {
  return ReqClient('/api/order/debt/get', 'POST', {
    ...data
  })
}
// 分账
export const agent_pay_list = (data) => {
  return ReqClient('/api/order/agent/pay/list/by/order', 'POST', {
    ...data
  })
}

// 分拣售后退款信息
export const refund_alls = (data) => {
  return ReqClient('/api/order/refund/list/all', 'POST', {
      ...data
  })
}

// 分拣售后退款信息
export const refund_after_sale_by_order = (data) => {
  return ReqClient('/api/order/refund/after/sale/for/order', 'POST', {
      ...data
  })
}