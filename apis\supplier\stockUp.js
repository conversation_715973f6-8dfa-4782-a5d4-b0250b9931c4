import { ReqClient } from '../../utils/request'

//备货列表-实际未备货
export const stock_up_list = (data) => {
  return ReqClient('/api/order/stock/up/list','POST', {
    ...data
  })
}

//备货列表-实际已备货
export const stock_up_list_has = (data) => {
  return ReqClient('/api/order/stock/up/list/has','POST', {
    ...data
  })
}
// 未备货订单列表
export const no_stock_up_list = (data) => {
  return ReqClient('/api/order/stock/up/to/do/list','POST', {
    ...data
  })
}

// 将订单加入备货组
export const add_stock_up = (data) => {
  return ReqClient('/api/order/stock/up/order/add','POST', {
    ...data
  })
}

// 备货更新
export const update_stock_up = (data) => {
  return ReqClient('/api/order/stock/up/update','POST', {
    ...data
  })
}

//查询-根据集中仓
export const service_point_list = (data) => {
  return ReqClient('/api/route/list/by/warehouse','POST', {
    ...data
  })
}

//查询-根据集中仓
export const stock_up_doing = (data) => {
  return ReqClient('/api/order/doing/list/supplier','POST', {
    ...data
  })
}
