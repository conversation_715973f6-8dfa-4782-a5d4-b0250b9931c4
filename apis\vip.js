
import { ReqClient } from '../utils/request'

// 审核列表
export const buyer_list = (data) => {
  return ReqClient('/api/admin/buyer/list', 'POST', {
      ...data
  })
}

// 查询详情
export const getBuyer = (id) => {
  return ReqClient(`/api/buyer/${id}`,'GET',)
}

// 识别回填
export const ocrLicense = (data) => {
  return ReqClient('/api/sys/ocr/do', 'POST', {
      ...data
  })
}

// 保存
export const updateLicenseStatus = (data) => {
  return ReqClient('/api/buyer/license/status/update', 'POST', {
      ...data
  })
}

// 维护人
export const link_list = (data) => {
  return ReqClient('/api/buyer/link/user/list', 'POST', {
      ...data
  })
}

// 提交
export const auditBuyer = (data) => {
  return ReqClient('/api/admin/buyer/audit', 'POST', {
      ...data
  })
}

// 手机号
export const getUserPhone = (data) => {
  return ReqClient('/api/user/get', 'POST', {
      ...data
  })
}

//售后审核列表
export const order_refund_list = (data) => {
  return ReqClient('/api/admin/order/refund/list', 'POST', {
      ...data
  })
}
//售后审核
export const refund_audit = (data) => {
  return ReqClient('/api/admin/order/refund/audit', 'POST', {
      ...data
  })
}

//积分订单
export const integral_order = (data) => {
  return ReqClient('/api/integral/order/list/by/web', 'POST', {
      ...data
  })
}

// 取消积分订单列表
export const integral_order_cancel = (data) => {
  return ReqClient('/api/integral/order/cancel', 'POST', {
      ...data
  })
}

// 发货积分订单列表
export const integral_order_ship = (data) => {
  return ReqClient('/api/integral/order/ship', 'POST', {
      ...data
  })
}

// 商品审核列表
export const product_audit_list = (data) => {
  return ReqClient('/api/product/audit/list', 'POST', {
      ...data
  })
}

// 下架审核列表
export const product_list_offline = (data) => {
  return ReqClient('/api/product/list/offline/audit', 'POST')
}

// 商品审核
export const product_audit = (data) => {
  return ReqClient('/api/admin/product/audit', 'POST', {
      ...data
  })
}

// 下架审核
export const product_audit_offline = (data) => {
  return ReqClient('/api/product/offline/audit', 'POST', {
      ...data
  })
}


// 标签
export const tag_list = () => {
  return ReqClient('/api/product/tag/list/avilable', 'POST')
}


