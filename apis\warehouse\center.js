
import { ReqClient } from '../../utils/request'
//服务仓详情
export const warehouse_detail = (data) => {
  return ReqClient('/api/warehouse/get/user','POST', {
    ...data
  })
}

//用户余额
export const user_balance = (data) => {
  return ReqClient('/api/pay/account/user/balance','POST', {
    ...data
  })
}




//提现协议-获取url
export const withdraw_protocol = (data) => {
  return ReqClient('/api/authentication/sign/acct/protocol','POST', {
    ...data
  })
}

//提现页面信息
export const withdraw_account = (data) => {
  return ReqClient('/api/withdraw/account/info','POST', {
    ...data
  })
}

//提现页面信息
export const withdraw_list = (data) => {
  return ReqClient('/api/withdraw/apply/list','POST', {
    ...data
  })
}
