import { ReqClient } from '../../utils/request'


//  待品控
export const quality_list_temp = (data) => {
  return ReqClient('/api/order/quality/list/temp','POST', {
    ...data
  })
}


//品控
export const quality_updata_temp = (data) => {
  return ReqClient('/api/order/quality/update/temp','POST', {
    ...data
  })
}

//集中仓发货数据--整体
export const warehouse_total_statistics_temp = (data) => {
  return ReqClient('/api/stats/warehouse/ship/total/temp','POST', {
    ...data
  })
}

//集中仓发货数据--列表
export const warehouse_product_statistics_temp = (data) => {
  return ReqClient('/api/stats/warehouse/single/product/temp','POST', {
    ...data
  })
}

//当前待备货
export const pre_stock_up = (data) => {
  return ReqClient('/api/order/list/to/stock/up','POST', {
    ...data
  })
}
//执行备货
export const stock_up = (data) => {
  return ReqClient('/api/order/do/stock/up','POST', {
    ...data
  })
}


//执行备货
export const quality_search = (data) => {
  return ReqClient('/api/order/quality/list/search','POST', {
    ...data
  })
}



//执行备货
export const warning_ship = (data) => {
  return ReqClient('/api/order/warning/ship','POST', {
    ...data
  })
}

//采购总览
export const procurement_verview_stats = (data) => {
  return ReqClient('/api/order/buy/stats','POST', {
    ...data
  })
}


// 刷新采购价格
export const refresh_buy_stats = (data) => {
  return ReqClient('/api/order/buy/stats/refresh','POST', {
    ...data
  })
}


// 刷新采购价格
export const order_warning_weight = () => {
  return ReqClient('/api/order/warning/over/weight','POST', { })
}