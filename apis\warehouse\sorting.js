import { ReqClient } from '../../utils/request'
//分拣-列表
export const sorting_list = (data) => {
  return ReqClient('/api/order/sort/list','POST', {
    ...data
  })
}

export const sorting_list_temp = (data) => {
  return ReqClient('/api/order/sort/list/temp','POST', {
    ...data
  })
}

//分拣-列表
export const sorting_list_has = (data) => {
  return ReqClient('/api/order/sort/list/has','POST', {
    ...data
  })
}


//分拣-执行前查询信息
export const sorting_order_query = (data) => {
  return ReqClient('/api/order/sort/query','POST', {
    ...data
  })
}

export const sorting_order_query_temp = (data) => {
  return ReqClient('/api/order/sort/list/order/temp','POST', {
    ...data
  })
}


//分拣-执行
export const sorting_updata = (data) => {
  return ReqClient('/api/order/sort/update','POST', {
    ...data
  })
}

export const sorting_updata_temp = (data) => {
  return ReqClient('/api/order/sort/update/temp','POST', {
    ...data
  })
}

export const sorting_updata_photo = (data) => {
  return ReqClient('/api/order/sort/update/photo','POST', {
    ...data
  })
}

//分拣-查询产品订单列表
export const search_order_list = (data) => {
  return ReqClient('/api/order/sort/list/order','POST', {
    ...data
  })
}
// 分拣-查询产品的备货和品控信息
export const search_stock_up_info = (data) => {
  return ReqClient('/api/order/stock/up/get','POST', {
    ...data
  })
}

// 订单地址查询
export const order_address = (data) => {
  return ReqClient('/api/order/address/get','POST', {
    ...data
  })
}


// 订单地址查询
export const sort_search = (data) => {
  return ReqClient('/api/order/sort/list/search','POST', {
    ...data
  })
}