// app.js
const util = require('./utils/util');
import dayjs from './libs/dayjs';
import {
  refreshTokenKey,
  expiresTokenTimeKey,
  tokenKey,
  userIDKey,
} from './utils/dict';

App({
  onLaunch(options) {
    if (options.scene == 1007 || options.scene == 1008) {
      this.globalData.share = true
    } else {
      this.globalData.share = false
    };
 
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          wx.showLoading({
            title: '新版本下载中....',
          })
        }
      });
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          showCancel: false,
          confirmText: '马上重启',
          content: '新版本已经上线，需要您重启小程序以应用新版本。',
          success: function (res) {
            if (res.confirm) {
              // 调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate();
            }
          }
        });
      });
      updateManager.onUpdateFailed(function () {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请删除当前小程序，重新搜索打开。',
        });
      });

    } else { // 如果用户微信版本过低，则弹出提示框
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
      });

    }

    if (wx.getStorageSync(tokenKey)) {
      const newDatas = new Date().valueOf()
      const difference = (wx.getStorageSync(expiresTokenTimeKey) - newDatas) / 1000 //过期时间差值
      if (difference > 0 && difference < 7200 * 10 * 2) {
        //  20*2小时替换
        let refreshKey = wx.getStorageSync(refreshTokenKey)
        if (refreshKey) {
          // console.log("refreshKey", refreshKey, 999);
          wx.request({
            url: this.globalData.url + '/api/user/refresh/token',
            method: "post",
            header: {
              "X-Env": this.globalData.env,
              'Authorization': wx.getStorageSync(tokenKey),
              "X-Request-Id": wx.getStorageSync(userIDKey) + util.uuid2(20),
            },
            data: {
              refresh_token: refreshKey,
            },
            success: (res) => {
              // console.log(res)
              if (res.data.code == 0) {
                const i = res.data.data
                wx.setStorageSync(tokenKey, i.access_token)
                wx.setStorageSync(expiresTokenTimeKey, i.expires)
                wx.setStorageSync(refreshTokenKey, i.refresh_token)
              } else {
                //  清空授权信息
                wx.clearStorageSync()
              }
            },
            fail: (res) => {
              Toast.fail("请求异常，稍后重试")
            }
          })
        } else {
          wx.clearStorageSync()
        }
      } else if (difference < 0) {
        wx.clearStorageSync()
      }
    }

    wx.getSystemInfo({
      success: res => {
        // const systemInfo = wx.getSystemInfoSync();
        let menuButtonObject = {}
        if (res.platform === 'ios') {
          // ios设备的胶囊按钮都是固定的
          menuButtonObject = {
            width: 87,
            height: 32,
            left: res.screenWidth - 7 - 87,
            right: res.screenWidth - 7,
            top: res.statusBarHeight + 4,
            bottom: res.statusBarHeight + 4 + 32
          }
        } else {
          // 安卓通过api获取
          menuButtonObject = wx.getMenuButtonBoundingClientRect()
        }
        
        this.globalData.safeArea =res.safeArea
        this.globalData.phoneParam =res

        this.globalData.navHeight = (menuButtonObject.height + menuButtonObject.top + menuButtonObject.bottom) / 2

        this.globalData.navBarHeight = (menuButtonObject.top - res.statusBarHeight) * 2 + menuButtonObject.height + res.statusBarHeight;
        this.globalData.menuBottom = menuButtonObject.top - res.statusBarHeight;
        this.globalData.menuHeight = menuButtonObject.height;

      }
    }) 
  },

  onShow(options) {
    console.log(options)
    if (options.referrerInfo && options.referrerInfo.extraData) {
      this.globalData.backMerchantUrl = options.referrerInfo.extraData.backMerchantUrl;
      // wx.navigateTo({
      //   url: '/pages/商户自己的 webview 页面/商户自己的 webview 页面'
      // });
      wx.request({
        url: this.globalData.url + '/api/sys/sign/back/url',
        method: "post",
        header: {
          "X-Env": this.globalData.env,
          'Authorization': wx.getStorageSync(tokenKey),
          "X-Request-Id": wx.getStorageSync(userIDKey) + util.uuid2(20),
        },
        data: {
          url: options.referrerInfo.extraData.backMerchantUrl,
        },
        success: (res) => {
          // console.log(res)
          if (res.data.code == 0) {

          }
        },
        fail: (res) => {
          Toast.fail("请求异常，稍后重试")
        }
      })
    }

  },


  globalData: {
    share: false, // 分享默认为false
    backMerchantUrl: '',
    navHeight: "", //导航栏总高度,
    navBarHeight: 0,
    menuHeight: 0,
    menuBottom: 0,
    safeArea: {},
    phoneParam:{},
    currentUser:null, // 当前用户
    navigationBarHeight: "",
    quality_time:0, // 品控时间
    delivery_time:0, // 品控时间
    url: "http://127.0.0.1:11001",
    // url: "http://192.168.10.179:11001",
    // url: "http://192.168.3.68:11001",
    // url: "http://192.168.1.6:11001",
    // url: "https://api.guoshut.com",
    imageUrl: "https://image.guoshut.com/",
    env: "2"
  },
})