// component/decimal-input/index.js
const util = require('../../utils/util');
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    value:{
      type:Number,
      value:0,
    },
    width:{
      type:Number,
      value:120
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    input(e){
        this.setData({
          value:util.clearNumOne(e.detail.value)
        })

      this.triggerEvent("input",this.data.value)
    }
  }
})
