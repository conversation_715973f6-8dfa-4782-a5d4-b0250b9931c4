// component/file-stepper/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    labelWidth:{
      type:String,
      value:"240"
    },
    label:{
      type:String,
      value:""
    },
    disabled:{
      type:Boolean,
      value:false
    },

    value:{
      type:String,
      value:""
    },

    decimalLength:{
      type:Number,
      value:0
    },
    step:{
      type:Number,
      value:1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onChange(e){
      this.triggerEvent("change",e.detail)
    }
  }
})
