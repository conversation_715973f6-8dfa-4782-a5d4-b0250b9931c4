<!--component/file-stepper/index.wxml-->
<view class="file_stepper_wrap">
  <view class="filed_label" style="width: {{labelWidth}}rpx;">
    {{label}}
  </view>

  <view class="filed_input" style="width: calc(100% - {{labelWidth}}rpx);">
       <van-stepper value="{{value}}" min="0" input-width="50" disabled="{{disabled}}"   step="{{step}}" decimal-length="{{ decimalLength }}" bind:change="onChange" />
  </view>
</view>