// component/filed-input/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
      labelWidth:{
        type:String,
        value:"240"
      },
      label:{
        type:String,
        value:""
      },
      isIcon:{
        type:Boolean,
        value:false
      },
      type:{
        type:String,
        value:"text"
      },

      disabled:{
        type:Boolean,
        value:false
      },
      value:{
        type:String,
        value:""
      },
      maxlength:{
        type:Number,
        value:-1
      },
      placeholder:{
        type:String,
        value:""
      },
      tips:{
        type:String,
        value:"",
      },

      input_tips:{
        type:String,
        value:"",
      },

      inputs:{
        type:Boolean,
        value:true
      }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    input(e){
      this.triggerEvent("input",e.detail.value)
    }
  }
})
