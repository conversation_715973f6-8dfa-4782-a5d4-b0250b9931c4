<!--component/filed-input/index.wxml-->
<view class="filed_input_wrap">
  <view class="filed_label" style="width: {{labelWidth}}rpx;">
    <view>{{label}}</view>
    <view class="two">{{tips}}</view>
  </view>
  <view class="filed_input" style="width: calc(100% - {{labelWidth}}rpx);">
    <view class="filed_inputs">
      <input type="{{type}}" wx:if="{{inputs}}" value="{{value}}" maxlength="{{maxlength}}" placeholder-class="placeholder_style" placeholder="{{placeholder}}" disabled="{{disabled}}" bindinput="input" />
      <textarea wx:else bindblur="bindTextAreaBlur" value="{{value}}" maxlength="{{maxlength}}" auto-height placeholder="{{placeholder}}" disabled="{{disabled}}" bindinput="input" />
      <van-icon name="arrow" wx:if="{{isIcon}}" />
    </view>
    <view class="input_tips">
      {{input_tips}}
    </view>
  </view>
</view>