<view class='nav-wrap' style='height: {{height*2 + 20}}px;'>
  <!-- // 导航栏 中间的标题 -->
  <view class='nav-title' style='line-height: {{height*2 + 44}}px;'>{{navbarData.title}}</view>
  <view style='display: flex; justify-content: space-around;flex-direction: column'>
    <view class='nav-capsule' style='height: {{height*2 + 44}}px;' wx:if='{{navbarData.showCapsule}}'>
      <view bindtap='_navback' wx:if='{{!share}}'>
        <van-icon name="arrow-left" />
      </view>
      <view bindtap='_backhome' wx:if='{{share}}'>
        <van-icon name="wap-home-o" />
      </view>
    </view>
  </view>
</view>