/* 顶部要固定定位   标题要居中   自定义按钮和标题要和右边微信原生的胶囊上下对齐 */
.nav-wrap {
  position: fixed;
  width: 100%;
  top: 0;
  /* background: #fff; */
  color: #000;
  z-index: 9999999;
}

/* 标题要居中 */
.nav-title {
  position: absolute;
  text-align: center;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  font-size: 36rpx;
  color: black;
  font-weight: 600;
}

.nav-capsule {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  width: 140rpx;
  justify-content: space-between;
  height: 100%;
}

.navbar-v-line {
  width: 1px;
  height: 32rpx;
  /* background-color: #e5e5e5; */
}

.back-pre,
.back-home {
  width: 32rpx;
  height: 36rpx;
  margin-top: 4rpx;
  padding: 10rpx;
}

.nav-capsule .back-home {
  width: 36rpx;
  height: 40rpx;
  margin-top: 3rpx;
}