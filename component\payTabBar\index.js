import dayjs from '../../libs/dayjs';
import {
  warn_receive,
} from '../../apis/servicePoint/delivery';

import {
  servicePointIDKey,
} from '../../utils/dict';

Component({

  properties: {
    active: {
      type: Number,
      value: 0
    },
  },

  lifetimes: {
    ready() {
      this.queryReceiveWarn()
    },

  },

  data: {
    deliver_num: 0,
    self_get_num: 0,
    logistics_num: 0,
    instant_num: 0,
    service_point_id: ''
  },

  methods: {
    switchTab(event) {
      // this.setData({
      //   active: event.detail,
      // });
      let active = event.detail

      if (active == 0) {
        wx.redirectTo({
          url: '/pages/servicePoint/selfGet/index',
        })
        return
      }
      if (active == 1) {
        wx.redirectTo({
          url: '/pages/servicePoint/instantDeliver/index',
        })
        return
      }
      if (active == 2) {
        wx.redirectTo({
          url: '/pages/servicePoint/deliver/index',
        })
        return
      }
      if (active == 3) {
        wx.redirectTo({
          url: '/pages/servicePoint/logistics/index',
        })
        return
      }

    },
    queryReceiveWarn() {
      return new Promise(async (callback) => {
        let point_id = wx.getStorageSync(servicePointIDKey)
        this.setData({
          service_point_id: point_id,
        })
        await this.requestWarnReceive(1)
        await this.requestWarnReceive(3)
        await this.requestWarnReceive(2)
        await this.requestWarnReceive(4)
        callback()
      })
    },

    requestWarnReceive(deliver_type) {
      return new Promise((callback) => {
        let now = dayjs()
        let nowStamp = now.valueOf()
        let data = {
          timestamp: nowStamp,
          deliver_type: deliver_type,
          service_point_id: this.data.service_point_id,
        }
        warn_receive(data).then(res => {
          if (res.data.code == 0) {
            let num = res.data.data
            switch (deliver_type) {
              case 1:
                this.setData({
                  deliver_num: num
                })
                break
              case 2:
                this.setData({
                  self_get_num: num
                })
                break
              case 3:
                this.setData({
                  logistics_num: num
                })
                break
              case 4:
                this.setData({
                  instant_num: num
                })
                break
            }
          }
        }).finally(() => {
          callback()
        })
      })
    },
  }
})