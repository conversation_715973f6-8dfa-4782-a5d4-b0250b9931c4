// component/product_price_param/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    priceList:{
      type:Array,
      value:[],
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    preNum:1,
    priceListPrice:0,
    priceListNum:1,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    priceListPrice(e) {
      this.setData({
        priceListPrice: e.detail
      })
    },

    priceListNum(e) {
      console.log(e)
      this.setData({
        priceListNum: e.detail
      })
      // wx.setStorageSync('preNum',e.detail.value)
    },
    
    addPriceList() {
      let that = this
      if (that.data.priceListPrice && that.data.priceListNum) {
          if(that.data.priceList.length>0){
            if((that.data.priceList[that.data.priceList.length-1].num<that.data.priceListNum)&&(that.data.priceList[that.data.priceList.length-1].price>that.data.priceListPrice)){
                that.addData()
            }else{
                wx.showToast({
                  title: '请输入正确内容',
                  icon:"none"
                })
            }
          }else{
            that.addData()

            this.triggerEvent("addPriceList",that.data.priceList)
          }
      }
    },


    addData(){
      let that = this
      let data = {
        price: parseInt(that.data.priceListPrice),
        num: that.data.priceListNum
      }
      that.data.priceList.push(data)
      that.setData({
        priceList: that.data.priceList,
        priceListPrice: "",
        priceListNum: ""
      })
    },

    delect(e){
      let index = e.currentTarget.dataset.index
      this.data.priceList.splice(index, 1);
      this.setData({
        priceList:this.data.priceList
      })
    },
  }
})