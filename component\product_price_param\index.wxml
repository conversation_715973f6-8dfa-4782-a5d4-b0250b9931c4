<!--component/product_price_param/index.wxml-->
<view class="container">
  <view wx:for="{{priceList}}" wx:key="key">
    <view class="form_list">
      <view class="form_list">
        <view class="form_label">价格:{{item.price}}</view>
        <view class="">数量:{{priceList[index -1].num==undefined?"1":priceList[index -1].num}}~{{item.num}}</view>
        <view style="margin-left: 60rpx;">
        <van-icon name="delete-o" data-index="{{index}}" bind:click="delect" />
      </view>
      </view>
    </view>

  </view>
  <view class="form_list">
    <view class="form_label">价格</view>
    <view class="add_info_form">
      <!-- <view> -->
        <!-- <input type="number" value="{{priceListPrice}}"  bindinput="priceListPrice" placeholder="" /> -->
        <van-stepper value="{{ priceListPrice }}" min="0" decimal-length="{{ 2 }}" bind:change="priceListPrice" />
      <!-- </view> -->
    </view>
  </view>
  <view class="form_list">
    <view class="form_label">数量</view>
    <view class="add_info_form">
      <view>
        <!-- <input type="number" value="{{priceListNum}}" bindinput="priceListNum" placeholder="" /> -->
        <van-stepper value="{{ priceListNum }}" bind:change="priceListNum" />
      </view>
    </view>
  </view>
  <view class="addbaseInfo">
    <van-button type="primary" bind:click="addPriceList">添加价格信息</van-button>
  </view>
</view>