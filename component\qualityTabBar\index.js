import {
  tabbarStats,
} from '../../apis/warehouse/stockUp';
import dayjs from '../../libs/dayjs';
import Toast from '@vant/weapp/toast/toast';
import { servicePointIDKey,stationInfoKey } from '../../utils/dict';
const app = getApp()
Component({

  properties: {
    active: {
      type: Number,
      value: 0
    },
    level:{
      type: String,
      value: ''
    }
  },

  lifetimes: {
    ready() {
      this.getTabbarNum()
    },
  },

  data: {
    tabbarNum: {
      to_confirm_num: 0,
      to_quality_num: 0,
      to_ship_num: 0,
      to_sort_num: 0,
    },
  },

  methods: {
    switchTab(event) {
      let active = event.detail
      if (active == 0) {
        wx.redirectTo({
          url: '/pages/servicePoint/quality/confirm/index',
        })
        return
      }
      if (active == 1) {
        wx.redirectTo({
          url: '/pages/servicePoint/quality/checking/index',
        })
        return
      }
      if (active == 3) {
        wx.redirectTo({
          url: '/pages/servicePoint/quality/shipping/index',
        })
        return
      }
      if (active == 2) {
        wx.redirectTo({
          url: '/pages/servicePoint/quality/sorting/index',
        })
        return
      }
    },
    // 集中仓tabbar统计
    getTabbarNum() {
      let now = dayjs()
      let times = app.globalData.quality_time
      let param = {
        timestamp: times,
      }
  
      if (app.globalData.env == 3) {
        param.service_point_id = wx.getStorageSync(servicePointIDKey)
      }
  
      if (app.globalData.env == 7) {
        let station_info = wx.getStorageSync(stationInfoKey)
        param.service_point_id = station_info.service_point_id
        param.station_id = station_info.id
      }
  
      tabbarStats(param).then(res => {
        if (res.data.code === 0) {
          this.setData({
            tabbarNum: res.data.data
          })
        }
      }).catch(err => {
        Toast.fail(err.data.message)
      })
    },
  }
})