// component/radio/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    radioTitle:{
      type:String,
      value:"",
    },
    radioList:{
      type:Array,
      value:[],
    },
    radio:{
      type:Number,
      value:1,
    },
    disabled:{
      type:Boolean,
      value:false
    },
    tips:{
      type:String,
      value:""
    },

    radio_tips:{
      type:String,
      value:""
    },

    direction:{
      type:String,
      value:"horizontal"
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onChange(e){
      this.triggerEvent("change",e.detail)
    }
  }
})
