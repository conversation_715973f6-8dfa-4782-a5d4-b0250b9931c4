<!--component/radio/index.wxml-->
<view>
  <view class="supplier_type">
    <view class="labels">
      <view>{{radioTitle}}</view>
      <view class="two">{{tips}} </view>
    </view>
    <view class="radio_content">
        <van-radio-group value="{{ radio }}" disabled="{{disabled}}" bind:change="onChange" direction="{{direction}}">
        <view wx:for="{{radioList}}" wx:key="key">
          <van-radio name="{{item.id}}">{{item.name}}</van-radio>
        </view>
      </van-radio-group>
      <view class="radio_tips">
          {{radio_tips}}
      </view>
    </view>
  </view>
</view>