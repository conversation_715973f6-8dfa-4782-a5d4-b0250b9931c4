// component/updata-avatar/index.js
const uploadFile = require('../../utils/uploadFile');
const util = require('../../utils/util');
const app = getApp()
import {
  upload_sign,
} from '../../utils/api';
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    width:{
      type:String,
      value:60
    },
    height:{
      type:String,
      value:60
    },

    fileList:{
      type:Array,
      value:[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imageUrl:app.globalData.imageUrl,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    choosePortrait() {
      let that = this
      that.data.fileList = []
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success(res) {
          console.log(res)
          var file = res.tempFiles[0].tempFilePath
          console.log(file)
          let type = "certificate"
          upload_sign(type).then(res => {
            if (res.data.code == 0) {
             let  uploadData = res.data.data;
              let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(file))
              uploadFile(uploadData.host, file, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
                if(data.statusCode==200){
                  const {
                    fileList = []
                  } = that.data;
                  fileList.push({
                    url: that.data.imageUrl + newPath
                  });
                  let list = {
                    type: 'image',
                    origin_name: util.siding(file),
                    name:newPath,
                  }
                  that.setData({
                    fileList,
                  });
                  // console.log(that.data.fileList)
                  that.triggerEvent("choosePortrait",list)
                }
              })
            }
          })

        }
      })
    },
  },

  lifetimes:{
    attached: function () {
    }
  }
})
