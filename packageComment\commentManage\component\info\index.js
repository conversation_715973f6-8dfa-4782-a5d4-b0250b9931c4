const app = getApp()
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import {
  reason,
  reply
} from '../../../../apis/supplier/comment';
import {
  categoryCoverProcess
} from '../../../../utils/dict';

Component({
  properties: {
    item: {
      type: Object,
      value: {},
    },
    comment: {
      type: String,
      value: ''
    },
    active: {
      type: String,
      value: ''
    }
  },

  data: {
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    supplier_id: '',
    status: 1,
    active: 'doing',
    isEnd: false,
    refresh: false, // 是否刷
    page: 1,
    showDialog: false,
    radio: 'one',
    reason: '',
    id: '',
    showVideo: false,
    video: '',
    showReply: false,
    replyText: ''
    // comment: '满意'
  },

  methods: {

    // 审核
    examine(e) {
      let id = e.currentTarget.dataset.info.id
      this.setData({
        showDialog: true,
        id: id,
      })
    },

    sure() {
      const data = {
        comment_id: this.data.id,
        audit_status: this.data.radio == 'one' ? 2 : 3,
        audit_note: this.data.reason
      }
      if (data.audit_status == 3 && data.audit_note == "") {
        wx.showToast({
          title: '请填写审核理由',
          icon: "none",
          duration: 1000
        })
        return
      }

      let that = this

      reason(data).then(res => {
        if (res.data.code == 0) {
          wx.showToast({
            title: '保存成功',
          })
          setTimeout(() => {
            this.setData({
              showDialog: false
            })

            that.triggerEvent('refresh', '')
          }, 1200);
        }
      }).catch((r) => {
        //  todo
      })
    },

    reason(e) {
      this.setData({
        'reason': e.detail.value
      })
    },

    onClose() {
      this.setData({
        reason: '',
        showDialog: false,
        radio: 'one'
      })
    },

    radioChange(event) {
      let v = event.detail
      let reason = this.data.reason
      if (v == 'one') {
        reason = ''
      }
      this.setData({
        radio: v,
        reason: reason,
      })
    },

    handleImg(e) {
      let path = e.currentTarget.dataset.info.name
      let src = this.data.imageUrl
      wx.previewImage({
        urls: [src + path]
      })
    },

    // handleVideo(e) {
    // let path = e.currentTarget.dataset.video
    // this.setData({
    // video: path,
    // showVideo: true
    // })
    // },

    onCloseVideo() {
      this.setData({
        showVideo: false
      });
    },


    // 跳转商品
    toProductDetail(e) {
      let id = e.currentTarget.dataset.info.product_id
      wx.navigateTo({
        url: '/pages/supplier/product/info/index?id=' + id,
      })
    },
    // 回复
    replyMessage(e) {
      this.setData({
        showReply: true,
        id: e.currentTarget.dataset.info.id
      });
    },
    onChangeReply(e) {
      this.setData({
        replyText: e.detail
      });
    },
    onCloseReply() {
      this.setData({
        showReply: false
      });
    },
    handleReply(e) {
     
      if (this.data.replyText == '') {
        Toast('请填写回复内容');
        return
      }
      let data = {
        id: this.data.id,
        content: this.data.replyText
      }
      let that = this
      reply(data).then(res => {
        if (res.data.code == 0) {
          setTimeout(() => {
            that.triggerEvent('refresh', '')
          }, 1200);
        }
      })
    }
  }
})