<view class="container">
  <view class="userInfo">
    <view class="user">
      <image wx:if="{{item.buyer_avatar.name}}" class="headImg" src="{{imageUrl + item.buyer_avatar.name}}" mode="" />
      <text>{{item.buyer_name}}</text>
    </view>

  </view>
  <view class="comment">
    <view>
      <van-rate value="{{ item.product_star_fmt }}" allow-half void-icon="star" void-color="#eee" readonly />
      <text class="isolation">|</text>
      <text>{{comment}}</text>
    </view>


  </view>
  <view class="content">
    {{item.content}}
  </view>
  <view class="commentImg">
    <video class="productImg" class="ProductMap" wx:if="{{item.video.name}}" src="{{imageUrl + item.video.name}}" data-video="{{imageUrl + item.video.name}}" mode="aspectFit"></video>
    <!-- <view > -->
    <image wx:for="{{item.img_list}}" wx:key="index" class="ProductMap" src="{{imageUrl + item.name}}" data-info="{{item}}" mode="aspectFit" bindtap="handleImg" />
    <!-- </view> -->
  </view>
  <view class="product" data-info="{{item}}" bindtap="toProductDetail">
    <view class="cover">
      <image wx:if="{{item.product_cover.name}}" src="{{imageUrl+categoryCoverProcess+item.product_cover.name}}" mode="widthFix" class="img" />
    </view>
    <view class="right">
      <view class="title">
        {{item.product_title}}
      </view>
      <view class="to-detail">
        <van-icon name="arrow" />
      </view>
    </view>
  </view>

  <view class="bottom">
    <text class="createTime">时间：{{item.receive_time}}</text>
    <text class="toExamine" bind:tap="examine" data-info="{{item}}" wx:if="{{item.audit_status===1}}">审核</text>
    <view wx:if="{{active == 'done'&& item.reply_content==''}}" class="reply" data-info="{{item}}" bind:tap="replyMessage">回复</view>
  </view>

  <view class="refuse" wx:if="{{item.audit_status===3}}">
    <text>审核理由：</text>
    <text>{{item.supplier_audit_note}}</text>
  </view>
  <view wx:if="{{item.reply_content!==''}}" class="replyContent">
    <view>商家回复</view>
    {{item.reply_content}}
  </view>
</view>

<van-dialog use-slot title="" show="{{ showDialog }}" show-confirm-button="{{false}}" bind:close="onClose" closeOnClickOverlay>
  <view style="padding: 20rpx 6rpx;">
    <view class="redio">
      <view>
        状态：
      </view>
      <van-radio-group value="{{ radio }}" bind:change="radioChange">
        <view style="display: flex;">
          <view>
            <van-radio name="one">通过</van-radio>
          </view>
          <view style="margin-left: 14rpx;">
            <van-radio name="two">不通过</van-radio>
          </view>
        </view>
      </van-radio-group>
    </view>
    <view class="reason" wx:if="{{radio == 'two'}}">
      <text class="text">理由:</text>
      <input type="text" placeholder="填写理由" value="{{reason}}" bindinput="reason" maxlength="30" />
    </view>
    <view style="width: 100%;display: flex;margin-top: 30rpx; justify-content: space-around;">
      <view style="width:100rpx" class='cancelbnt' bindtap='onClose'>取消</view>
      <view style="width:100rpx" class='wishbnt' bindtap='sure'>确定</view>
    </view>
  </view>
</van-dialog>

<!-- 回复 -->
<van-dialog use-slot title="回复" show="{{ showReply }}" show-cancel-button confirm-button-open-type="getUserInfo" bind:close="onCloseReply" bind:confirm="handleReply">
  <view style="padding: 0 20rpx;">
    <!-- <van-cell-group> -->
    <van-field value="{{ replyText }}" maxlength="100" type="textarea" placeholder="请输入" autosize border="{{ false }}" bind:change="onChangeReply" />
    <!-- </van-cell-group> -->
  </view>

</van-dialog>


<van-toast id="van-toast" />