/* pages/classify/goodsDetail/CommentList/index.wxss */

.container{
  padding: 30rpx;
}
.list{
  border: 1px solid #eceaea;
  padding: 20rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}
.userInfo{
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  align-items: stretch;
}
.headImg{
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.user{
  display: flex;
  align-items: center;
}
.createTime{
 color: rgb(170, 171, 172);
}


.comment{
  color: rgb(133, 134, 136);
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
  display: flex;
  justify-content: space-between;
}
.reply{
  /* color: #696767; */
  border: 1px solid #e7e4e4;
  padding: 4rpx 20rpx;
  border-radius: 10rpx;
}
.isolation{
  margin: 0 20rpx;
  color: rgb(209, 208, 205);
}
.content{
  margin-bottom: 20rpx;
  word-break:break-all; 
}
.ProductMap{
  width: 200rpx;
  height: 200rpx;
  margin-right: 14rpx;
}
.toExamine{
  border: 1rpx solid #c4c2c2;
  display: inline-block;
  width: 100rpx;
  text-align: center;
  vertical-align: middle;
  padding: 8rpx 0;
  border-radius: 10rpx;
}

.bottom{
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}
.redio{
  padding: 30rpx;
  display: flex;
  align-items: center;
}
.reason{
  display: flex;
  height: 50rpx;
  line-height: 50rpx;
  align-items: center;
  padding: 0 30rpx ;
}
.text{
  margin-right: 20rpx;
}
.btn{
  display: flex;
  /* border: 1px solid red; */
  justify-content: space-around;
  margin-top: 30rpx;
}
.btn text{
  border: 1px solid rgb(177, 174, 174);
  display: inline-block;
  padding:  10rpx 30rpx;
}
.refuse{
  margin-top: 20rpx;
}

.cancelbnt{
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx;
  color: #07c060;
  margin-right: 20rpx;
}
.wishbnt{
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx;
  color: white;
}


.product {
  display: flex;
  box-sizing: border-box;
  align-items: stretch;
  background-color: #eeeeee;
  border-radius: 22rpx;
  padding: 10rpx 10rpx;
}

.product .right {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.product .right .to-detail {
  display: flex;
  justify-content: center;
  padding: 10rpx;
}


.product .right .title {
  font-size: 26rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  /* display: -moz-box;
  -moz-line-clamp: 2;
  -moz-box-orient: vertical; */
  /* word-wrap: break-word; */
  word-break: break-all;
  white-space: normal;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 1.3em;
  max-height: 2.6em;
}

.product .cover {
  margin-right: 20rpx;
}

.product .cover .img {
  width: 100rpx;
  height: auto;
  border-radius: 22rpx;
}
.replyContent{
  border: 1px solid #c5c3c3;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  margin-top: 20rpx;
  color: #c4c2c2;
  font-size: small;
  /* text-indent: 2em; */
}