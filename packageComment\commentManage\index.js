import {
  list,
  reason
} from '../../apis/supplier/comment';
const app = getApp()
import Toast from '@vant/weapp/toast/toast';
import dayjs from "../../libs/dayjs"
import Dialog from '@vant/weapp/dialog/dialog';
import {
  checkPrivacy,
} from '../../utils/check'
Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    star: 5,
    supplier_id: '',
    status: 1,
    active: 'doing',
    list: [],
    isEnd: false,
    refresh: false, // 是否刷
    page: 1,
  },

  onChange(event) {
    let active = event.detail.name
    let status = 1
    switch (active) {
      case 'doing':
        status = 1
        break;
      case 'done':
        status = 2
        break;
      case 'noNet':
        status = 3
        break;
    }
    this.setData({
      active: active,
      list: [],
      page: 1,
      isEnd: false,
      status: status
    })
    this.list()
  },

  list() {
    if (this.data.isEnd) {
      return
    }
    let page = this.data.page
    const data = {
      "supplier_id": this.data.supplier_id,
      "status": this.data.status,
      "page": page,
      "limit": 5
    }
    list(data).then((res) => {
      if (res.data.code == 0) {
        let state = res.data.data.list
        if (!state) {
          // wx.showToast({
          //   title: '没有更多数据了',
          //   icon: 'none',
          //   duretion: 2000
          // })
          this.setData({
            isEnd: true,
            // list: []
          })
          return
        }
        let star = 5
        let time = ''
        let id = ''
        if (res.data.data.list !== null) {
          res.data.data.list.forEach((item) => {
            item.receive_time = this.dealTime(item.created_at)
            id = item.id
            star = item.product_star
            time = item.created_at

            item.product_star_fmt = item.product_star/10

            console.log(typeof item.product_star_fmt);

            let s = ''
            switch (star) {
              case 5:
              case 10:
                s = "非常差"
                break;
              case 15:
              case 20:
                s = "不满意"
                break;
              case 25:
              case 30:
                s = "一般"
                break;
              case 35:
              case 40:
                s = "比较满意"
                break;
              case 45:
              case 50:
                s = "非常满意"
                break;
              default:
                break;
            }
            item.star = star
            item.star_desc = s

          })
          let newList = [...this.data.list, ...res.data.data.list]
          this.setData({
            list: newList,
            star: star / 10,
            page: page + 1,
            id: id
          })

        }
      }

    }).catch(err => {
      Toast(err.data.message);
    })
  },

  dealTime(at) {
    return dayjs(at).format('YYYY-MM-DD hh:mm:ss')
  },
  onLoad(options) {
    checkPrivacy()
    let supplierid = wx.getStorageSync('supplierid')
    this.setData({
      supplier_id: supplierid
    })
    this.list()
  },


  refresh() {
    this.setData({
      active: 'doing',
      list: [],
      page: 1,
      isEnd: false,
      status: 1,
    })
    this.list()
  },

  onReachBottom() {
    // console.log('onReachBottom');
    this.list()
  },
})