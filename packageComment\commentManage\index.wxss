/* pages/classify/goodsDetail/CommentList/index.wxss */

.container{
  
}
.list{
  border: 1px solid #eceaea;
  margin: 20rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}
.userInfo{
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  align-items: stretch;
}
.headImg{
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.user{
  display: flex;
  align-items: center;
}
.createTime{
 color: rgb(170, 171, 172);
}
.commentImg{
}

.comment{
  color: rgb(133, 134, 136);
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}
.isolation{
  margin: 0 20rpx;
  color: rgb(209, 208, 205);
}
.content{
  margin-bottom: 20rpx;
  word-break:break-all; 
}
.ProductMap{
  width: 200rpx;
  height: 200rpx;
  margin-right: 14rpx;
}
.toExamine{
  border: 1rpx solid #c4c2c2;
  display: inline-block;
  width: 100rpx;
  text-align: center;
  vertical-align: middle;
  padding: 8rpx 0;
}

.bottom{
  display: flex;
  justify-content: space-between;
  
}
.redio{
  padding: 30rpx;
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.reason{
  /* border: 1px solid red; */
  display: flex;
  height: 50rpx;
  line-height: 50rpx;
  align-items: center;
  padding: 0 30rpx ;
}
.text{
  margin-right: 20rpx;
}
.btn{
  display: flex;
  /* border: 1px solid red; */
  justify-content: space-around;
  margin-top: 30rpx;
}
.btn text{
  border: 1px solid rgb(177, 174, 174);
  display: inline-block;
  padding:  10rpx 30rpx;
}
.refuse{
  margin-top: 30rpx;
}