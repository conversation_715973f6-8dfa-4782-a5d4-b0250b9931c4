const app = getApp();

let tsc = require('../../packageSort/gprint/tsc')
let esc = require('../../packageSort/gprint/esc.js')

Page({
  data: {

    devicesList: [],
    services: [],
    serviceId: 0,
    writeCharacter: false,
    readCharacter: false,
    notifyCharacter: false,
    isScanning: false,

    looptime: 0,
    currentTime: 1,
    lastData: 0,
    oneTimeData: 20, //发送数据大小，测试正常，不能太多
    returnResult: "returnResult",
    canvasWidth: 80,
    canvasHeight: 80,
    printNum: 1,
    currentPrint: 1,
    isReceiptSend: false,
    isLabelSend: false,


    list: [{
        name: "昆明仓11"
      },
      {
        name: "昆明仓22"
      },
      {
        name: "昆明仓33"
      },
      {
        name: "昆明仓44"
      },
    ],
    currentNumber:0,
  },
  /**
   * 监听数据输入
   */
  inputEvent: function (e) {
    this.setData({
      oneTimeData: e.detail.value
    })
    console.log('oneTimeData: ', this.data.oneTimeData)
  },
  /**
   * 蓝牙搜索
   */
  searchBluetooth: function () {
    var that = this
    //判断蓝牙是否打开
    wx.openBluetoothAdapter({
      success: function (res) {
        console.log(res, 999)
        wx.getBluetoothAdapterState({
          success: function (res) {
            // console.log(res)
            if (res.available) {
              if (res.discovering) {
                wx.stopBluetoothDevicesDiscovery({
                  success: function (res) {
                    console.log(res)
                  }
                })
              }
              that.checkPemission()
            } else {
              wx.showModal({
                title: '提示',
                content: '请开启手机蓝牙后再试',
              })
            }
          },
        })
      },
      fail: function (e) {
        console.log(e, 9999)
        wx.showModal({
          title: '提示',
          content: '蓝牙初始化失败，请打开蓝牙',
        })
      }
    })
  },
  /**
   * android 6.0以上需授权地理位置权限
   */
  checkPemission: function () {
    var that = this;
    var systemInfo = wx.getSystemInfoSync();
    var platform = systemInfo.platform;
    if (platform == "ios") {
      that.getBluetoothDevices()
    } else if (platform == "android") {
      let system = systemInfo.system;
      let system_no = system.replace('android', '');
      system_no = system.replace('Android', '');
      if (Number(system_no) > 5) {
        wx.getSetting({
          success: function (res) {
            console.log(res)
            if (!res.authSetting['scope.userLocation']) {
              wx.authorize({
                scope: 'scope.userLocation',
                complete: function (res) {
                  that.getBluetoothDevices()
                }
              })
            } else {
              that.getBluetoothDevices()
            }
          }
        })
      }
    }
  },
  /**
   * 获取蓝牙设备信息
   */
  getBluetoothDevices: function () {
    var that = this
    // console.log("start search")
    wx.showLoading({
      title: '正在加载',
    })
    that.setData({
      isScanning: true
    })
    wx.startBluetoothDevicesDiscovery({
      success: function (res) {
        console.log(res)
        setTimeout(function () {
          wx.getBluetoothDevices({
            success: function (res) {
              var devices = []
              var num = 0
              for (var i = 0; i < res.devices.length; ++i) {
                if (res.devices[i].name != "未知设备") {
                  devices[num] = res.devices[i]
                  num++
                }
              }
              // console.log(devices,"之前")
              // devices = devices.filter(item=>{
              //   return item.name == "XP";
              // })
              let devicess = []
              devices.map(item=>{
                let reg = RegExp(/XP/)
                console.log(reg.exec(item.name))
                if (reg.exec(item.name)){
                  devicess.push(item)
                }
              })
         

              
              // console.log(devicess,"之后")
              that.setData({
                devicesList: devicess,
                isScanning: false
              })
              wx.hideLoading()
              wx.stopPullDownRefresh()
            },
          })
        }, 3000)
      },
    })
  },
  /**
   * 开始连接蓝牙设置
   */
  connectBluetoothSettings: function (e) {
    var that = this;
    let index = e.currentTarget.dataset.index;
    let deviceId = that.data.devicesList[index].deviceId;
    wx.stopBluetoothDevicesDiscovery({
      success: function (res) {
        console.log(res)
      },
    })
    that.setData({
      serviceId: 0,
      writeCharacter: false,
      readCharacter: false,
      notifyCharacter: false
    })
    console.log(deviceId)
    wx.showLoading({
      title: '正在连接',
    })
    wx.createBLEConnection({
      deviceId: deviceId,
      success: function (res) {
        console.log(res)
        app.globalData.bluetoothDeviceId = deviceId
        that.getBLEDeviceServices();
        wx.navigateBack()
      },
      fail: function (e) {
        wx.showModal({
          title: '提示',
          content: '连接失败',
        })
        console.log(e)
        wx.hideLoading()
      },
      complete: function (e) {
        console.log(e)
      }
    })
  },
  /**
   * 获取蓝牙设备所有服务
   */
  getBLEDeviceServices: function () {
    var that = this
    console.log(app.globalData.bluetoothDeviceId)
    wx.getBLEDeviceServices({
      deviceId: app.globalData.bluetoothDeviceId,
      success: function (res) {
        console.log(res)
        that.setData({
          services: res.services
        })
        that.getBLEDeviceCharacteristics()
      },
      fail: function (e) {
        console.log(e)
      },
      complete: function (e) {
        console.log(e)
      }
    })
  },
  /**
   * 获取蓝牙设备某个服务中所有特征值
   */
  getBLEDeviceCharacteristics: function () {
    var that = this
    var list = that.data.services
    var num = that.data.serviceId
    var write = that.data.writeCharacter
    var read = that.data.readCharacter
    var notify = that.data.notifyCharacter
    wx.getBLEDeviceCharacteristics({
      deviceId: app.globalData.bluetoothDeviceId,
      serviceId: list[num].uuid,
      success: function (res) {
        console.log(res)
        for (var i = 0; i < res.characteristics.length; ++i) {
          var properties = res.characteristics[i].properties
          var item = res.characteristics[i].uuid
          if (!notify) {
            if (properties.notify) {
              app.globalData.notifyCharaterId = item
              app.globalData.notifyServiceId = list[num].uuid
              notify = true
            }
          }
          if (!write) {
            if (properties.write) {
              app.globalData.writeCharaterId = item
              app.globalData.writeServiceId = list[num].uuid
              write = true
            }
          }
          if (!read) {
            if (properties.read) {
              app.globalData.readCharaterId = item
              app.globalData.readServiceId = list[num].uuid
              read = true
            }
          }
        }
        if (!write || !notify || !read) {
          num++
          that.setData({
            writeCharacter: write,
            readCharacter: read,
            notifyCharacter: notify,
            serviceId: num
          })
          if (num == list.length) {
            wx.showModal({
              title: '提示',
              content: '找不到该读写的特征值',
            })
          } else {
            that.getBLEDeviceCharacteristics()
          }
        } else {
          that.notifyBLECharacteristicValueChange()
        }
      },
      fail: function (e) {
        console.log(e)
      },
      complete: function (e) {
        console.log("write:" + app.globalData.writeCharaterId)
        console.log("read:" + app.globalData.readCharaterId)
        console.log("notify:" + app.globalData.notifyCharaterId)
      }
    })
  },
  /**
   * 启用低功耗蓝牙设备特征值变化时的 notify 功能
   */
  notifyBLECharacteristicValueChange: function () {
    console.log("deviceId:" + app.globalData.bluetoothDeviceId)
    console.log("serviceId:" + app.globalData.notifyServiceId)
    console.log("notifyCharaterId:" + app.globalData.notifyCharaterId)
    wx.hideLoading();
    wx.notifyBLECharacteristicValueChange({
      deviceId: app.globalData.bluetoothDeviceId,
      serviceId: app.globalData.notifyServiceId,
      characteristicId: app.globalData.notifyCharaterId,
      state: true,
      success: function (res) {
        wx.onBLECharacteristicValueChange(function (r) {
          // console.log(`characteristic ${r.characteristicId} has changed, now is ${r}`)
          console.log('onBLECharacteristicValueChange=', r);
        })
      },
      fail: function (e) {
        console.log('fail', e)
      },
      complete: function (e) {
        console.log('complete', e)
      }
    })
  },
  /**
   * 账单模式
   */
  sendData: function () {
    var data = "好好学习，天天向上\n"
    var that = this;
    var command = tsc.jpPrinter.createNew()
    command.setGap(0)
    command.setCls()
    command.setText(40, 60, "TSS24.BF2", 1, 1, data)
    command.setPagePrint()
    that.prepareSend(command.getData())
  },

  labelTests() {
    let lengths = this.data.list.length
    if(this.data.currentNumber==lengths){
      // console.log("停止打印")
    }else{
      this.labelTest()
    }
  },
  /**
   * 标签模式
   */
  labelTest: function (name) {
    console.log("打印")
    var that = this;
    var canvasWidth = that.data.canvasWidth
    var canvasHeight = that.data.canvasHeight
    var command = tsc.jpPrinter.createNew()
    command.setSize(80, 60)
    command.setGap(0)
    command.setCls() //需要设置这个，不然内容和上一次重复
    // command.setQR(1, 120, "L", 5, "A", "poso2o.com")
    command.setText(20, 70, "TSS24.BF2", 2, 2, this.data.list[this.data.currentNumber].name)
    command.setText(20, 130, "TSS24.BF2", 2, 2, "云南省昆明市官渡区银海松")
    command.setText(20, 180, "TSS24.BF2", 1, 1, "地址: 云南省昆明市官渡区云南省昆明市官渡区广福路")
    command.setText(20, 210, "TSS24.BF2", 1, 1, "电话:152****4153")
    command.setText(300, 210, "TSS24.BF2", 1, 1, "收货人:杨丽梅")
    command.setText(20, 240, "TSS24.BF2", 1, 1, "品名:海南麒麟瓜22kg1号箱装")
    command.setText(20, 300, "TSS24.BF2", 1, 1, "数量:1件(1)")
    command.setText(300, 300, "TSS24.BF2", 1, 1, "重量:22KG")

    command.setText(20, 360, "TSS24.BF2", 1, 1, "供应商:鲜源果业有限公司")
    command.setText(20, 390, "TSS24.BF2", 1, 1, "2023年05月05日08:20:05")
    wx.canvasGetImageData({
      canvasId: 'edit_area_canvas',
      x: 0,
      y: 0,
      width: canvasWidth,
      height: canvasHeight,
      success: function (res) {
        command.setBitmap(60, 0, 0, res)
      },
      complete: function () {
        command.setPagePrint()
        that.setData({
          isLabelSend: true
        })
        that.prepareSend(command.getData())
      }
    })

  },
  /**
   * 准备发送数据
   */
  prepareSend: function (buff) {
    console.log('buff', buff)
    var that = this
    var time = that.data.oneTimeData
    var looptime = parseInt(buff.length / time);
    var lastData = parseInt(buff.length % time);
    console.log(looptime + "---" + lastData);
    that.setData({
      looptime: looptime + 1,
      lastData: lastData,
      currentTime: 1,
    })
    that.Send(buff)
  },
  /**
   * 查询打印机状态
   */
  queryPrinterStatus: function () {
    var command = esc.jpPrinter.Query();
    command.getRealtimeStatusTransmission(1);
    this.setData({
      returnResult: "查询成功"
    })
  },
  /**
   * 分包发送
   */
  Send: function (buff) {
    var that = this
    var currentTime = that.data.currentTime;
    var loopTime = that.data.looptime;
    var lastData = that.data.lastData;
    var onTimeData = that.data.oneTimeData;
    var printNum = that.data.printNum; //打印多少份
    var currentPrint = that.data.currentPrint;
    var buf
    var dataView
    if (currentTime < loopTime) {
      buf = new ArrayBuffer(onTimeData)
      dataView = new DataView(buf)
      for (var i = 0; i < onTimeData; ++i) {
        dataView.setUint8(i, buff[(currentTime - 1) * onTimeData + i])
      }
    } else {
      buf = new ArrayBuffer(lastData)
      dataView = new DataView(buf)
      for (var i = 0; i < lastData; ++i) {
        dataView.setUint8(i, buff[(currentTime - 1) * onTimeData + i])
      }
    }
    console.log("第" + currentTime + "次发送数据大小为：" + buf.byteLength);
    console.log("deviceId:" + app.globalData.bluetoothDeviceId)
    console.log("serviceId:" + app.globalData.writeServiceId)
    console.log("characteristicId:" + app.globalData.writeCharaterId)

    wx.writeBLECharacteristicValue({
      deviceId: app.globalData.bluetoothDeviceId,
      serviceId: app.globalData.writeServiceId,
      characteristicId: app.globalData.writeCharaterId,
      value: buf,
      success: function (res) {
        console.log('写入成功', res)
        if(currentTime==loopTime){
          that.data.currentNumber++
              that.labelTests()
        }
      },
      fail: function (e) {
        console.error('写入失败', e)
      },
      complete: function () {
        currentTime++
        if (currentTime <= loopTime) {
          that.setData({
            currentTime: currentTime
          })
          that.Send(buff)
        } else {
          wx.showToast({
            title: '已打印第' + currentPrint + '张',
          })
          if (currentPrint == printNum) {
            that.setData({
              looptime: 0,
              lastData: 0,
              currentTime: 1,
              isReceiptSend: false,
              isLabelSend: false,
              currentPrint: 1
            })
          } else {
            currentPrint++
            that.setData({
              currentPrint: currentPrint,
              currentTime: 1,
            })
            console.log("开始打印")
            that.Send(buff)
          }
        }
      }
    })
  },
  onLoad: function (options) {
    let that = this;
    that.searchBluetooth()
  },

})