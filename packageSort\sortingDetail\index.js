import {
  sorting_order_query,
  sorting_order_query_temp,
  sorting_updata,
  sorting_updata_temp,
  sorting_updata_photo
} from '../../apis/warehouse/sorting';
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
const util = require('../../utils/util');
let tsc = require('../../packageSort/gprint/tsc')
let esc = require('../../packageSort/gprint/esc.js')

import {
  upload_sign,
} from '../../utils/api';
const uploadFile = require('../../utils/uploadFile');

import regeneratorRuntime from 'regenerator-runtime'

const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    sortingDetail: "", //分拣详情
    num: 0,
    ischange: false,
    isSorting: false,
    looptime: 0,
    currentTime: 1,
    lastData: 0,
    oneTimeData: 20, //发送数据大小，测试正常，不能太多
    returnResult: "returnResult",
    canvasWidth: 80,
    canvasHeight: 80,
    printNum: 1,
    currentPrint: 1,
    isReceiptSend: false,
    isLabelSend: false,

    currentNumber: 0,
    loading: false, //打印按钮加载状态

    printIndex: 0,
    // printIndexs: "",

    bluetoothOn: false,
    showSortBtn: true,
    value: 0,

    returnStatus: false,
    isPrinting: false, // 打印中
    showOther: false,
    mergeOne: false,
    mergeData: {},
    photoPreviewList: [], // 商品图
    emptyImg: {
      type: '',
      origin_name: '',
      name: '',
    },
    hasShip: false, // 是否发货
    readOnly: false,
  },


  // 分拣数量
  sortingNum(e) {
    let index = e.currentTarget.dataset.index //外层下标
    this.data.sortingDetail.order_list[index].sort_num = e.detail
    this.setData({
      sortingDetail: this.data.sortingDetail
    })
  },
  //分拣重量
  sortingWeight(e) {
    let index = e.currentTarget.dataset.index //外层下标
    // let w = Number.parseFloat(e.detail.value)
    let w = e.detail.value
    let dw = this.data.sortingDetail.order_list[index].due_weight
    let warn = this.warnSortWeight(dw, w)
    this.data.sortingDetail.order_list[index].sort_weight = util.clearNumOne(w)
    this.data.sortingDetail.order_list[index].warn_sort_weight = warn
    let wsw = "sortingDetail.order_list[" + index + "].warn_sort_weight"
    let f = true
    for (let i = 0; i < this.data.sortingDetail.order_list.length; i++) {
      const element = this.data.sortingDetail.order_list[i];
      if (element.warn_sort_weight && element.warn_sort_weight === true) {
        f = false
        break
      }
    }
    this.setData({
      [wsw]: warn,
      showSortBtn: f,
      sortingDetail: this.data.sortingDetail
    })
  },



  warnSortWeight(dueWeight, sortWeight) {
    let diff = Math.abs(dueWeight - sortWeight)
    let percent = diff / dueWeight
    if (percent > 0.2) {
      return true
    }
    return false
  },
  secondConfirm(e) {
    let index = e.currentTarget.dataset.index //外层下标
    this.data.sortingDetail.order_list[index].warn_sort_weight = false
    let f = true
    for (let i = 0; i < this.data.sortingDetail.order_list.length; i++) {
      const element = this.data.sortingDetail.order_list[i];
      if (element.warn_sort_weight && element.warn_sort_weight === true) {
        f = false
        break
      }
    }
    let wsw = "sortingDetail.order_list[" + index + "].warn_sort_weight"
    setTimeout(() => {
      this.setData({
        showSortBtn: f,
        [wsw]: false
      })
    }, 200);
  },

  //分拣
  sorting() {
    let data = {
      id: this.data.stockupid,
      order_list: []
    }
    this.data.sortingDetail.order_list.map(item => {
      let datas = {
        order_id: item.order_id,
        num: parseInt(item.sort_num),
        weight: parseInt((item.sort_weight * 1000).toFixed(0))
      }
      data.order_list.push(datas)
    })
    Dialog.confirm({
        title: '提示',
        message: '确认分拣',
      })
      .then(() => {
        // on confirm
        this.sortings(data)
      })
      .catch(() => {
        // on cancel
      })
  },

  // 查询蓝牙是否已经连接
  openBluetoothAdapter() { //根据自己需要在哪一步执行调用即可
    let that = this;
    wx.openBluetoothAdapter({
      success(res) {
        // console.log(res)
        that.setData({
          bluetoothOn: true
        })
      },
      fail(err) {
        // console.log(err)
        that.setData({
          bluetoothOn: false
        })
      },
    })
  },



  other(e) {
    let index = e.currentTarget.dataset.index
    let p = this.data.sortingDetail.order_list[index].photo
    let hasShip = this.data.sortingDetail.order_list[index].has_ship

    let imgList = []
    if (p && p.name != '') {
      imgList.push({
        url: this.data.imageUrl + p.name,
        name: 'preview' + index.toString(),
        isImage: true,
        deletable: !hasShip,
      })
    }

    this.setData({
      showOther: true,
      mergeData: e,
      photoPreviewList: imgList,
      hasShip: hasShip,
    })
  },

  onCloseOther(e) {
    this.setData({
      showOther: false
    });
  },

  mergeOnePrint(e) {
    //  合单打印
    this.setData({
      showOther: false,
      mergeOne: true,
    });
    this.printLabel(this.data.mergeData)
  },

  printLabel(e) {
    let that = this
    let index = e.currentTarget.dataset.index
    let info = this.data.sortingDetail.order_list[index]
    let point = e.currentTarget.dataset.point

    console.log("info:::",info);

    if (info.sort_num == 0) {
      Toast.fail("分拣数不能等于0")
      return
    }


    let deliver_type = ''
    switch (info.deliver_type) {
      case 1:
        deliver_type = '配送'
        break
      case 2:
        deliver_type = '自提'
        break
      case 3:
        deliver_type = '物流'
        break
      case 4:
        switch (info.instant_deliver_type) {
          case 0:
            deliver_type = '即时配送'
            break
          case 1:
            deliver_type = '跑腿'
            break
          case 2:
            deliver_type = '货拉拉'
            break
        }
        break
    }

    let mobile = info.address.contact.mobile
    mobile = mobile.toString().replace(/^(\d{3})\d{4}(\d{4})$/, "$1****$2");

    let buyerName = info.buyer_name

    let userName = info.address.contact.name
    userName = this.hiddenName(userName)
    let data = {
      servicePoint: point, //服务点
      buyer_name: buyerName, //采购商名称
      deliver_type: deliver_type, // 收货方式
      address: info.address.address, //地址
      locationAddress: info.address.location.address,
      phone: mobile, //电话
      user_name: userName,
      product_title: this.data.sortingDetail.product_title,
      sku_name: this.data.sortingDetail.sku_name,
      sort_num: info.sort_num,
      sort_weight: info.sort_weight,
      supplier_name: this.data.sortingDetail.supplier_name,
      second_name: '->' + info.second_point_name,
      data: util.timestampToTime(new Date().getTime()),
    }
    this.setData({
      printIndex: index,
      printData: data,
      isPrinting: true
    })
    this.labelTest(this.data.printData, this.data.mergeOne).then(res => {
      this.setData({
        isPrinting: false,
        mergeOne: false,
        mergeData: {},
      })
    })
  },

  // 中间部分*
  hiddenName(str) {
    let str1 = ''
    let len = str.length
    let middle = parseInt(Math.ceil(len / 3))

    if (len == 1) {
      str1 = str
    }
    if (len > 1 && len < 5) {
      if (middle - 1 <= 0) {
        middle = 2
      }
      str1 = str.substr(0, middle - 1) + '*' + str.substr(len - middle + 1, len - 1)
    }
    if (len >= 5) {
      if (middle > 2) {
        middle = 2
      }
      str1 = str.substr(0, middle) + '*' + str.substr(len - middle, len - 1)
    }

    return str1
  },

  labelTest(data, mergeOne) {
    return new Promise((resolve) => {
      var that = this;
      var canvasWidth = that.data.canvasWidth
      var canvasHeight = that.data.canvasHeight
      var command = tsc.jpPrinter.createNew()
      // let nums = this.data.currentNumber + 1
      data.address.toString()
      let product_title = data.product_title.replace(/[ ]|[\r\n]/g, "");

      let num = data.sort_num

      if (mergeOne) {
        num = 1
      }

      for (let i = 0; i < num; i++) {
        let index = i + 1
        // command.setHome()
        command.setCls()
        command.setSize(80, 60)
        command.setGap(1)
        // command.setCls()
        command.setSpeed(6)
        command.setSound(0, 0)
        if (data.locationAddress, data.locationAddress.length > 20) {
          command.setText(20, 130, "TSS24.BF2", 1, 1, "定位:" + data.locationAddress.substring(0, 20))
          command.setText(20, 160, "TSS24.BF2", 1, 1, data.locationAddress.substring(20, 40))
          command.setText(20, 190, "TSS24.BF2", 1, 1, "地址:" + data.address)
        } else {
          command.setText(20, 130, "TSS24.BF2", 1, 1, "定位:" + data.locationAddress)
          command.setText(20, 160, "TSS24.BF2", 1, 1, "地址:" + data.address)
        }
        command.setText(20, 40, "TSS24.BF2", 2, 2, data.buyer_name)
        //  服务仓
        command.setText(20, 0, "TSS24.BF2", 1, 1, data.servicePoint)
        // command.setText(150, 10, "TSS24.BF2", 1, 1, data.second_name)
        // command.setText(390, 100, "TSS24.BF2", 1, 1, data.servicePoint)
        command.setText(20, 220, "TSS24.BF2", 1, 1, "电话:" + data.phone)
        command.setText(300, 220, "TSS24.BF2", 1, 1, "收货人:" + data.user_name)
        command.setText(20, 280, "TSS24.BF2", 1, 1, "品名:" + product_title)
        command.setText(20, 310, "TSS24.BF2", 1, 1, "规格:" + data.sku_name)
        if (mergeOne) {
          command.setText(20, 340, "TSS24.BF2", 1, 1, "总数:" + data.sort_num)
        } else {
          command.setText(20, 340, "TSS24.BF2", 1, 1, "数量:" + index + '(' + num + ')')
        }
        command.setText(300, 340, "TSS24.BF2", 1, 1, "重量:" + data.sort_weight + "kg")

        command.setText(20, 400, "TSS24.BF2", 1, 1, "供应商:" + data.supplier_name)

        let x = 330
        if (data.deliver_type.length==4||data.deliver_type.length==3){
          x = 280
        }

        command.setText(x, 410, "TSS24.BF2", 2, 2, data.deliver_type)
        command.setText(20, 430, "TSS24.BF2", 1, 1, data.data)
        command.setPagePrint()
      }

      // wx.canvasGetImageData({
      //   canvasId: 'edit_area_canvas',
      //   x: 0,
      //   y: 0,
      //   width: canvasWidth,
      //   height: canvasHeight,
      //   success: function (res) {
      //     command.setBitmap(60, 0, 0, res)
      //   },
      //   complete: function () {
      //     command.setPagePrint()
      //     that.setData({
      //       isLabelSend: true
      //     })
      that.prepareSend(command.getData(), resolve)
      //   }
      // })
    })
  },

  prepareSend(buff, resolve) {
    var that = this
    var time = that.data.oneTimeData
    var looptime = parseInt(buff.length / time);
    var lastData = parseInt(buff.length % time);
    // console.log(looptime + "---" + lastData);
    that.setData({
      looptime: looptime + 1,
      lastData: lastData,
      currentTime: 1,
    })
    that.Send(buff, resolve)
    // })
  },

  Send(buff, resolve) {
    var that = this
    var currentTime = that.data.currentTime;
    var loopTime = that.data.looptime;
    var lastData = that.data.lastData;
    var onTimeData = that.data.oneTimeData;
    var printNum = that.data.printNum; //打印多少份
    var currentPrint = that.data.currentPrint;
    var buf
    var dataView
    if (currentTime < loopTime) {
      buf = new ArrayBuffer(onTimeData)
      dataView = new DataView(buf)
      for (var i = 0; i < onTimeData; ++i) {
        dataView.setUint8(i, buff[(currentTime - 1) * onTimeData + i])
      }
    } else {
      buf = new ArrayBuffer(lastData)
      dataView = new DataView(buf)
      for (var i = 0; i < lastData; ++i) {
        dataView.setUint8(i, buff[(currentTime - 1) * onTimeData + i])
      }
    }

    wx.writeBLECharacteristicValue({
      deviceId: app.globalData.bluetoothDeviceId,
      serviceId: app.globalData.writeServiceId,
      characteristicId: app.globalData.writeCharaterId,
      value: buf,
      success: function (res) {
        // console.log('写入成功', res)
        if (currentTime == loopTime) {
          // that.data.currentNumber++
          // that.print()
          // that.order_address(that.data.orderId, that.data.printIndex, that.data.printIndexs)
        }
      },
      fail: function (e) {
        // console.error('写入失败', e)
      },
      complete: function () {
        currentTime++
        if (currentTime <= loopTime) {
          that.setData({
            currentTime: currentTime
          })
          that.Send(buff, resolve)
        } else {
          if (currentPrint == printNum) {
            that.setData({
              looptime: 0,
              lastData: 0,
              currentTime: 1,
              isReceiptSend: false,
              isLabelSend: false,
              currentPrint: 1
            })
            resolve()
          } else {
            currentPrint++
            that.setData({
              currentPrint: currentPrint,
              currentTime: 1,
            })
            that.Send(buff, resolve)
          }
        }
      }
    })
  },

  queryPrinterStatus: function () {
    var command = esc.jpPrinter.Query();
    command.getRealtimeStatusTransmission(1);
    this.setData({
      returnResult: "查询成功"
    })
  },

  onLoad(options) {
    this.openBluetoothAdapter()
    let f = false
    if (options.readOnly && options.readOnly === "1") {
      f = true
    }
    this.setData({
      stockupid: options.stockupid,
      servicepointid: options.servicepointid,
      readOnly: f,
    })
    this.sortingDetail()
  },

  // 分拣详情
  sortingDetail() {
    let data = {
      service_point_id: this.data.servicepointid,
      id: this.data.stockupid,
    }
    sorting_order_query_temp(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        res.data.data.order_list.forEach(item => {
          item.sort_weight = item.sort_weight / 1000
          item.due_weight = item.due_weight / 1000
          list.push(item.has_sort)
        });
        if (list.indexOf(false) == -1) {
          this.setData({
            isSorting: true
          })
        } else {
          this.setData({
            isSorting: false
          })
        }

        res.data.data.order_list.forEach(item => {
          let photo_preview_list = []
          if (item.photo_list) {
            item.photo_list.forEach(element => {
              let url = this.data.imageUrl + element.name
              photo_preview_list.push({
                url: url,
              })
            });
          }
          item.photo_preview_list = photo_preview_list
        });

        this.setData({
          sortingDetail: res.data.data,
        })
      }
    })
  },

  //分拣
  sortings(data) {
    sorting_updata_temp(data).then(res => {
      if (res.data.code == 0) {
        Toast('分拣成功!');
        this.sortingDetail()
        this.setData({
          returnStatus: true
        })
      }
    }).catch(err => {

      // this.data.sortingDetail.order_list.map(item => {
      //   let datas = {
      //     order_id: item.order_id,
      //     num: Number(item.sort_num),
      //     weight: item.sort_weight * 1000
      //   }
      //   data.order_list.push(datas)
      // })
      Toast(err.data.message);
    })
  },

  //  上传商品图
  uploadPhoto(e) {
    let that = this
    let tempFilePath = e.detail.file
    let fileType = 'image'
    let type = "product"
    let item = tempFilePath
    upload_sign(type).then(res => {
      if (res.data.code == 0) {
        let uploadData = res.data.data;
        let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(item.url))
        uploadFile(uploadData.host, item.url, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
          if (data.statusCode == 200) {
            // let imgList = []
            // imgList.push({
            //   url: that.data.imageUrl + newPath,
            //   name: 'p1',
            //   isImage: true,
            //   deletable: true,
            // });

            let index = e.currentTarget.dataset.index

            let photo = {
              type: fileType,
              origin_name: util.siding(item.url),
              name: newPath,
            }

            let originList = this.data.sortingDetail.order_list[index].photo_list
            if (!originList) {
              originList = []
            }

            originList.push(photo)

            // let key = `sortingDetail.order_list[${index}].photo`

            // that.setData({
            // photoPreviewList: imgList,
            // [key]: photo,
            // });

            let info = this.data.sortingDetail
            let quality_id = info.id
            let order_id = info.order_list[index].order_id
            let data = {
              quality_id,
              order_id,
              photo_list: originList
            }

            this.updatePhoto(data)
          }
        })
      }
    })
  },

  //  商品图
  deletePhoto(e) {
    let index = e.currentTarget.dataset.index
    let info = this.data.sortingDetail
    let quality_id = info.id
    let order_id = info.order_list[index].order_id

    let deleteIndex = e.detail.index

    let originList = this.data.sortingDetail.order_list[index].photo_list

    originList.splice(deleteIndex, 1)

    let data = {
      quality_id,
      order_id,
      photo_list: originList
    }

    this.updatePhoto(data)
  },

  //  商品图
  updatePhoto(data) {
    sorting_updata_photo(data).then(res => {
      if (res.data.code === 0) {
        Toast("更新成功")
        this.sortingDetail()
      }
    })
  },

  onReady() {

  },

  onShow() {

  },

  onHide() {

  },

  onUnload() {
    let pages = getCurrentPages()
    let prevPage = pages[pages.length - 2];
    prevPage.setData({ // 将我们想要传递的参数在这里直接setData。上个页面就会执行这里的操作。
      returnStatus: this.data.returnStatus,
    })
  },
  onPullDownRefresh() {

  },
  onReachBottom() {

  },
})