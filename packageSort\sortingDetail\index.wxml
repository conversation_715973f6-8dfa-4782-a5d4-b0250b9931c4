<!--pages/warehouse/sorting/sortingDetail/index.wxml-->
<wxs src="../../utils/tool.wxs" module="tool" />
<view>
  <view class="sorting_detail">
    <view style="font-weight: bold;font-size: 26rpx;">
      {{sortingDetail.product_title}}- <text style="color: orange;">{{sortingDetail.sku_name}}</text>
    </view>
    <view style="margin: 10rpx 0;font-size: 26rpx;">
      <view>供应商：{{sortingDetail.supplier_name}}</view>
    </view>
    <view style="font-weight: bold;margin-top: 20rpx;font-size: 26rpx;">
      <view wx:if="{{!sortingDetail.quality_has}}">
        <van-tag type="danger">未品控</van-tag>
      </view>
      <view wx:else>
        已品控/总数：
        <text style="color: red;">{{sortingDetail.quality_num}}</text>
        <text>/</text>
        <text>{{sortingDetail.quality_due_num}}</text>
        <text></text>
      </view>
    </view>

    <view class="font_color sorting_order_list" wx:for="{{sortingDetail.order_list}}" wx:key="key">

      <view style="width: 100%;">
        <van-divider />

        <view style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
          <view style="font-weight: bold;color: #000;">
            {{item.buyer_name}}
            <text wx:if="{{item.has_quality==false}}" style="color: red;font-size: 24rpx !important;">[未品控]</text>
            <text wx:if="{{item.has_sort==false}}" style="color: red;font-size: 24rpx !important;">[未分拣]</text>
          </view>
          <view bindtap="other" style="margin-right: 10rpx;" data-info="{{item}}" data-point="{{sortingDetail.service_point_name}}" data-index="{{index}}">
            <van-icon name="ellipsis" size="25px" />
          </view>
          <van-button size="small" wx:if="{{isPrinting==false}}" class="print" data-info="{{item}}" loading="{{printIndex==index?loading:''}}" data-point="{{sortingDetail.service_point_name}}" data-index="{{index}}" data-second="{{item.second_point_name}}" bindtap="printLabel" type="info"> {{sortingDetail.sort_has?'再打印':'打印'}}</van-button>
          <van-button size="small" wx:else class="print" loading loading-text="{{printIndex==index?'打印中...':'等待'}}" type="info"> </van-button>
        </view>
        <view style="font-size: 28rpx;margin-top: 6rpx;">
          地址：{{item.address.address}}
        </view>
        <view style="font-size: 28rpx;margin-top: 6rpx;">
          配送方式：
          <text style="color: #409EFF;" wx:if="{{item.deliver_type==1}}">送货到店</text>
          <text style="color: #409EFF;" wx:if="{{item.deliver_type==2}}">自提</text>
          <text style="color: #409EFF;" wx:if="{{item.deliver_type==3}}"> 第三方物流</text>
          <text style="color: #409EFF;" wx:if="{{item.deliver_type==4}}"> 即时配送</text>
        </view>
        <view wx:for="{{sortingDetail.note_list}}" wx:for-item="ele" wx:key="order_id" style="margin-top: 10rpx;font-size: 26rpx;">
          <view wx:if="{{item.order_id == ele.order_id}}" style="display: flex;align-items: center;">
            <view style="display: flex;align-items: center;gap: 10rpx;white-space: nowrap;">
              <image src="{{imageUrl+'icon/note.png'}}" mode="widthFix" style="height: 40rpx;width: 40rpx;"></image>
            </view>
            <view style="color: red;font-size: 24rpx;">备注：{{ele.order_note}}</view>
          </view>

        </view>

        <view wx:for="{{sortingDetail.service_point_note_list}}" wx:for-item="ele" wx:key="order_id" style="margin-top: 10rpx;font-size: 26rpx;">
          <view wx:if="{{item.order_id == ele.order_id}}" style="display: flex;align-items: center;">
            <view style="display: flex;align-items: center;gap: 10rpx;white-space: nowrap;">
              <image src="/static/point/note.png" mode="widthFix" style="height: 40rpx;width: 40rpx;"></image>
            </view>
            <view style="color: red;font-size: 24rpx;">服务仓备注：{{ele.service_point_note}}</view>
          </view>
        </view>

        <view class="sorting_list">
          <view style="width: 240rpx;">应分拣：{{item.due_num}}件</view>
          <view style="display: flex;align-items: center;margin-left: 30rpx;">
            <view>分拣数：</view>
            <van-stepper show-plus="{{true}}" disabled="{{item.has_quality==false||item.has_ship||!sortingDetail.quality_has}}" data-info="{{item}}" data-index="{{index}}" show-minus="{{false}}" integer min="0" max="{{item.due_num}}" input-width="40" value="{{item.sort_num}}" bind:change="sortingNum" />
            <van-tag wx:if="{{item.has_ship==true}}" type="success">已发货</van-tag>
          </view>
        </view>
        <view class="sorting_list" wx:if="{{sortingDetail.is_check_weight}}">
          <view style="width: 240rpx;">应称重：{{item.due_weight}}kg</view>
          <view style="display: flex;align-items: center;margin-left: 30rpx;">
            <view>称重值：</view>
            <input type="digit" class="{{(item.has_quality==false||item.has_ship||!sortingDetail.quality_has)?'weight_input':''}}" disabled="{{item.has_quality==false||item.has_ship||!sortingDetail.quality_has}}" style="width:120rpx;text-align: center;border: 1rpx solid #f2f3f5;height: 56rpx;" data-info="{{item}}" data-index="{{index}}" placeholder="" value="{{item.sort_weight}}" bindinput="sortingWeight" />
            kg
          </view>
        </view>
        <view wx:if="{{item.warn_sort_weight===true}}" style="display: inline-flex;margin-top: 10rpx;">
          <view>称重差异超过20%</view>
          <view style="margin-left: 20rpx;">
            <van-button type="danger" size="mini" data-info="{{item}}" data-index="{{index}}" bindtap="secondConfirm">二次确认</van-button>
          </view>
        </view>

        <view wx:if="{{item.sort_user_name !== ''}}" style="margin-top: 10rpx;">{{item.sort_user_name}}：{{item.sort_user_mobile}}</view>

        <view>
          <van-divider contentPosition="left" customStyle="color: #1989fa; border-color: #1989fa; font-size: 28rpx;margin:0">品控图</van-divider>
          <view>
            <view style="font-size: 22rpx;color: #aaaaaa;margin-bottom: 10rpx;">上传即生效，不需要确认</view>
            <van-uploader bind:delete="deletePhoto" file-list="{{ item.photo_preview_list }}" data-index="{{index}}" max-count="3" name="photo" accept="image" bind:after-read="uploadPhoto" />
          </view>
        </view>
      </view>
    </view>

  </view>
  <!-- &&!sortingDetail.sort_has -->
  <view class="sorting_button_wrap" wx:if="{{sortingDetail.quality_has&&!readOnly}}">
    <view class="{{isSorting==true?'sorting_buttons':'sorting_button'}}" wx:if="{{showSortBtn}}" bindtap="sorting">
      确定
    </view>
    <view class="sorting_button to-confirm" wx:if="{{!showSortBtn}}">
      待二次确认
    </view>
  </view>

  <view style="margin: 100rpx 20rpx;font-size: 28rpx;">
    <view style="font-weight: 600;">
      提示：
    </view>
    <view style="margin:10rpx 0">
      1. 分拣不会更新已发货订单的数量和重量
    </view>
    <view>
      2. 称重值与应称重差异超过20%，需二次确认才能分拣
    </view>
  </view>
  <view style="width:100%;height: 300rpx;"></view>


  <van-action-sheet style="margin-bottom: 90rpx;" show="{{ showOther }}" bind:close="onCloseOther" close-on-click-overlay>
    <view style="width: 400rpx;margin: 20rpx auto;">
      <van-button type="info" block plain round bind:tap="mergeOnePrint">合单打印</van-button>
    </view>
  </van-action-sheet>

  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>