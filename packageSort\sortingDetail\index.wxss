.sorting_detail {
  padding: 30rpx;
}

.sorting_order_list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sorting_list {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.sorting_list view {
  margin-right: 10rpx;
}

.sorting_button_wrap {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.sorting_button {
  width: 200rpx;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #FC4840;
  border-radius: 8rpx;
  color: #ffffff;
}

.sorting_buttons {
  width: 200rpx;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #999999;
  border-radius: 8rpx;
  color: #ffffff;
}

.to-confirm {
  background-color: #868585;

}

.weight_input {
  background-color: #f2f3f5;
  border: 1rx solid #ffffff !important;
}

.font_color {
  font-size: 30rpx;
  color: #333333;
}

.sort-input {
  width: 90rpx;
  padding: 6rpx;
  background-color: #f2f3f5;
}