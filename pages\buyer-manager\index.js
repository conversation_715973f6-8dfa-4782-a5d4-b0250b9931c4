import {
  manager_create,
  manager_list,
  manager_delete
} from '../../apis/manager';

Page({
  data: {
    customerList: [],
    page: 1,
    isEnd: true,
    showItemActions: false,
    showDeleteDialog: false,
    showAddForm: false,
    currentCustomerId: null,
    formData: {
      name: '',
      phone: ''
    },
    itemActions: [{
      name: '删除',
      color: '#ee0a24'
    }]
  },

  onLoad() {
    this.getCustomerList()
  },

  // 获取客户列表
  getCustomerList() {
    if (!this.data.isEnd) {
      return
    }
    let page = this.data.page++
    let data = {
      Page: page,
      limit: 10,
    }
    manager_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data.list !== null) {
          list = res.data.data.list
        }
        let newList = [...this.data.customerList, ...list]
        this.setData({
          customerList: newList,
          isEnd: this.data.customerList.length < res.data.data.count
        })
      }
    })

  },

  toManager(e) {
    let id = e.currentTarget.dataset.id
    let param = 'point'
    wx.navigateTo({
      url: '/pages/buyerLink/index?param=' + param + '&id=' + id,
    })
  },

  inputName(e) {
    this.setData({
      "formData.name": e.detail.value
    })
  },

  inputPhone(e) {
    let phoneNumber = (e.detail.value).replace(/\s+/g, "");
    phoneNumber = phoneNumber.slice(0, 11) // t
    this.setData({
      'formData.phone': phoneNumber
    })
  },

  // 显示新增表单
  showAddForm() {
    this.setData({
      showAddForm: true,
      formData: {
        name: '',
        phone: ''
      }
    })
  },

  // 确认新增
  confirmAdd() {
    const {
      formData,
    } = this.data
    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return
    }

    if (!formData.phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    // 这里应该调用添加API
    const newCustomer = {
      user_name: formData.name,
      mobile: formData.phone
    }
    manager_create(newCustomer).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })
        this.setData({
          page: 1,
          customerList: [],
          showAddForm: false
        })

        setTimeout(() => {
          this.getCustomerList()
        }, 200);
      }
    }).catch(err=>{
      wx.showToast({
        title: err.data.message ,
        icon: 'none'
      })
    }).finally(()=>{
     
    })
  },

  // 取消新增
  cancelAdd() {
    this.setData({
      showAddForm: false
    })
  },

  // 显示单个客户的操作菜单
  showItemActions(e) {
    const customerId = e.currentTarget.dataset.id
    this.setData({
      showItemActions: true,
      currentCustomerId: customerId
    })
  },

  // 关闭单个客户操作菜单
  onItemActionsClose() {
    this.setData({
      showItemActions: false
    })
  },

  // 选择单个客户操作
  onItemActionSelect(event) {
    const {
      name
    } = event.detail
    if (name === '删除') {
      this.setData({
        showDeleteDialog: true
      })
    }
    this.onItemActionsClose()
  },

  // 确认删除
  confirmDelete() {
    const {
      currentCustomerId,
    } = this.data

    let data = {
      user_id: currentCustomerId
    }
    manager_delete(data).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        this.setData({
          customerList: [],
          page: 1,
          showDeleteDialog: false,
          currentCustomerId: null
        })
        this.getCustomerList()
      }
    }).catch(err=>{
      wx.showToast({
        title: err.data.message ,
        icon: 'none'
      })
    })
  },

  // 取消删除
  cancelDelete() {
    this.setData({
      showDeleteDialog: false,
      currentCustomerId: null
    })
  },

  onReachBottom() {
    this.getCustomerList()
  },
})