<!-- 客户经理页面 -->
<view class="container">
  <view class="content">
    <!-- 客户列表 -->
    <view class="customer-list">
      <block wx:for="{{customerList}}" wx:key="id">
        <view class="customer-item">
          <view class="customer-info" bind:tap="toManager" data-id="{{item.user_id}}">
            <view class="name">{{item.user_name}}</view>
            <view class="phone">{{item.mobile}}</view>
          </view>
          <view class="item-actions">
            <van-icon name="more-o" size="20px" color="#999" data-id="{{item.id}}" bindtap="showItemActions" />
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="footer">
    <view class="add-btn" bindtap="showAddForm">新增客户经理</view>
  </view>
</view>

<!-- 单个客户操作弹出层 -->
<van-action-sheet
  show="{{ showItemActions }}"
  actions="{{ itemActions }}"
  bind:close="onItemActionsClose"
  bind:select="onItemActionSelect"
  cancel-text="取消"
  bind:cancel="onItemActionsClose"
/>

<!-- 新增客户经理弹框 -->
<van-dialog
  use-slot
  title="新增客户经理"
  show="{{ showAddForm }}"
  show-cancel-button
  bind:confirm="confirmAdd"
  bind:cancel="cancelAdd"
  confirm-button-text="确定"
>
  <view class="form-item">
    <view class="form-label">姓名</view>
    <input class="form-input" model:value="{{ formData.name }}" bindinput="inputName" placeholder="请输入姓名" />
  </view>
  <view class="form-item">
    <view class="form-label">手机号</view>
    <input class="form-input" model:value="{{ formData.phone }}" type="number" bindinput="inputPhone" maxlength="11" placeholder="请输入手机号" />
  </view>
</van-dialog>

<!-- 删除确认弹窗 -->
<van-dialog
  id="van-dialog"
  title="确认删除"
  message="确定要删除该客户吗？"
  show="{{ showDeleteDialog }}"
  show-cancel-button
  confirm-button-text="删除"
  confirm-button-color="#ee0a24"
  bind:confirm="confirmDelete"
  bind:cancel="cancelDelete"
/> 