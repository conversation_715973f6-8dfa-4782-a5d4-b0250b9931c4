.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

.customer-list {
  margin-top: 20rpx;
  padding-bottom: 120rpx;
}

.customer-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-info {
  flex: 1;
}

.customer-info .name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.customer-info .phone {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.item-actions {
  padding: 10rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.add-btn {
  background: #07c160;
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 30rpx;
  width: 100%;
}

/* 表单样式 */
.form-item {
  padding: 20rpx 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
} 