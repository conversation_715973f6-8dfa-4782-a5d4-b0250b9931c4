import {
  manager_link_list,
  manager_link_search,
  manager_link_create,
  manager_link_delete
} from '../../apis/manager'
import {
  userIDKey,
} from '../../utils/dict.js';
Page({

  data: {
    page: 1,
    isEnd: true,
    list: [],
    param: '',
    user_id: '',
    show: false,
    content: '',
    search_list: [],
    radio: '',
    showUnbindConfirm: false, // 新增解除绑定弹框显示状态
    unbindId: null, // 新增解除绑定的客户ID
  },

  onLoad(options) {
    let id = wx.getStorageSync(userIDKey)
    if (options.id) {
      id = options.id
    }
    this.setData({
      param: options.param,
      user_id: id
    })
    this.getManagerList()
  },

  // 获取客户列表
  getManagerList() {
    if (!this.data.isEnd) {
      return
    }
    let page = this.data.page++
    let user_id = this.data.user_id
    let data = {
      page: page,
      user_id: user_id,
      limit: 20,
    }
    manager_link_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data.list !== null) {
          list = res.data.data.list
        }
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          isEnd: this.data.list.length < res.data.data.count
        })
      }
    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: 'none'
      })
    }).finally(() => {

    })

  },


  // 绑定客户
  showAddForm() {
    this.setData({
      show: true
    })
  },
  inputName(e) {
    this.setData({
      content: e.detail.value
    })
  },

  handleSearch() {
    if (this.data.content == '') {
      wx.showToast({
        title: '请输入会员名',
        icon: 'none'
      })
      return
    }
    let data = {
      content: this.data.content,
      limit: 10,
      page: 1
    }
    manager_link_search(data).then(res => {
      let list = []
      if (res.data.data.list !== null) {
        list = res.data.data.list
      }
      let newList = [...this.data.search_list, ...list]
      this.setData({
        search_list: newList,
      })
    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: 'none'
      })
    }).finally(() => {

    })
  },


  onChange(e) {
    let id = e.currentTarget.dataset.id
    this.setData({
      radio: id
    })
  },

  confirmAdd() {
    if (this.data.radio == '') {
      wx.showToast({
        title: '请先搜索',
        icon: "none"
      })
      return
    }
    let data = {
      buyer_id: this.data.radio,
      buyer_manager_user_id: this.data.user_id
    }
    manager_link_create(data).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '绑定成功',
        })
        this.setData({
          show: false,
          list: [],
          page: 1,
          isEnd: true,
          radio: '',
          search_list: [],
          content: ''
        })
        this.getManagerList()
      }
    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: 'none'
      })
    }).finally(() => {

    })
  },

  cancelAdd() {
    this.setData({
      show: false,
      page: 1,
      isEnd: true,
    })
  },

  onShow() {

  },

  onReachBottom() {
    this.getManagerList()
  },

  // 显示解除绑定确认弹框
  showUnbindConfirm(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showUnbindConfirm: true,
      unbindId: id,
    });
  },

  // 取消解除绑定
  cancelUnbind() {
    this.setData({
      showUnbindConfirm: false,
      unbindId: null,
    });
  },

  // 确认解除绑定
  confirmUnbind() {
    const id = this.data.unbindId;
    let data = {
      buyer_id: id
    }

    manager_link_delete(data).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '解绑成功',
        })

        this.setData({
          showUnbindConfirm: false,
          unbindId: null,
          page: 1,
          list: []
        });
        setTimeout(() => {
          this.getManagerList()
        }, 200);
      }
    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: 'none'
      })
    }).finally(() => {

    })

  },

  showOrder(event) {
    const customerId = event.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/buyerOrder/index?id=${customerId}`,
    });
  }
})