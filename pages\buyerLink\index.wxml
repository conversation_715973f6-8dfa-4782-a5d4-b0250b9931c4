<view class="container">
  <!-- 客户列表 -->
  <view class="customer-list">
    <block wx:for="{{list}}" wx:key="id">
      <view class="customer-info">
        <view class="customer-details">
          <view class="name">{{item.buyer_name}}</view>
          <view class="phone">{{item.mobile}}</view>
        </view>
        <view class="unbind-btn" bindtap="showUnbindConfirm" data-id="{{item.id}}" wx:if="{{param == 'point'}}">解绑</view>
        <!-- 新增 查看订单 按钮 -->
        <view class="order-btn" bindtap="showOrder" data-id="{{item.id}}">订单</view>
      </view>
    </block>
  </view>

  <!-- 底部操作栏 -->
  <view class="footer" wx:if="{{param == 'point'}}">
    <view class="add-btn" bindtap="showAddForm">绑定客户</view>
  </view>
</view>

<!-- 绑定客户弹框 -->
<van-dialog use-slot title="绑定客户" show="{{ show }}" show-cancel-button bind:confirm="confirmAdd" bind:cancel="cancelAdd" confirm-button-text="确定">
  <view class="form-item">
    <view class="search">
      <input class="form-input" model:value="{{ content }}" bindinput="inputName" placeholder="请输入姓名" />
      <view class="form-label" catch:tap="handleSearch">搜索</view>
    </view>
    <view class="page-section-spacing">
      <view>
        <view wx:for="{{search_list}}" wx:key="index" class="item">
          <van-radio-group value="{{ radio }}" disabled="{{item.buyer_manager_user_name}}"  data-id="{{item.id}}" bind:change="onChange">
            <van-radio name="1">
              <view>
                <view>{{item.buyer_name}}</view>
                <view>{{item.mobile}}</view>
              </view>
            </van-radio>
          </van-radio-group>
        </view>
      </view>
    </view>
  </view>
</van-dialog>

<!-- 解除绑定确认弹框 -->
<van-dialog wx:if="{{param == 'point'}}" id="unbindDialog" use-slot title="确认解除绑定" show="{{ showUnbindConfirm }}" show-cancel-button bind:confirm="confirmUnbind" bind:cancel="cancelUnbind" confirm-button-text="确定">
  <view style="padding: 40rpx 20rpx;text-align: center;">您确定要解除该客户的绑定吗？</view>
</van-dialog>