.container{
  background-color: #f7f7f7;
  min-height: 95vh;
}

.customer-list {
  padding: 20rpx;
  padding-top: 10rpx;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.customer-info {
  background-color: #fff;
  padding: 20rpx; /* 增加内边距 */
  box-sizing: border-box;
  border-radius: 10rpx;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  border: 1px solid #e0e0e0; /* 增加边框 */
}

.customer-details {
  flex: 1;
}

.customer-info .name {
  font-size: 32rpx; /* 增加字体大小 */
  font-weight: bold;
  color: #333;
}

.customer-info .phone {
  font-size: 28rpx; /* 增加字体大小 */
  color: #666;
  margin-top: 10rpx;
}

.unbind-btn {
  align-self: flex-end;
  background-color: #ff4d4f; /* 增加背景颜色 */
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  border: none;
  font-size: 24rpx; /* 缩小字体 */
  opacity: 0.7; /* 弱化显示 */
}

.order-btn {
  align-self: flex-end;
  background-color: #68a5f3;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  border: none;
  font-size: 24rpx;
  opacity: 0.7;
  margin-left: 10rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.add-btn {
  background: #07c160;
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 30rpx;
  width: 100%;
}

.form-item {
  height: 50vh;
  padding: 20rpx;
}

.search {
  border: 1px solid #d6cccc;
  border-radius: 20rpx;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.page-section-spacing {
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f7f7f7;
  margin-top: 10rpx;
  height: 660rpx;
  overflow: hidden;
  overflow-y: scroll;
}

.form-input {
  flex: 1;
}

.form-label {
  background-color: orange;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  color: #fff;
}

.item {
  background-color: #fff;
  margin-top: 10rpx;
  font-size: 24rpx;
  padding: 10rpx;
  border-radius: 20rpx;
}

/* 新增解绑弹框样式 */
#unbindDialog {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

#unbindDialog .van-dialog__content {
  padding: 20rpx;
}