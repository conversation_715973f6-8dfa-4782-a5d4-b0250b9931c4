import {
  manager_buyer_list,
} from '../../apis/manager'
const app = getApp()
import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
} from '../../utils/dict';

Page({
  data: {
    buyer_id: '',
    page: 1,
    isEnd: false,
    list: [],
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        buyer_id: options.id
      });
      this.buyerOrderList(options.id);
    }
  },

  buyerOrderList(buyer_id) {
    if (this.data.isEnd) {
      return
    }
    let page = this.data.page++
    let data = {
      page: page,
      buyer_id: buyer_id,
      limit: 6,
    }
    manager_buyer_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list) {
          list = []
        } else {
          list.forEach((item) => {
            item.created_at_show = dealTimeFormat1(item.created_at)
            item.order_status_show = orderStatus(item.order_status)
            item.product_list.forEach(items => {
              items.prices = items.price / 100
            })
          })
        }
        this.data.page++
        this.data.isScrolling = false;
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          isEnd: list.length === 0,
        })
      }
    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: 'none'
      })
    }).finally(() => {

    })
  },

  toOrderDetail(e) {
    let id = e.currentTarget.dataset.info.id
    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id,
    })
  },
   onReachBottom() {
    this.buyerOrderList(this.data.buyer_id)
  },
});
