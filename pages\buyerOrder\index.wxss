

.con {
  padding: 20rpx;
  background-color: #ececec;
}

.order {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
}

.shop_title {
  display: flex;
  justify-content: space-between;
  padding-right:20rpx;
  gap: 20rpx;
}

.order-time {
  font-size: 24rpx;
}

.order-con {
  display: flex;
}

.product-list {
  /* flex: 1; */
  /* display: flex; */
  /* flex-wrap: nowrap; */
  /* flex-shrink: 0; */
  width: 100%;
  overflow: scroll;
  /* box-sizing: border-box; */
}

.order-con .right {
  padding: 10rpx;
  display: flex;
  align-items: center;
}

.goods_cover {
  width: 120rpx;
  height: auto;
  margin: 10rpx;
  border-radius: 22rpx;
}

.per {
  display: flex;
  /* border: 1px solid red; */
  margin-top: 10rpx;
}

.left {
  width: calc(100vw - 180rpx);
  padding: 10rpx 0;
  box-sizing: border-box;
}

.titleName {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-size: 28rpx;
}
