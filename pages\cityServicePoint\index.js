import {
  point_order_list
} from '../../apis/servicePoint/center';
import dayjs from "../../libs/dayjs"
import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
} from '../../utils/dict';
const app = getApp()

import regeneratorRuntime from 'regenerator-runtime'

Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    list: [],
    page: 1,
    show: false,
    time_range: [],
    time_begin: 0,
    time_end: 0,
    is_end: false,
    minDate: '',
    maxDate: '',
    id: '',
    ordertypeList: [{
        id: 1,
        title: "订单"
      },
      {
        id: 2,
        title: "送货到店"
      }
    ],
    active: 0,
  },

  async onLoad(options) {
    this.setData({
      id: options.id
    })
    await this.timeRange()
    await this.getPointOrderList()
  },

  timeRange() {
    return new Promise((resolve) => {
      let now = dayjs()
      let minDate = now.subtract(1, "month").startOf('day').valueOf()
      let maxDate = now.endOf('day').valueOf()
      let time_begin = now.subtract(0, "day").startOf('day').valueOf()
      let time_range = [time_begin, maxDate]

      this.setData({
        date: `${this.dealTimeToDay(time_begin) }-${ this.dealTimeToDay(maxDate)}`,
        page: 1,
        time_range: time_range,
        time_begin: time_begin,
        time_end: maxDate,
        minDate,
        maxDate
      });

      resolve()
    })

  },
  dealTimeToDay(at) {
    return dayjs(at).format('MM/DD')
  },
  onDisplay() {
    this.setData({
      show: true
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },
  onConfirm(event) {
    const [start, end] = event.detail;
    let time_begin = start.getTime()
    let time_end = dayjs(end.getTime()).endOf('day').valueOf()
    let time_range = [time_begin, time_end]

    this.setData({
      show: false,
      page: 1,
      list: [],
      date: `${this.dealTimeToDay(start)}-${this.dealTimeToDay(time_end)}`,
      time_begin,
      time_range,
      time_end,
      is_end: false
    });
    //  查询列表
    this.getPointOrderList()
  },

  switchTab(e) {
    let status = e.detail.name
    this.setData({
      active: status,
      page: 1,
      is_end: false,
      list: []
    })
    if (this.data.active == 0) {
      this.getPointOrderList()
    }
  },


  getPointOrderList() {
    return new Promise((resolve) => {
      if (this.data.is_end) return

      let data = {
        begin: this.data.time_begin,
        end: this.data.time_end,
        second_point_id: this.data.id,
        page: this.data.page,
        limit: 6
      }
      point_order_list(data).then(res => {
        if (res.data.code == 0) {
          let list = res.data.data.list
          let count = res.data.data.count

          if (!list) {
            list = []
          }

          list.forEach(item => {
            item.created_at_show = dealTimeFormat1(item.created_at)
            item.order_status_show = orderStatus(item.order_status)
            item.product_list.forEach(items => {
              items.prices_fmt = items.price / 100
            })
          })

          //   count
          let is_end =  false

          if (this.data.page * 6 < count) {
            this.data.page++
          }else{
            is_end = true
          }

          const res_list = [...this.data.list, ...list]
          this.setData({
            list: res_list,
            is_end: is_end,
          })
        }
      }).catch(err => {

      }).finally(() => {
        resolve()
      })
    })
  },

  toOrderDetail(e) {
    let id = e.currentTarget.dataset.info.id
    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id,
    })
  },

  onShow() {

  },

  onReachBottom() {
    this.getPointOrderList()
  },

})