<view style="background-color: #f7f7f7;">

  <van-sticky offset-top="{{ 0 }}">
    <van-tabs active="{{ active }}" sticky bind:click="switchTab" z-index="99">
      <van-tab wx:for="{{ordertypeList}}" wx:key="key" title="{{item.title}}"></van-tab>
    </van-tabs>
    <view class="time-class" wx:if="{{active == 0}}">
      <view bindtap="onDisplay">时间：{{date}}</view>
    </view>
  </van-sticky>

  <view class="con" wx:if="{{active == 0}}">
    <view wx:for="{{list}}" wx:key="key">
      <view class="order" data-info="{{item}}" bind:tap="toOrderDetail">
        <view class="shop_title" >
          <view style="display: flex;gap: 20rpx;align-items: center;">
            <text style="font-size: 28rpx;">{{item.buyer_name}}</text>
          </view>
          <view class="tag-one" wx:if="{{item.order_status== 3}}">
            {{item.order_status_show}}
          </view>

          <view class="tag-two" wx:if="{{item.order_status===1||item.order_status===2}}">
            {{item.order_status_show}}
          </view>

        </view>
        <view class="order-con">
          <view class="product-list">
            <block wx:for="{{item.product_list}}" wx:key="key" wx:for-item="items">
              <view class="per">
                <image class="goods_cover" src="{{items.product_cover_img.name?imageUrl+categoryCoverProcess + items.product_cover_img.name:''}}" mode="widthFix" />
                <view class="left">
                  <view class="titleName">{{items.product_title}}</view>
                  <view style="display: flex; justify-content: space-between; margin-top: 10rpx;">
                    <text style="color: #f7ab1e;">￥{{items.prices_fmt}}</text>
                    <text>x{{items.num}}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
        <view class="order-time">
          下单时间: {{item.created_at_show}}
        </view>
      </view>
    </view>
    <view style="height: 100rpx;">
    </view>
  </view>
</view>

<van-calendar class="calendar" show="{{ show }}" default-date="{{ time_range}}" max-date="{{maxDate}}" min-date="{{ minDate }}" show-confirm="{{false}}" show-title="{{false}}" type="range" bind:close="onClose" bind:confirm="onConfirm" allow-same-day="true" />