.calendar .van-popup {
  height: 400px !important;
}

.nav {
  background-color: #fff;
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  padding-left: 30rpx;
}

.con {
  padding: 20rpx;
  /* margin-top: 170rpx; */
}


.time-class {
  background-color: #fff;
  padding: 20rpx 0 20rpx 20rpx;
}

.order {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
}

.shop_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;
}

.order-time {
  font-size: 24rpx;
}

.order-con {
  display: flex;
}

.product-list {
  width: 100%;
  overflow: scroll;
}

.order-con .right {
  padding: 10rpx;
  display: flex;
  align-items: center;
}

.goods_cover {
  width: 120rpx;
  height: auto;
  margin: 10rpx;
  border-radius: 22rpx;
}

.per {
  display: flex;
  margin-top: 10rpx;
}

.left {
  width: calc(100vw - 180rpx);
  padding: 10rpx 0;
  box-sizing: border-box;
}

.titleName {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-size: 28rpx;
}

.capsule-box {
  display: flex;
}

.calendar .van-popup {
  height: 400px !important;
}

.tag-one,.tag-two{
  font-size: 24rpx;
  padding: 2rpx 10rpx;
  color: #fff;
  border-radius: 10rpx;
}

.tag-one{
  background: #07c160;
}

.tag-two{
  background: #cfcfcf;
}