import {
  refund_count,
  product_count,
  integral_num,
  count_not_confirm,
  is_delivery_man,
  serviceWarning,
  getManager,
} from "../../apis/index";

import { getByUser } from "../../apis/admin";

import { get_service_point_detail } from "../../apis/servicePoint/center";

import { send_code, login, get_supplier } from "../../utils/api";

import {
  token<PERSON>ey,
  userIDKey,
  refreshToken<PERSON>ey,
  expiresTokenTime<PERSON>ey,
  rememberAccount<PERSON>ist<PERSON><PERSON>,
  user<PERSON>ey,
  roleKey,
  authKey,
  supplierIDKey,
  supplierKey,
  servicePointIDKey,
} from "../../utils/dict.js";
import { checkPrivacy } from "../../utils/check";
const app = getApp();
import Toast from "@vant/weapp/toast/toast";
import dayjs from "../../libs/dayjs";
import Dialog from "@vant/weapp/dialog/dialog";

import regeneratorRuntime from "regenerator-runtime";

Page({
  data: {
    imageUrl: app.globalData.imageUrl,
    show: false,
    currentTime: "获取验证码", //倒计时
    mobile: "",
    pwd: "",
    disabled: false, //发送验证码按钮禁用
    status: "未认证",
    keyboardheight: "",
    warehouse: false,
    showPrivacy: false,
    user_info: {}, // 用户信息
    role_list: [],
    auth_list: [],
    checked: true,
    newUserID: "", //当前登录账号
    linkCount: 0,
    refund_count: 0, //地址反馈
    product_count: 0, //商品审核数字
    integral_count: 0, //积分订单
    service_point_detail: {},
    show_point: false,
    loading: false,
    phoneParam: app.globalData.phoneParam,
    is_form: false,
    not_confirm_num: 0,
    version: "",
    delivery_man: false,
    show_manager: false,
    show_purchase: false,
    is_station: false,
    station_info: {},
    supplier_info: null,
    account: 0,
    service_warning_num: 0,
    remember_account_list: [],
    show_after_sale: false, // 售后审核
    show_product_audit: false, // 商品审核
    show_task: false,
  },

  async onLoad() {
    checkPrivacy();
    await this.checkRememberAccountList();
    this.initQualityTime();
    this.getMiniProgram();
    this.confirmIsDeliveryMan();

    if (wx.getStorageSync(tokenKey)) {
      this.setData({
        loading: true,
      });
      await this.initData();
      this.getPointNotConfirmNUm();
      this.getServiceWarning();
      await this.checkAuth();
      this.queryCount();

      this.setData({
        loading: false,
      });
    }
  },

  onShow() {
    if (this.data.show_task) {
      this.queryCount()
    }
  },

  queryCount() {
    this.refundCount();
    this.productCount();
  },

  //  检查登录列表过期时间
  checkRememberAccountList() {
    return new Promise((callback) => {
      let list = wx.getStorageSync(rememberAccountListKey) || [];
      this.setData({
        remember_account_list: list,
      });

      callback();
    });
  },

  async checkAuth() {
    return new Promise(async (callback) => {
      await this.getUserInfo();

      // 如果role_list中包含superAdmin，则show_purchase为true
      let role_list = wx.getStorageSync(roleKey) || [];
      let auth_list = wx.getStorageSync(authKey) || [];

      let show_purchase = false;
      if (role_list.includes("superAdmin")) {
        show_purchase = true;
      }

      let show_after_sale = false;
      if (auth_list.includes("normalAdmin:afterSale:audit")) {
        show_after_sale = true;
      }

      let show_product_audit = false;
      if (auth_list.includes("normalAdmin:product:audit")) {
        show_product_audit = true;
      }

      let show_task = false;
      // 只要show_purchase，show_after_sale，show_product_audit存在一个true，show_task=true
      if (show_purchase || show_after_sale || this.data.show_product_audit) {
        show_task = true;
      }

      this.setData({
        show_task: show_task,
        show_purchase: show_purchase,
        show_after_sale: show_after_sale,
        show_product_audit: show_product_audit,
      });
      callback();
    });
  },

  toPurchase() {
    wx.navigateTo({
      url: "/pages/procure/index?from=index",
    });
  },

  // 是否为配送员
  confirmIsDeliveryMan() {
    if (!wx.getStorageSync(tokenKey)) {
      this.setData({
        delivery_man: false,
      });
      return;
    }
    let data = {
      user_id: wx.getStorageSync(userIDKey),
    };
    is_delivery_man(data)
      .then((res) => {
        if (res.data.code == 0) {
          this.setData({
            delivery_man: true,
          });
        }
      })
      .catch((err) => {
        this.setData({
          delivery_man: false,
        });
      });
  },

  // 查询客户经理
  queryManager() {
    if (!wx.getStorageSync(tokenKey)) {
      this.setData({
        show_manager: false,
      });
      return;
    }
    let data = {
      user_id: wx.getStorageSync(userIDKey),
    };
    getManager(data)
      .then((res) => {
        if (res.data.code == 0) {
          this.setData({
            show_manager: true,
          });
        }
      })
      .catch((err) => {
        this.setData({
          show_manager: false,
        });
      });
  },

  // 当前小程序版本
  getMiniProgram() {
    let accountInfo = wx.getAccountInfoSync();
    const version = accountInfo.miniProgram.version;
    this.setData({
      version,
    });
  },

  // 品控中心待确认数字
  getPointNotConfirmNUm() {
    if (!wx.getStorageSync(servicePointIDKey)) {
      return;
    }
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
    };
    count_not_confirm(data)
      .then((res) => {
        if (res.data.code == 0) {
          this.setData({
            not_confirm_num: res.data.data,
          });
        }
      })
      .catch((err) => {
        Toast(err.data.message);
      });
  },

  // 品控中心待确认数字
  getServiceWarning() {
    if (!wx.getStorageSync(servicePointIDKey)) {
      return;
    }
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
    };
    serviceWarning(data)
      .then((res) => {
        if (res.data.code == 0) {
          this.setData({
            service_warning_num: res.data.data,
          });
        }
      })
      .catch((err) => {
        Toast(err.data.message);
      });
  },

  initQualityTime() {
    let now = dayjs();
    app.globalData.quality_time = now.valueOf();
    app.globalData.delivery_time = now.valueOf();
  },

  initData() {
    return new Promise(async (callback) => {
      // 初始化数据
      await this.checkSupplier();
      await this.servicePointDetail();
      callback();
    });
  },

  handleLogin() {
    this.setData({
      is_form: true,
    });
  },
  backLogin() {
    this.setData({
      is_form: false,
    });
  },

  handleOut() {
    Dialog.confirm({
      title: "提示",
      message: "确认退出?",
    })
      .then(async () => {
        wx.clearStorageSync();
        wx.reLaunch({
          url: "/pages/index/index",
        });
      })
      .catch(() => {
        // on cancel
      });
  },

  // 基本信息
  toMine() {
    wx.navigateTo({
      url: "/pages/servicePoint/mine/index",
    });
    app.globalData.env = 3;
  },

  productRemove() {
    wx.navigateTo({
      url: "/pages/index/remove/index",
    });
  },

  // 中心仓
  servicePointDetail() {
    return new Promise((callback) => {
      let data = {
        user_id: wx.getStorageSync(userIDKey),
        level: "first",
      };
      get_service_point_detail(data)
        .then((res) => {
          if (res.data.code == 0) {
            let d = res.data.data;
            wx.setStorageSync(servicePointIDKey, d.id);
            this.setData({
              service_point_detail: d,
              show_point: true,
            });
          }
        })
        .catch((err) => {
          //  异常
          this.setData({
            show_point: false,
          });
          if (err.data.code == 4002) {
            // 非服务仓,不处理
          } else {
            wx.showToast({
              title: err.data.message,
              icon: "none",
              duration: 1000,
            });
          }
        })
        .finally(() => {
          callback();
        });
    });
  },

  // 审核地址数
  addressCount() {
    address_count().then((res) => {
      if (res.data.code == 0) {
        this.setData({
          address_count: res.data.data,
        });
      }
    });
  },

  // 审核会员数
  refundCount() {
    if (!wx.getStorageSync(servicePointIDKey)) {
      return;
    }
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
    };
    refund_count(data).then((res) => {
      if (res.data.code == 0) {
        this.setData({
          refund_count: res.data.data,
        });
      }
    });
  },

  // 总审核数
  // allCount() {
  //   if (!wx.getStorageSync(servicePointIDKey)) {
  //     return
  //   }
  //   let data = {
  //     service_point_id: wx.getStorageSync(servicePointIDKey)
  //   }
  //   all_count(data).then(res => {
  //     if (res.data.code == 0) {
  //       this.setData({
  //         all_count: res.data.data,
  //       })
  //     }
  //   })
  // },

  // 商品审核数
  productCount() {
    if (!wx.getStorageSync(servicePointIDKey)) {
      return;
    }
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
    };
    product_count(data).then((res) => {
      if (res.data.code == 0) {
        this.setData({
          product_count: res.data.data,
        });
      }
    });
  },

  // 积分订单
  integralCount() {
    if (!wx.getStorageSync(servicePointIDKey)) {
      return;
    }

    integral_num().then((res) => {
      if (res.data.code == 0) {
        this.setData({
          integral_count: res.data.data,
        });
      }
    });
  },

  changeMobile(e) {
    this.setData({
      mobile: e.detail,
    });
  },

  open() {
    wx.openPrivacyContract({
      success: (res) => { },
    });
  },

  keyboardheight(e) {
    this.setData({
      keyboardheight: e.detail.height,
    });
  },

  login() {
    let that = this;
    if (this.data.mobile == "") {
      Toast("请输入电话号码");
      return;
    }

    if (this.data.pwd == "") {
      Toast("请填写密码");
      return;
    }

    this.setData({
      loading: true,
    });

    let data = {
      mobile: this.data.mobile,
      pwd: this.data.pwd,
    };

    login(data)
      .then(async (res) => {
        if (res.data.code == 0) {
          let info = res.data.data;

          let role_info = info.role_info;
          if (!role_info) {
            role_info = [];
          }

          let role_list = [];
          let auth_list = [];

          role_info.forEach((e) => {
            role_list.push(e.value);

            e.auth_list.forEach((auth) => {
              auth_list.push(auth.value);
            });
          });

          wx.setStorageSync(tokenKey, info.access_token);
          wx.setStorageSync(userIDKey, info.user_id);
          wx.setStorageSync(userKey, info);
          wx.setStorageSync(roleKey, role_list);
          wx.setStorageSync(authKey, auth_list);
          wx.setStorageSync(expiresTokenTimeKey, info.expires);
          wx.setStorageSync(refreshTokenKey, info.refresh_token);

          // 记录账号
          let remember_account_list =
            wx.getStorageSync(rememberAccountListKey) || [];
          // 如果存在就覆盖，不存在就加入
          const index = remember_account_list.findIndex(
            (item) => item.mobile === data.mobile
          );
          if (index !== -1) {
            // 已存在，覆盖
            remember_account_list[index] = {
              mobile: data.mobile,
              pwd: data.pwd,
            };
          } else {
            // 不存在，加入
            remember_account_list.push({
              mobile: data.mobile,
              pwd: data.pwd,
            });
          }
          wx.setStorageSync(rememberAccountListKey, remember_account_list);

          this.setData({
            loading: true,
            user_info: info,
            role_list: role_list,
            auth_list: auth_list,
            show: false,
            account: wx.getStorageSync("x_supplier").account_status,
            remember_account_list: remember_account_list,
          });

          this.checkSupplier();
          await this.servicePointDetail();
          this.checkAuth();
          this.confirmIsDeliveryMan();
        }
      })
      .catch((err) => {
        console.log(err);
        wx.showToast({
          title: err.data.message,
          icon: "none",
        });
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },

  //  切换
  async switchUser(e) {
    const info = e.currentTarget.dataset.info;
    this.setData({
      mobile: info.mobile,
      pwd: info.pwd,
    });

    this.login();
  },

  // 删除账号
  handleDelUser(e) {
    let info = e.currentTarget.dataset.info;
    let remember_account_list = wx.getStorageSync(rememberAccountListKey) || [];
    let index = remember_account_list.findIndex(
      (item) => item.mobile === info.mobile
    );
    if (index !== -1) {
      remember_account_list.splice(index, 1);
    }

    wx.setStorageSync(rememberAccountListKey, remember_account_list);
    this.setData({
      remember_account_list: remember_account_list,
    });

    let user = wx.getStorageSync(userKey);
    if (user.mobile === info.mobile) {
      wx.clearStorageSync();
      wx.reLaunch({
        url: "/pages/index/index",
      });
    }
  },

  // 展示登录
  showLogin() {
    this.setData({
      is_form: false,
      show: true,
      mobile: "",
      pwd: "",
    });
  },

  // 关闭登录
  closeLogin() {
    this.setData({
      show: false,
      keyboardheight: 0,
    });
  },

  changePwd(e) {
    this.setData({
      pwd: e.detail,
    });
  },

  toPayCenter(e) {
    let from = e.currentTarget.dataset.from;
    if (from == "point") {
      app.globalData.env = 3;
    }
    if (from == "station") {
      app.globalData.env = 7;
    }

    wx.navigateTo({
      url: "/pages/servicePoint/instantDeliver/index",
    });
  },

  toPoster() {
    app.globalData.env = 3;
    wx.navigateTo({
      url: "/pages/servicePoint/poster/index",
    });
  },

  toPromoteNew() {
    app.globalData.env = 3;
    wx.navigateTo({
      url: "/pages/servicePoint/promoteNew/index",
    });
  },

  toSupplier() {
    if (wx.getStorageSync(tokenKey) == "") {
      this.setData({
        is_form: true,
        show: true,
      });
    } else {
      app.globalData.env = 2;
      wx.switchTab({
        url: "/pages/supplier/afterSale/index",
      });
    }
  },

  servicePoint() {
    wx.navigateTo({
      url: "/pages/servicePoint/index",
    });
    app.globalData.env = 3;
  },

  deliverMan() {
    wx.navigateTo({
      url: "/pages/servicePoint/deliverMan/index",
    });
  },

  toManager() {
    let param = "manager";
    wx.navigateTo({
      url: "/pages/buyerLink/index?param=" + param,
    });
  },

  toInstantDeliver() {
    wx.navigateTo({
      url: "/pages/servicePoint/instantDeliver/index",
    });
  },

  memberMan() {
    wx.navigateTo({
      url: "/pkgAdmin/member/index",
    });
  },

  toQuality(e) {
    let from = e.currentTarget.dataset.from;

    if (from == "point") {
      app.globalData.env = 3;
    }
    if (from == "station") {
      app.globalData.env = 7;
    }

    wx.navigateTo({
      url: "/pages/servicePoint/quality/confirm/index",
    });
  },

  // 查询供应商认证
  checkSupplier() {
    return new Promise((callback) => {
      let data = {
        user_id: wx.getStorageSync(userIDKey),
      };
      get_supplier(data)
        .then((res) => {
          if (res.data.code == 0) {
            let d = res.data.data;
            //  供应商信息缓存
            wx.setStorageSync(supplierIDKey, d.id);
            wx.setStorageSync(supplierKey, d);
            this.setData({
              supplier_info: d,
              account: wx.getStorageSync("x_supplier").account_status,
            });
          }
        })
        .catch((err) => {
          if (err.data.code == 4002) {
            wx.removeStorageSync(supplierIDKey);
            wx.removeStorageSync(supplierKey);
            this.setData({
              supplier_info: null,
            });
            return;
          }
          Toast(err.data.message);
        })
        .finally(() => {
          callback();
        });
    });
  },

  agree(e) { },

  disagree(e) { },

  handleToDo(e) {
    console.log(e);
    let id = e.currentTarget.dataset.id;
    switch (id) {
      case "1":
        wx.navigateTo({
          url: "/pkgAdmin/buyer/index",
        });
        break;
      case "2":
        wx.navigateTo({
          url: "/pkgAdmin/afterSale/index",
        });
        break;
      case "3":
        wx.navigateTo({
          url: "/pkgAdmin/product/index",
        });
        break;
      case "4":
        wx.navigateTo({
          url: "/pkgAdmin/integral/index",
        });
        break;
    }
  },
  toUserCenter() {
    let id = this.data.station_info.id;
    wx.navigateTo({
      url: "/pages/index/member/index?id=" + id,
    });
  },

  toOrderCenter() {
    let id = this.data.station_info.id;
    wx.navigateTo({
      url: "/pages/index/orderList/index?id=" + id,
    });
  },

  toManageCenter() {
    wx.navigateTo({
      url: "/pages/index/manage/index",
    });
  },

  async getUserInfo() {
    return new Promise((callback) => {
      let data = {
        user_id: wx.getStorageSync(userIDKey),
      };
      getByUser(data)
        .then((res) => {
          if (res.data.code == 0) {
            let info = res.data.data;

            let role_info = info.role_info;
            if (!role_info) {
              role_info = [];
            }

            let role_list = [];
            let auth_list = [];

            role_info.forEach((e) => {
              role_list.push(e.value);

              e.auth_list.forEach((auth) => {
                auth_list.push(auth.value);
              });
            });

            wx.setStorageSync(roleKey, role_list);
            wx.setStorageSync(authKey, auth_list);
          }
          callback();
        })
        .catch((err) => {
          wx.setStorageSync(roleKey, []);
          wx.setStorageSync(authKey, []);
          callback();
        });
    });
  },

  onShareAppMessage() { },
});
