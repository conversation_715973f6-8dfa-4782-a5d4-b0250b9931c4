<view class="container">
  <view class="tips">
    <view>【果蔬团】管理端</view>
  </view>
  <view bindtap="toSupplier" class="part" wx:if="{{supplier_info}}">
    <view style="display: flex;align-items: center;justify-content: space-between;height:150rpx;">
      <view>
        <view style="font-size: 26rpx;color: #7e7e7e;">供应商</view>
        <view style="font-size: 34rpx;margin-top: 10rpx;">
          {{supplier_info.shop_simple_name}}
          <text wx:if="{{account !==1}}" style="font-size: 22rpx;">(不可用)</text>
        </view>
      </view>
      <view class="supplier_img_wrap">
        <image src="{{imageUrl+'/icon/avatar.png'}}" mode="widthFix" class="supplier_img"></image>
      </view>
    </view>
  </view>
  <view class="part point" wx:if="{{show_point}}">
    <view class="title">{{service_point_detail.name}}</view>
    <view class="two">
      <view class="enter-per" bindtap="toQuality" data-from="{{'point'}}" style="position: relative;">
        <image src="/static/point/quality.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx; color: #000;margin-top: 10rpx;">品控/分拣</view>
        <view class="point-num" wx:if="{{not_confirm_num > 0}}">{{not_confirm_num}}</view>
      </view>
      <view class="enter-per" style="position: relative;" bindtap="toPayCenter" data-from="{{'point'}}">
        <image src="/static/point/delivery.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx; color: #000;margin-top: 10rpx;">交付中心</view>
        <image class="warning-num" wx:if="{{service_warning_num > 0}}" src="/static/point/tip.png" mode="widthFix" style="width: 40rpx;height: 40rpx;"></image>
      </view>
      <view class="enter-per" bindtap="toPoster">
        <image src="/static/point/poster.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx; color: #000;margin-top: 10rpx;">海报</view>
      </view>
      <view class="enter-per" bindtap="toPromoteNew">
        <image src="/static/point/promote_new.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx; color: #000;margin-top: 10rpx;">今日推荐</view>
      </view>
      <view class="enter-per" bindtap="productRemove">
        <image src="/static/point/remove.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx;color: #000;margin-top: 10rpx;">商品下架</view>
      </view>
      <view class="enter-per" bindtap="toMine">
        <image src="/static/point/cert-info.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx;color: #000;margin-top: 10rpx;">管理中心</view>
      </view>
    </view>
  </view>
  <view class="part" wx:if="{{delivery_man||show_manager}}">
    <view class="three">
      <view wx:if="{{delivery_man}}" class="enter-per" bindtap="deliverMan">
        <image src="/static/point/deliver-index.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx;">订单配送</view>
      </view>
      <view wx:if="{{show_manager}}" class="enter-per" bindtap="toManager">
        <image src="/static/point/manager.png" mode="widthFix" style="width: 60rpx;height: 60rpx;"></image>
        <view style="font-size: 28rpx;">客户经理</view>
      </view>
    </view>
  </view>
  <view class="task part" wx:if="{{show_task}}">
    <view class="enter-per" bind:tap="handleToDo" data-id="2" wx:if="{{show_after_sale}}">
      <view class="{{refund_count !== 0?'count': 'not-count'}}">{{refund_count}}</view>
      <view style="font-size: 28rpx;margin-top: 10rpx;">售后审核</view>
    </view>
    <view class="enter-per" bind:tap="handleToDo" data-id="3" wx:if="{{show_product_audit}}">
      <view class="{{product_count !== 0?'count': 'not-count'}}">{{product_count}}</view>
      <view style="font-size: 28rpx;margin-top: 10rpx;">商品审核</view>
    </view>
    <!-- <view class="enter-per" bind:tap="handleToDo" data-id="4">
        <view class="{{integral_count !== 0?'count': 'not-count'}}">{{integral_count}}</view>
        <view style="font-size: 28rpx;margin-top: 10rpx;">积分订单</view>
      </view> -->
    <view class="enter-per" bindtap="toPurchase" wx:if="{{show_purchase}}">
      <image src="/static/point/purchase.png" mode="widthFix" style="width: 40rpx;height: 40rpx;"></image>
      <view style="font-size: 28rpx;margin-top: 10rpx;">采购总览</view>
    </view>
  </view>
  <view class="switch_account">
    <view style="display: flex;margin-top: 30rpx;">
      <view bindtap="showLogin">
        <view wx:if="{{remember_account_list.length > 0}}">切换账号登录</view>
        <view wx:else>登录</view>
      </view>
      <van-icon name="arrow" />
    </view>
    <view style="margin-top: 10rpx;" wx:if="{{version}}">v{{version}}</view>
  </view>
</view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" showCancelButton="{{true}}" />
<privacy-popup bind:agree="agree" bind:disagree="disagree"></privacy-popup>
<van-dialog use-slot z-index="{{10}}" title="" show="{{ show }}" show-confirm-button="{{false}}" bind:close="closeLogin">
  <view class="modalDlg" wx:if="{{is_form}}">
    <view style="position: relative;width: 100%;">
      <view style="font-size: 40rpx;padding: 10rpx 0 40rpx 0;text-align: center;">登录</view>
      <view style="position: absolute;top: 0rpx;right: 0;color: gray;font-size: 28rpx;" bindtap='closeLogin'>
        取消
      </view>
    </view>
    <van-cell-group>
      <van-field value="{{ mobile }}" bind:keyboardheightchange="keyboardheight" type="number" adjust-position="{{false}}" confirm-hold="{{true}}" custom-style="background:#f6f6f6;" required label="手机号" maxlength="11" placeholder="请输入手机号" bind:change="changeMobile" />
      <van-field value="{{ pwd }}" bind:keyboardheightchange="keyboardheight" type="number" adjust-position="{{false}}" confirm-hold="{{true}}" custom-style="background:#f6f6f6;" required center label="密码" placeholder="请输入密码" maxlength="6" bind:change="changePwd" border="{{ false }}" use-button-slot></van-field>
    </van-cell-group>
    <view style="width: 100%;display: flex;justify-content: space-around;margin-top: 100rpx;">
      <view style="width:60%" class='wishbnt' bindtap='login'>确定</view>
    </view>
    <view class="to-list">
      <view class='back-bnt' bindtap='backLogin'>返回-账号列表</view>
    </view>
  </view>
  <view class="scrollBox" wx:else>
    <view style="display: flex;align-items: center;justify-content: space-between;">
      <view class="UserList">账号列表</view>
      <view class="UserList" bindtap='closeLogin'>取消</view>
    </view>
    <scroll-view scroll-y="true" class="scroll" show-scrollbar="{{false}}" enhanced>
      <view class="phoneClass">
        <view wx:for="{{remember_account_list}}" wx:key="index" style="height: 90rpx;display: flex;align-items: center;justify-content: space-between;gap:20rpx">
          <view data-info="{{item}}" bind:tap="switchUser" class="colorText" style="flex:1">
            <view style="margin-top: 10rpx;">{{item.mobile}}</view>
          </view>
          <view style="width: 100rpx;font-size: 24rpx;color:#646464;" catch:tap="handleDelUser" data-info="{{item}}">
            删除
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="go-login">
      <view class='back-bnt' bindtap='handleLogin'>直接登录</view>
    </view>
  </view>
</van-dialog>
<van-overlay show="{{ loading }}" z-index="9999" custom-style="background-color:rgba(0, 0, 0, 0.8);">
  <view style="display: flex;justify-content: center;margin-top: calc({{phoneParam.safeArea.height  }}rpx + 100rpx);">
    <van-loading size="24px" color="#ffffff" vertical>
      <text style="color: #ffffff;">加载中...</text>
    </van-loading>
  </view>
</van-overlay>