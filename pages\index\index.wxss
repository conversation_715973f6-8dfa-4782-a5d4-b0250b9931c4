.container {
  box-sizing: border-box;
  width: 100%;
  background: linear-gradient(#FEF1DF, #FEF1DF, #FEF1DF, #ececec);
  /* background-color: #ededed; */
  height: 100vh;
}

.tips {
  margin-left: 60rpx;
  margin-bottom: 80rpx;
  padding-top: 180rpx;
}

.tips view:nth-child(1) {
  font-size: 50rpx;
  font-weight: bold;
}

.tips view:nth-child(2) {
  font-size: 32rpx;
  color: #999999;
  margin-top: 10rpx;
}

.part {
  box-sizing: border-box;
  box-shadow: 0px 0px 8rpx 0px rgba(56, 62, 116, 0.3);
  border-radius: 20rpx;
  font-size: 38rpx;
  z-index: 99;
  padding: 0 20rpx;
  margin: 20rpx 20rpx;
}

.point .title {
  font-size: 28rpx;
  color: #848484;
  padding-top: 20rpx;
}

.two {
  height: auto;
  display: grid;
  align-items: center;
  color: #FF851E;
  font-size: 38rpx;
  z-index: 99;
  margin-top: 10rpx;
  grid-template-columns: 25% 25% 25% 25%;
  padding-bottom: 20rpx;
  box-sizing: border-box;
}

.three {
  height: 150rpx;
  display: grid;
  align-items: center;
  font-size: 38rpx;
  z-index: 99;
  margin-top: 20rpx;
  grid-template-columns: 25% 25% 25% 25%;
  box-sizing: border-box;
}

.enter-per {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.point .enter-per{
  margin-bottom: 20rpx;
}

.point-num {
  position: absolute;
  top: -10rpx;
  right: 30rpx;
  background-color: red;
  color: #fff;
  width: 30rpx;
  height: 30rpx;
  font-size: 24rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.warning-num {
  position: absolute;
  top: -10rpx;
  right: 30rpx;
  color: #fff;
  width: 30rpx;
  height: 30rpx;
  font-size: 24rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.link {
  box-sizing: border-box;
  border: 1rpx solid orange;
  box-shadow: 0px 0px 8rpx 0px rgba(88, 115, 247, 0.16);
  border-radius: 20rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #FF851E;
  font-size: 38rpx;
  z-index: 99;
  padding: 0 20rpx;
  margin: 20rpx 30rpx;
}

.linkPerson {
  border: 1rpx solid #ff851e;
  border-radius: 20rpx;
  padding: 30rpx;
}

.middle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  margin: 0 30rpx;
  gap: 20rpx;
}

.supplier_img_wrap {
  width: 120rpx;
  height: auto;
  background-color: #E0DFDD;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.supplier_img {
  width: 110rpx;
  padding: 20rpx;
  height: auto;
}

.btn_box {
  text-align: center;
}

.login_wrap {
  padding: 30rpx;
  box-sizing: border-box;
  position: relative;
}

.phone_list {
  width: calc(100% - 6.2em);
  position: absolute;
  top: 100rpx;
  right: 0;
  padding: 10rpx 0;
  box-sizing: border-box;
  background-color: #ffffff;
  z-index: 9999;
}

.phone_list view {
  padding-left: 50rpx;
  box-sizing: border-box;
  z-index: 9999;
}

.login_button {
  margin-top: 30rpx;
}

.switch_account {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999999;
  position: fixed;
  bottom: calc(15rpx + env(safe-area-inset-bottom));
}


.modalDlg {
  margin: 0 auto;
  padding: 20rpx;
  padding-top: 30rpx;
  padding-bottom: 60rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 600rpx;
  position: relative;
}

.to-list {
  display: flex;
  justify-content: left;
  margin-top: 40rpx;
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
}

.dialog-modle {
  margin: 0 auto;
  padding: 20rpx;
  padding-top: 30rpx;
  padding-bottom: 60rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cancelbnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx;
  color: #07c060;
  margin-right: 20rpx;
}

.cancel-bnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx;
  margin-right: 20rpx;
}

.back-bnt {
  color: #1989fa;
  border-bottom: 1rpx solid #1989fa;
  text-align: center;
  padding: 10rpx;
  width: fit-content;
  font-size: 26rpx;
}
.back-out{
  color: #fa7e19;
  border-bottom: 1rpx solid #fa7e19;
  text-align: center;
  padding: 10rpx;
  width: fit-content;
  font-size: 26rpx;
}

.wishbnt {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx;
  color: white;
}

.wish-bnt {
  background-color: #eec271;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx;
}

.phoneClass {
  margin-top: 10rpx;
  padding: 10rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  
}

.scrollBox {
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 10rpx;
  box-sizing: border-box;
  background-color: #fff;
  height: 650rpx;
  position: relative;
}

.UserList {
  padding: 0 10rpx;
  font-size: 30rpx;
  color: #a1a0a0;
}

.scroll {
  height: 540rpx;
}

.go-login {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 40rpx;
  padding:0rpx 30rpx;
  position: absolute;
  bottom: 0;
  right: 0;
  box-sizing: border-box;
  background-color: #fff;
}

.colorText {
  color: gray;
  padding: 15rpx;
  border-radius: 10rpx;
  border: 1px solid #FF851E;
  padding-left: 35rpx;
  position: relative;
  margin-top: 20rpx;
}

.newColorText {
  color: orange;
  padding: 15rpx;
  border-radius: 10rpx;
  border: 1px solid #FF851E;
  border-left: 20rpx solid #FF851E;
  position: relative;
  margin-top: 20rpx;
}

.iconDel {
  width: 40rpx;
  height: auto;
  position: absolute;
  top: -15rpx;
  right: -10rpx;
  background-color: #fff;
  border-radius: 50%;
}

.weuiCell {
  display: flex;
  align-items: center;
}


/*  待办任务 */
.task{
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

.task .title {
  font-size: 28rpx;
  color: #848484;
  padding-top: 20rpx;
}

.count {
  width: 40rpx;
  height: 40rpx;
  background-color: rgb(255, 133, 30);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.not-count {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #aaaaaa;
}