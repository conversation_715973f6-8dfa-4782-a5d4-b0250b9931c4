import {
  userIDKey,
} from '../../../utils/dict.js';
import {
  station_user,
  station_balance
} from '../../../apis/index';
Page({

  data: {
    station_info: {},
    userBalance: {
      integerPart: 0,
      decimalPart: 0
    }
  },

  onLoad(options) {
    this.getStation()
  },

  // 站点
  getStation() {
    let user_id = wx.getStorageSync(userIDKey)
    let data = {
      user_id: user_id
    }
    station_user(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          station_info: res.data.data
        })
        this.getBalance(res.data.data.id)
      }

    }).catch(err => {
      if (err.data.code == 4002) {}
    })
  },

  // 余额
  getBalance(id) {
    let data = {
      station_id: id
    }
    station_balance(data).then(res => {
      if (res.data.code == 0) {
        let price_num = (res.data.data.allAmount / 100).toFixed(2)
        let parts = price_num.split(".");
        let integerPart = parts[0];
        let decimalPart = parts[1];
        this.setData({
          userBalance: res.data.data,
          "userBalance.integerPart": integerPart,
          "userBalance.decimalPart": decimalPart,
        })
      }
    })
  },

  //跳转钱包
  jumpWallet() {
    let id = this.data.station_info.id
    wx.navigateTo({
      url: '/pages/index/wallet/index?id=' + id,
    })
  }


})