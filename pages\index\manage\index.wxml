<view class="container">
  <view class="station-info">
    <view style="font-size: 30rpx;">{{station_info.name}}</view>
    <view style="font-size: 26rpx;color: #a6a6a6;margin: 10rpx 0;">{{station_info.contact_user}}({{station_info.contact_mobile}})</view>
    <view style="font-size: 26rpx;color: #a6a6a6;">{{station_info.address}}</view>
    <view style="display: flex;align-items: center;gap: 10rpx;margin-top: 10rpx;">
      <view wx:for="{{station_info.deliver_type}}" wx:key="index">
        <view wx:if="{{item == 1}}" class="type">自提</view>
        <view wx:if="{{item == 2}}" class="type">送货到店</view>
        <view wx:if="{{item == 3}}" class="type">物流</view>
        <view wx:if="{{item == 4}}" class="type">及时配送</view>
      </view>
    </view>
  </view>

  <view class="money">
    <view bind:tap="jumpWallet" class="money-content">
      <view style="font-size: 26rpx;color: #888787;">余额(元)</view>
      <view style="display: flex;align-items: flex-end;margin-top: 20rpx;" >
        <view style="font-size: 40rpx;font-weight: bold;">{{ userBalance.allAmount >0 ?userBalance.integerPart:0}}</view>
        <view style="font-weight: bold;" wx:if="{{userBalance.allAmount > 0}}">.{{userBalance.decimalPart}}</view>
      </view>
    </view>


  </view>




</view>