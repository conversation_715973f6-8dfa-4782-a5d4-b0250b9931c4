Page({

  data: {
    map: {},
    markers: [],
  },

  onLoad(options) {
    let map = JSON.parse(options.map) 
    let marker = []
    marker.push({
      id: 1,
      width: 35,
      height: 50,
      latitude: map.latitude,
      longitude: map.longitude,
      callout: {
        content:  map.address,
        fontSize: 12,
        display: "ALWAYS"
      },
    })
    this.setData({
     map,
     markers: marker
    })
  },

  onShow() {

  },

})