import {
  station_buyer
} from '../../../apis/index'
import { dealTimes } from '../../../utils/check'
Page({

  data: {
    buyer_list: [],
    ids:'',
    is_end: false,
    page: 1,
  },

  onLoad(options) {
    this.setData({
      ids:options.id
    })
    this.getBuyerList(options.id)
  },

  getBuyerList(id) {
    if (this.data.is_end) {
      return
    }
    let page = this.data.page
    let data = {
      station_id: id,
      page: page,
      limit: 5

    }
    station_buyer(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list) {
          list = []
          this.setData({
            is_end: true
          })
          return
        }
        list.forEach(ele=>{
          ele.created_at_fmt = dealTimes(ele.created_at)
        })
        let newList = [...this.data.buyer_list, ...list] 
        this.setData({
          buyer_list: newList,
          page: page + 1,
        })
      }

    }).catch(err=>{

    })
  },

  handleMap(e){
    let map = JSON.stringify(e.currentTarget.dataset.map) 
    wx.navigateTo({
      url: '../map/index?map=' + map,
    })
  },


  onShow() {

  },

  onReachBottom() {
    if(!this.data.is_end){
      this.getBuyerList(this.data.ids)
    }
  },
})