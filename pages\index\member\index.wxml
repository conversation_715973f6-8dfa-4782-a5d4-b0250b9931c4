<view style="background-color: #f0f2f5;min-height: 100vh;box-sizing: border-box;padding: 20rpx;padding-bottom: 50rpx;">
  <view wx:for="{{buyer_list}}" wx:key="id">
    <view class="list">
      <view>
        <text style="font-size: 28rpx;">会员：</text>
        <text class="buyerName">{{item.buyer_name}}</text>
      </view>
      <view class="buyerInfo">
        <view style="flex: 1;">
          <text style="font-size: 28rpx;">联系人：</text>
          <text class="buyerName">{{item.contact_user}}</text>
        </view>

        <view style="flex: 1;">
          <text style="font-size: 28rpx;">手机号：</text>
          <text class="buyerName">{{item.contact_mobile}}</text>
        </view>
      </view>

      <view class="buyerInfo">
        <view style="flex: 1;">
          <text style="font-size: 28rpx;">类型：</text>
          <van-tag round type="success" wx:if="{{item.buyer_type ===1}}">水果店</van-tag>
          <van-tag round type="success" wx:if="{{item.buyer_type ===2}}">商超</van-tag>
          <van-tag round type="success" wx:if="{{item.buyer_type ===3}}">其他</van-tag>
        </view>
        <view style="flex: 1;">
          <text style="font-size: 28rpx;">线下实体：</text>
          <van-tag type="primary" wx:if="{{item.entity === 1 || item.entity === 0}}">有</van-tag>
          <van-tag type="danger" wx:if="{{item.entity ===2}}">无</van-tag>
        </view>
      </view>
      <view>
        <text style="font-size: 28rpx;">地址：</text>
        <text class="buyerName">{{item.address}}</text>
      </view>

      <view style="display: flex;align-items: center;">
        <text style="font-size: 28rpx;">定位：</text>
        <view class="buyerName">
          <text>{{item.location.name}}</text>
          <text class="see" bind:tap="handleMap" data-map="{{item.location}}">查看</text>
        </view>
      </view>
      <view>
        <text style="font-size: 28rpx;">申请说明：</text>
        <text class="buyerName">{{item.apply_reason}}</text>
      </view>
      <view>
        <text style="font-size: 28rpx;">创建时间：</text>
        <text class="buyerName">{{item.created_at_fmt}}</text>
      </view>

      <!-- <view class="btn">
        <view class="time">{{item.create_at_fmt}}</view>
        <view class="info">详情</view>
      </view> -->

    </view>
  </view>
</view>