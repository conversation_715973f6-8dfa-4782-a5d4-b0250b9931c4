.list {
  padding: 20rpx;
  background-color: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.buyerName {
  font-size: 26rpx;
  color: #949494;
}

.buyerInfo {
  display: flex;
  align-items: center;
  gap: 50rpx
}

.btn {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.time {
  font-size: 24rpx;
  color: #727272;
}

.info {
  background-color: #1989fa;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 6rpx 20rpx;
}

.see {
  color: green;
  border: 1rxp solid green;
  margin-left: 10rpx;
  border-bottom: 1rpx solid green;
}