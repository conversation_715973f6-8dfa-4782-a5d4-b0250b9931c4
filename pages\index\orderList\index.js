import { list_order_station } from '../../../apis/index'
import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
  dealFenToYuan
} from '../../../utils/dict';
import dayjs from "../../../libs/dayjs"
const app = getApp()
Page({

  data: {
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    nav: app.globalData.nav,
    safeArea: app.globalData.safeArea,
    statusBarHeight: app.globalData.statusBarHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    list: [],
    page: 1,
    loadFinish: false,
    isScrolling: false,
    show: false,
    time_range: [],
    time_begin: 0,
    time_end: 0,
    date: '',
    station_id: ''
  },

  onLoad(options) {
    let now = dayjs()
    let minDate = now.subtract(3, "month").startOf('day').valueOf()
    let maxDate = now.endOf('day').valueOf()
    let time_begin = now.subtract(2, "day").startOf('day').valueOf()
    let time_range = [time_begin, maxDate]
    this.setData({
      date: `${this.dealTimeToDay(time_begin) }-${ this.dealTimeToDay(maxDate)}`,
      page: 1,
      time_range: time_range,
      time_begin: time_begin,
      time_end: maxDate,
      minDate,
      maxDate,
      station_id: options.id
    });
    this.getList()
  },

  dealTimeToDay(at) {
    return dayjs(at).format('MM/DD')
  },
  back(){
    wx.navigateBack({
      success: function (res) {},
      fail: function (res) {
        wx.reLaunch({
          url: '/pages/index/index',
        })
      },
    })
  },

  getList() {

    if (this.data.loadFinish) {
      return
    }
    let data = {
      page: this.data.page,
      time_begin: this.data.time_begin,
      time_end: this.data.time_end,
      limit: 10,
      station_id: this.data.station_id
    }
    list_order_station(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list) {
          list = []
        } else {
          list.forEach((item) => {
            item.created_at_show = dealTimeFormat1(item.created_at)
            item.order_status_show = orderStatus(item.order_status)
            item.product_list.forEach(items => {
              items.prices = items.price / 100
            })
          })
        }
        this.data.page++
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          loadFinish: list.length === 0,
        })
      }
    })
  },
  onDisplay() {
    this.setData({
      show: true
    });
  },
  onConfirm(event) {
    const [start, end] = event.detail;
    let time_begin = start.getTime()
    let time_end = dayjs(end.getTime()).endOf('day').valueOf()
    let time_range = [time_begin, time_end]

    this.setData({
      show: false,
      page: 1,
      list: [],
      date: `${this.dealTimeToDay(start)}-${this.dealTimeToDay(time_end)}`,
      time_begin,
      time_range,
      time_end,
      loadFinish: false,
      isScrolling: false,
    });
    //  查询列表
    this.getList()
  },

  onClose() {
    this.setData({
      show: false
    });
  },
  toOrderDetail(e) {
    let id = e.currentTarget.dataset.info.id
    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id,
    })
  },
})