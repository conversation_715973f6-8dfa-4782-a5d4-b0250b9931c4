Component({
  properties: {
    showPrivacy: {
      type: Boolean,
      value: false
    }
  },

  data: {
    innerShow: false,
  },
  lifetimes: {
    attached: function () {
      if (wx.getPrivacySetting) {
        wx.getPrivacySetting({
          success: res => {
            // console.log("是否需要授权：", res.needAuthorization, "隐私协议的名称为：", res.privacyContractName)
            if (res.needAuthorization) {
              this.popUp()
            } else {
              // this.popUp()
              this.triggerEvent("agree")
            }
          },
          fail: () => {},
          complete: () => {},
        })
      } else {
        // 低版本基础库不支持 wx.getPrivacySetting 接口，隐私接口可以直接调用
        this.triggerEvent("agree")
      }
    },
  },
  methods: {
    handleDisagree(e) {
      this.disPopUp()
    },
    handleAgree(e) {
      this.disPopUp()
      wx.navigateTo({
        url: '/pages/index/index',
      })
    },
    popUp() {
      this.setData({
        innerShow: true
      })

    },
    disPopUp() {
      this.setData({
        innerShow: false
      })
    },
    openPrivacyContract() {
      wx.openPrivacyContract({
        success: res => {
          console.log('openPrivacyContract success')
        },
        fail: res => {
          console.error('openPrivacyContract fail', res)
        }
      })
    }
  },

  observers: {
    'showPrivacy': function (showPrivacy) {
      this.setData({
        innerShow: showPrivacy,
      })
    }
  }

})