<van-popup show="{{ innerShow }}" round bind:close="onClose">
  <view class="privacy pop">
    <view class="title">
      隐私政策协议
    </view>
    <view class="desc">
      <text>亲爱的用户，感谢您对果蔬团一直以来的信任！</text>
      <view class="content">
        <view class='indent'>为保护您的个人信息，果蔬团制定
          <text class="see-privacy" bind:tap="openPrivacyContract">《果蔬团隐私政策》</text>
          明确小程序业务场景下收集使用个人信息、申请小程序敏感权限的规则，请您理解。
        </view>
        <view class='indent'>您需同意
          <text class="see-privacy" bind:tap="openPrivacyContract">《果蔬团隐私政策》</text>
        ，便于授权果蔬团申请权限为您判断和匹配附近的服务仓，展示相应的商品和提供完整的服务。 </view>
      </view>

    </view>
    <view class="operate">
      <button id="agree-btn" class="agree" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="handleAgree">同意《隐私权政策》并继续</button>
      <view id="agree-btn" class="agree-not" bind:tap="handleDisagree">不同意，仅浏览</view>
    </view>
  </view>
</van-popup>