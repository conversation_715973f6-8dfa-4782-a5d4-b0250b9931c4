.pop {
  width: 600rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.title {
  margin-left: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.desc {
  padding: 20rpx;
  font-size: 30rpx;
}

.tip_img {
  width: 100%
}

.operate {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  margin-top: 30rpx;
  font-size: 30rpx;
  padding: 0 10rpx;
  width: 100%;
  box-sizing: border-box;
}

.agree {
  background-color: #54be6a;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  padding: 0 40rpx;
  width: 100%;
  margin-top: 20rpx;
  font-size: 32rpx;
}

.agree-not {
  border: none;
  border-radius: 40rpx;
  padding: 0 40rpx;
  width: 100%;
  margin-top: 20rpx;
  text-align: center;
  font-size: 26rpx;
}

.see-privacy {
  color: #1e94ef;
}

.close{
  padding-top: 30rpx;
  color: #797878;
  font-size: 26rpx;
  border-radius: 66rpx;
}

.content{
  line-height: 50rpx;
}

.indent{
  text-indent: 1em;
}