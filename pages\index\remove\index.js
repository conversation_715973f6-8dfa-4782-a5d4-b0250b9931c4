import {
  product_offline
} from '../../../apis/index';

import {
  dealTimeFormat1,
  dealTimeFormatMMDD
} from '../../../utils/dict';
import dayjs from "../../../libs/dayjs"
Page({

  data: {
    timestamp: 0,
    list: [],
    time: 0,
    times: []
  },



  onLoad(options) {
    let date = dayjs().valueOf()
    this.setData({
      timestamp: date,
      other_date: dealTimeFormatMMDD(date),
    })
    this.initTime()
    this.getOfflineData()
  },

  initTime() {
    let now = dayjs()
    let t = []
    for (let i = 0; i < 5; i++) {
      t.push({
        id: 2,
        month_timestamp: now.subtract(i, 'day').startOf('day').valueOf(),
        time: now.subtract(i, 'day').startOf('day').format('MM-DD'),
      })
    }
    t[0].time = '今日'
    t[1].time = '昨日'
    this.setData({
      times: t,
      month_timestamp: t[0].month_timestamp,
    })
  },

  handelTime(e) {
    let time = e.currentTarget.dataset.time
    this.setData({
      month_timestamp: time.month_timestamp,
      list: []
    })
    this.getOfflineData()
  },

  getOfflineData() {
    let data = {
      timestamp: this.data.month_timestamp
    }
    product_offline(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list) {
          list = []
        }
        list.forEach(item => {
          item.offline_at_fmt = dealTimeFormat1(item.offline_at)
        })
        this.setData({
          list
        })

      }
    })
  },



  handelCalendar(e) {
    let time = e.currentTarget.dataset.time
    this.setData({
      time,
      show: true
    })

  },

  onClose() {
    this.setData({
      show: false
    });
  },
  onConfirm(event) {
    const [start, end] = event.detail;
    let time_begin = start.getTime()
    let time_end = dayjs(end.getTime()).endOf('day').valueOf()
    let time_range = [time_begin, time_end]

    this.setData({
      show: false,
      time_begin,
      time_range,
      list: []
    });

    //  查询列表
    this.getOfflineData()
  },


  jumpProductDetail(e) {
    let info = e.currentTarget.dataset.info
    let id = info.product_id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id + `&from=list`,
    })
  },

  onShow() {

  },
})