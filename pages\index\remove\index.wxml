<view class="content">

  <view class="day">
    <view class="date {{month_timestamp == item.month_timestamp?'back': ''}}" wx:for="{{times}}" wx:key="index" catch:tap="handelTime" data-time="{{item}}">{{item.time}}</view>
  </view>


  <block wx:for="{{list}}" wx:key="index">
    <view class="list" bind:tap="jumpProductDetail" data-info="{{item}}">
      <view class="supplier">
        <view style="font-size: 28rpx;">{{item.product_title}}</view>
        <view class="icon">{{item.supplier_name}}</view>
      </view>
      <view style="color: #999898;font-size: 28rpx;">下架时间：{{item.offline_at_fmt}}</view>
    </view>
  </block>

  <van-empty description="暂无下架商品" wx:if="{{list.length == 0}}" />

</view>