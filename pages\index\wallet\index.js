import {
  station_authentication,
  station_balance,
  station_withdraw
} from '../../../apis/index'
import { dealTimeFormat1 } from '../../../utils/dict'
Page({

  data: {
    id: '',
    authentication_info: {},
    bankcard: '',
    userBalance: {
      integerPart: 0,
      decimalPart: 0
    },
    withdraw_list: []
  },

  onLoad(options) {
    let id = options.id
    this.setData({
      id: id
    })
    this.getAuthentication()
    this.getBalance()
    this.getWithdraw()
  },

  getAuthentication() {
    let data = {
      station_id: this.data.id
    }
    station_authentication(data).then(res => {
      if (res.data.code == 0) {
        let card = res.data.data.individual_bankcard_no
        let bankcard = card.slice(0, 6) + '******' + card.slice(-4)
        this.setData({
          authentication_info: res.data.data,
          bankcard,
        })
      }
    })
  },

  // 余额
  getBalance() {
    let data = {
      station_id: this.data.id
    }
    station_balance(data).then(res => {
      if (res.data.code == 0) {
        let price_num = (res.data.data.allAmount / 100).toFixed(2)
        let parts = price_num.split(".");
        let integerPart = parts[0];
        let decimalPart = parts[1];
        this.setData({
          userBalance: res.data.data,
          "userBalance.integerPart": integerPart,
          "userBalance.decimalPart": decimalPart,
        })
      }
    })
  },

  // 提现记录
  getWithdraw() {
    let data = {
      station_id: this.data.id
    }
    station_withdraw(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if(!list){
          list = []
        }
        list.forEach(ele=>{
          ele.created_at_fmt = dealTimeFormat1(ele.created_at)
          ele.amount_fmt = (ele.amount/100).toFixed(2)
        })
        this.setData({
          withdraw_list: list
        })
      }
    }).catch(err=>{})
  },


})