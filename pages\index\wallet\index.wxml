<view class="container">
  <view class="info">
    <view>{{bankcard}}</view>
    <view style="display: flex;justify-content: space-between;align-items: center;margin-top: 20rpx;">
      <view style="display: flex;flex-direction: column;align-items: center;">
        <view>我的余额</view>
        <view style="display: flex;align-items: flex-end;margin-top: 20rpx;">
          <view style="font-size: 40rpx;font-weight: bold;">￥{{ userBalance.allAmount >0 ?userBalance.integerPart:0}}</view>
          <view style="font-weight: bold;" wx:if="{{userBalance.allAmount > 0}}">.{{userBalance.decimalPart}}</view>
        </view>
      </view>

      <view class="btn">提现<text style="font-size: 18rpx;">(暂未开放)</text></view>
    </view>
  </view>


  <view>
    <view class="detail_record_title">
      <view class="detail_record">余额明细</view>
      <view></view>
    </view>
    <view>

      <view class="record_list_" wx:for="{{withdraw_list}}" wx:key="index">
        <view class="record_list_left">
          <view class="title">提现<van-tag color="#f2826a" wx:if="{{item.pay_status==3}}" style="margin-left:10rpx" plain type="primary">失败</van-tag>
            <van-tag color="#1989fa" wx:if="{{item.pay_status==99}}" style="margin-left:10rpx" plain type="primary">处理中</van-tag>
          </view>
          <view class="times">{{item.created_at_fmt}}</view>
        </view>
        <view class="record_value_">-{{item.amount_fmt}}元</view>
      </view>

    </view>

  </view>


</view>