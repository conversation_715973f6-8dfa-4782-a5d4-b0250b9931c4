.container{
  min-height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
}
.info{
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-sizing: border-box;
  background-color: #ffa105;
  color: #fff;
}
.btn{
  background-color: #fff;
  color: #ffa105;
  padding: 16rpx 30rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
}

.detail_record_title {
  padding: 30rpx;
}

.detail_record{
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}



.record_list {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  width: 100%;
  height: 114rpx;
  background: rgba(251, 181, 87, 0.2);
  padding: 0 30rpx;
  justify-content: space-between;
  box-sizing: border-box;
}

.record_list_{
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  width: 100%;
  height: 114rpx;
  padding: 0 30rpx;
  justify-content: space-between;
  box-sizing: border-box;
  border-bottom: 2rpx solid #eeeeee;
}


.title {
  font-size: 28rpx;
  font-weight:bold;
  color: #333333;
}

.times {
  font-size: 28rpx;
  font-weight: bold;
  color: #999999;
  margin-top: 5rpx;
}

.record_value_ {
  font-size: 36rpx;
  font-weight: 500;
  color: #666666;
}

.record_value {
  font-size: 36rpx;
  font-weight: 500;
  color: #FC4840;
}