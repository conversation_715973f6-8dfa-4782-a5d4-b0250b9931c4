// pages/intro/index.js
const app = getApp();
import { intro_tips_img } from "../../apis/intro";
Page({
  data: {
    imgUrl: app.globalData.imageUrl,
    descImg: [],
    showPrivacy: false,
  },

  onLoad(options) {
    // this.descImg()
  },

  descImg() {
    intro_tips_img().then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data.img_list;
        this.setData({
          descImg: list,
        });
      }
    });
  },

  toShowPrivacy() {
    this.setData({
      showPrivacy: true,
    });
  },
});
