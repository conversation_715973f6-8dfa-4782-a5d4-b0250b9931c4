const app = getApp()
import dayjs from '../../libs/dayjs';
import {
  dealTimeFormat2,
} from '../../utils/dict';
import {
  procurement_verview_stats,
  refresh_buy_stats
} from '../../apis/warehouse/qualityControl';
Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    timesPop: false,
    times: 0, //筛选时间
    newTimes: 0, //筛选时间
    maxTimes: 0,
    now_timestamp: 0,
    procurementOverviewTitleList: [
      "分类", "商品", "供应商", "订单信息", "采购信息", "利润信息"
    ], //个人中心 采购总览
    procureOverviewList: [],
    all_list: [],
    from: '',
    actionShow: false,
    actionsList: [],
    show: false,
    supName: '全部',
    stats: {},
    supplier_id: '',
  },

  onLoad(options) {
    let now = dayjs()
    let newTimes = now.subtract(60, 'day').valueOf()
    let maxTimes = now.add(0, 'day').valueOf()

    let from = options.from

    console.log(from);

    let supplier_id;
    if (from !== 'index') {
      supplier_id = wx.getStorageSync('supplierid')
    }

    this.setData({
      now_timestamp: now.valueOf(),
      times: dealTimeFormat2(now.valueOf()),
      maxTimes: maxTimes,
      newTimes: newTimes,
      from: from,
      supplier_id: supplier_id,
    });
    this.procureOverview()
  },

  //打开时间弹窗
  openTimePop() {
    this.setData({
      timesPop: true,
    })
  },
  //关闭时间弹窗
  closeCarendar() {
    this.setData({
      timesPop: false,
    })
  },

  // 确认时间
  confirmTime(e) {
    this.setData({
      timesPop: false,
      times: dealTimeFormat2(e.detail.getTime()),
      now_timestamp: e.detail.getTime(),
    })
    this.procureOverview()
  },

  // 采购总览
  procureOverview() {

    let supplier_id = this.data.supplier_id

    if (supplier_id == 'all' && this.data.from == 'index') {
      supplier_id = ''
    }

    let data = {
      supplier_id: supplier_id,
      timestamp: this.data.now_timestamp,
    }

    procurement_verview_stats(data).then(res => {
      if (res.data.code == 0) {
        let d = res.data.data
        let list = d.list
        if (!list) {
          list = []
        }
        list.forEach(item => {
          if (item.list.length > 0 && item.list !== null) {
            let arrList = item.list
            arrList.forEach(ele => {
              ele.average_price_fmt = (ele.average_price / 100).toFixed(2)
              ele.average_unit_price_fmt = (ele.average_unit_price / 100).toFixed(2)
              ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(1)
              ele.total_standard_weight_fmt = (ele.total_standard_weight / 1000).toFixed(1)
              ele.total_sort_weight_fmt = (ele.total_sort_weight / 1000).toFixed(1)
              ele.buy_amount_fmt = (ele.buy_amount / 100).toFixed(2)
              ele.profit_amount_fmt = (ele.profit_amount / 100).toFixed(2) //单价利润
              ele.all_piece_profit = ((ele.profit_amount / 100) * ele.buy_num).toFixed(2) //计件 利润
              ele.all_weight_profit = ((ele.profit_amount / 100) * (ele.total_sort_weight / 1000)).toFixed(2) //称重 利润
              ele.average_buy_price_fmt = (ele.average_buy_price / 100).toFixed(2)
              ele.average_buy_unit_price_fmt = (ele.average_buy_unit_price / 100).toFixed(2)

            })
          }
        })

        d.total_buy_product_amount_fmt = (d.total_buy_product_amount / 100).toFixed(2)
        d.total_order_product_amount_fmt = (d.total_order_product_amount / 100).toFixed(2)
        d.total_profit_fmt = (d.total_profit / 100).toFixed(2)

        let stats = {
          total_buy_product_amount_fmt: d.total_buy_product_amount_fmt,
          total_order_product_amount_fmt: d.total_order_product_amount_fmt,
          total_profit_fmt: d.total_profit_fmt,
          profit_rate: d.total_profit_rate,
        }
        if (this.data.from === 'index' && !supplier_id) {
          this.initActionList(list)
        }

        this.setData({
          procureOverviewList: list,
          all_list: list,
          stats: stats,
        })
      }

    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: "none"
      })
    })

  },


  dealMoney(fen) {
    return Math.round(fen) / 100
  },

  //  初始化动作面板数据
  initActionList(list) {
    //  id和name
    let supplierMap = {}
    list.forEach(item => {
      if (item.list.length > 0 && item.list !== null) {
        let arrList = item.list
        arrList.forEach(ele => {
          supplierMap[ele.supplier_id] = ele.supplier_simple_name
        })
      }
    })

    let actionsList = [{
      id: 'all',
      name: '全部',
      color: '#ee0a24'
    }]
    for (var key in supplierMap) {
      actionsList.push({
        id: key,
        name: supplierMap[key],
      })
    }
    this.setData({
      supName: "全部",
      actionsList: actionsList
    })
  },

  // 打开动作面板
  handleAction() {
    this.setData({
      actionShow: true,
    })
  },

  handleClose() {
    this.setData({
      actionShow: false,
    })
  },

  handleSelect(e) {
    const {
      id,
      name
    } = e.detail
    let actionsList = this.data.actionsList
    actionsList.forEach(item => {
      if (item.id == id) {
        item.color = '#ee0a24'
      } else {
        item.color = '#000'
      }
    });

    let supplier_id = this.data.supplier_id
    if (this.data.from === 'index' && id !== 'all') {
      supplier_id = id
    }
    if (this.data.from === 'index' && id == 'all') {
      supplier_id = ''
    }

    this.setData({
      procureOverviewList: [],
      supName: name,
      actionsList: actionsList,
      supplier_id: supplier_id,
    })
    this.procureOverview()
  },

  // 跳转商品详情
  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  },

  //  刷新
  refresh(e) {
    let info = e.currentTarget.dataset.info
    let product_id = info.product_id
    let stock_up_day_time = info.stock_up_day_time
    let stock_up_no = info.stock_up_no

    let data = {
      product_id: product_id,
      stock_up_day_time: stock_up_day_time,
      stock_up_no: stock_up_no,
    }

    refresh_buy_stats(data).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '刷新成功',
        })
        this.procureOverview()
      }
    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: "none"
      })
    })
  },

  onShow() {
    let now = dayjs().valueOf()
    this.setData({
      now_timestamp: now,
    });
  },
})