<view class="container">
  <view class="nav">
    <view class="capsule-box">
      <view bindtap="openTimePop">时间：{{times}}</view>
    </view>
    <view class="search" bind:tap="handleAction" wx:if="{{from==='index'}}">供应商: {{supName}}></view>
  </view>


  <view class="function_two">
    <scroll-view scroll-x show-scrollbar="{{false}}" enhanced>
      <view class="part">
        <view class="detail_form_title_wrap">
          <view style="width: 70rpx;">
            分类
          </view>
          <view style="width: 240rpx;">
            商品
          </view>
          <view style="width: 70rpx;">
            供应商
          </view>
          <view style="width: 200rpx;">
            订单信息
          </view>
          <view style="width: 200rpx;">
            采购信息
          </view>
          <view style="width: 200rpx;">
            利润信息
          </view>
        </view>
        <!-- 数据 -->
        <view>
          <view wx:for="{{procureOverviewList}}" wx:key="index" style="display: flex;">
            <view class="eleClass"> {{item.category_name}}</view>
            <view>
              <view wx:for="{{item.list}}" wx:key="id" wx:for-item="ele" style="display: flex;">
                <view class="eleTitle" catchtap="jumpProductDetail" data-info="{{ele}}">
                  <view style="flex:1">
                    <text>{{ele.product_title}}</text>
                    <text style="color: #ff5000;padding: 0 8rpx;">[{{ele.sku_name}}]</text>
                    <text wx:if="{{ele.stock_up_no>1}}" style="color: #c50202;">P{{ele.stock_up_no}}</text>
                  </view>
                </view>
                <!--  供应商 -->
                <view class="eleProfit" style="align-items: center;justify-content: center;width: 70rpx;">
                  {{ele.supplier_simple_name}}
                </view>
                <!-- 订单 -->
                <view class="eleProfit" style="width: 200rpx;">
                  <view>
                    均价：{{ele.average_price_fmt}}
                  </view>
                  <view wx:if="{{ele.is_check_weight}}">单价：{{ele.average_unit_price_fmt}}/kg</view>
                  <view wx:else>单价：{{ele.average_unit_price_fmt}}/件</view>
                  <view>数量：{{ele.total_num}}</view>
                  <view>毛重：{{ele.rough_weight_fmt}}kg</view>
                  <view>总重：{{ele.total_standard_weight_fmt}}kg</view>
                </view>
                <!--  采购 -->
                <view class="eleProfit" style="width: 200rpx;">
                  <view>总价：{{ele.buy_amount_fmt}}</view>
                  <view wx:if="{{ele.is_check_weight}}">单价：{{ele.average_buy_unit_price_fmt}}/kg</view>
                  <view wx:else>单价：{{ele.average_buy_unit_price_fmt}}/件</view>
                  <view>数量：{{ele.buy_num}}</view>
                  <view>总重：{{ele.total_sort_weight_fmt}}kg</view>
                </view>
                <view class="eleProfit" style="flex-direction: column;width: 200rpx;box-sizing: border-box;">
                  <view>单价利润：{{ele.profit_amount_fmt}}</view>
                  <view wx:if="{{ele.is_check_weight}}">利润小计：{{ele.all_weight_profit}}</view>
                  <view wx:else>利润小计：{{ele.all_piece_profit}}</view>
                  <view>毛利率：
                    <text wx:if="{{ele.profit_percent>10}}" style="color: red;">{{ele.profit_percent}}%</text>
                    <text wx:if="{{ele.profit_percent<0}}" style="color: green;">{{ele.profit_percent}}%</text>
                    <text wx:if="{{ele.profit_percent>=0&&ele.profit_percent<=10}}">{{ele.profit_percent}}%</text>
                  </view>
                  <view wx:if="{{from=='index'&&ele.refresh_status==1}}" class="refresh" data-info="{{ele}}" catch:tap="refresh">
                    刷新
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view style="margin-left: 20rpx;display: flex;gap: 10rpx;flex-direction: column;">
    <view>销售：{{stats.total_order_product_amount_fmt}}</view>
    <view>采购：{{stats.total_buy_product_amount_fmt}}</view>
    <view>利润：{{stats.total_profit_fmt}}</view>
    <view>利润率：{{stats.profit_rate}}%</view>
  </view>
  <view style="margin-left: 20rpx;margin-top: 20rpx;font-size: 24rpx;color: #747474;">
    计算方式：1. 销售=采购数*销售价（分拣重量*销售单价）
    2. 采购=采购数*采购价（分拣重量*采购单价）
  </view>
</view>

<van-calendar class="calendar" bind:close="closeCarendar" first-day-of-week="1" show-title="{{false}}" show="{{ timesPop }}" show-confirm="{{ false }}" default-date="{{times}}" min-date="{{newTimes}}" max-date="{{maxTimes}}" bind:confirm="confirmTime" />

<van-action-sheet show="{{ actionShow }}" actions="{{ actionsList }}" close-on-click-overlay cancel-text="取消" bind:cancel="handleClose" bind:close="handleClose" bind:select="handleSelect" />