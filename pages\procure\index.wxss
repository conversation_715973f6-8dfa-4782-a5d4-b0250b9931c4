.container {
  padding-bottom: env(safe-area-inset-bottom);
}

.nav {
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  padding: 0 10rpx;
}

.inputBox {
  border: 1rpx solid #aaa;
  border-radius: 16rpx;
  padding-left: 10rpx;
  margin-right: 10rpx;
}

.search {
  padding: 0 6rpx;
  border-radius: 10rpx;
  margin-left: 40rpx;
}

.capsule-box {
  padding-left: 20rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.function_two {
  margin-top: 20rpx;
  overflow-x: scroll;
  padding: 10rpx;
  box-sizing: border-box;
}

.part {
  width: 150%;
  font-size: 22rpx;
}

.detail_form_title_wrap {
  display: flex;
  height: 100rpx;
  overflow-x: scroll;
}

.detail_form_title_wrap view {
  border: 2rpx solid #e5e5e5;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}

.eleClass {
  border: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 70rpx;
  padding: 0 4rpx;
}

.eleProfit {
  border: 1rpx solid #e5e5e5;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 4rpx;
}


.eleTitle {
  border: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  box-sizing: border-box;
  padding: 0 4rpx;
}

.calendar .van-popup {
  height: 500px !important;
}

.refresh {
  background-color: #12aaab;
  border-radius: 20rpx;
  text-align: center;
  padding: 4rpx 10rpx;
  width: 80rpx;
  color: #fff;
  font-size: 26rpx;
  margin-top: 20rpx;
}