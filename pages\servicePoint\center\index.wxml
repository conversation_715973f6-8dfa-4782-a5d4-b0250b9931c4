<!--pages/servicePoint/center/index.wxml-->
<wxs src="../../../utils/tool.wxs" module="tool" />
<template name="4">
  <view class="container">
    <van-nav-bar title="个人中心" fixed z-index="-2" placeholder />
    <view class="base_bg">
      <image class="bg" src="{{imageUrl+'/icon/bg.jpg'}}" mode="aspectFill" />
      <view class="base_info">
        <view class="store_cover">
          <image class="store_cover" src="{{servicePointDetail.shop_head_img.name?imageUrl + servicePointDetail.shop_head_img.name:''}}" mode="aspectFill" />
        </view>
        <view style="margin-left: 30rpx;margin-top: 10rpx;" class="">
          {{servicePointDetail.name}}
        </view>
      </view>

      <view class="amount_module_wrap">
        <view class="amount_module">
          <view class="amount_module_list" bindtap="jumpWallet">
            <view>余额</view>
            <view>{{userBalance.allAmount?tool.toFixedTwo(userBalance.allAmount / 100):0}}</view>
          </view>
          <!-- <view class="amount_module_list">
            <view>待结算</view>
            <view>0</view>
          </view> -->
        </view>
      </view>
    </view>
    <view class="function_list">
      <!-- <view style="margin-top:100rpx">
        <van-cell title="保证金(待缴纳)" is-link />
      </view> -->

      <view class="function" style="margin-top:100rpx">
        <view class="functionBox" bind:tap="switchRole">
          <image src="{{imageUrl+'icon/role.png'}}" style="width: 50rpx;height: auto; margin-bottom: 20rpx; " mode="widthFix" />
          <view>切换角色</view>
        </view>

        <view  bind:tap="jumpSetup"  class="functionBox" >
          <image src="{{imageUrl+'icon/setup.png'}}" style="width: 50rpx;height: auto; margin-bottom: 20rpx;" mode="widthFix" />
          <view>设置</view>
        </view>
      </view>
    </view>
  </view>
</template>