import {
  authentication_get,
} from '../../../../../apis/setup';
const app = getApp()
Page({

  data: {
    info: {},
    imgUrl: app.globalData.imageUrl,
  },

  onLoad(options) {
    this.authInfo(options.id)
  },
  // 认证信息
  authInfo(id) {
    let data = {
      service_point_id: id,
    }
    authentication_get(data).then(res => {
      if(res.data.code == 0){
        this.setData({
          info: res.data.data
        })
      }
    })
  },
  //  查看图片
  viewImage(e) {
    let current = this.data.imgUrl + e.currentTarget.dataset.url
    let urls = []
    urls.push(current)
    wx.previewImage({
      current: current,
      urls: urls,
    })
  },

  onShow() {

  },

})