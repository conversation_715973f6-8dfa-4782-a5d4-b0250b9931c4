// pages/supplier/center/setup/index.js
const app = getApp()
import {
  send_code,
  bind_phone
} from '../../../../apis/servicePoint/center'
import {
  upload_sign,
} from '../../../../utils/api';
import {
  service_get,
} from '../../../../apis/setup';
const uploadFile = require('../../../../utils/uploadFile');
const util = require('../../../../utils/util');
import {
  update_avatar
} from '../../../../apis/supplier/center';
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import {
  dealTime
} from '../../../../utils/check';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    formData: {
      mobile: "",
      captcha: "",
    },
    payPhone: 0,
    currentTime: "获取验证码", //倒计时
    imgUrl: app.globalData.imageUrl,
    fileList: []
  },


  onLoad(options) {
    let that = this
    this.setData({
      payPhone: options.payPhone,
      isbindphone: options.isbindphone,
      imgs: options.imgs,
      id: options.id
    })
    const {
      fileList = []
    } = that.data;
    fileList.push({
      url: that.data.imgUrl + options.imgs
    });

    this.setData({
      fileList: fileList
    })
    this.serviceInfo()
  },

  // 服务仓信息
  serviceInfo() {
    let data = {
      id: this.data.id
    }
    service_get(data).then(res => {
      if (res.data.code == 0) {
        res.data.data.scope_fmt = res.data.data.scope / 1000
        res.data.data.created_at_fmt = dealTime(res.data.data.created_at)
        this.setData({
          formData: res.data.data
        })
      }
    })
  },



  //更换头像
  choosePortrait(e) {
    console.log(e)
    let data = {
      avatar_img: e.detail
    }
    update_avatar(data).then(res => {
      if (res.data.code == 0) {
        Toast('更换头像成功');
      }
    })
  },

  // 手机验证码
  sms(e) {
    this.setData({
      'formData.captcha': e.detail
    })
  },

  //发送验证码
  sendCode() {
    let that = this
    let data = {
      mobile: this.data.formData.pay_mobile
    }
    send_code(data).then(res => {
      if (res.data.code == 0) {
        that.verificationCode()
        Toast('短信验证码已发送');
      }
    })
  },

  // 验证码倒计时
  verificationCode() {
    let that = this
    let currentTime = 60
    //设置一分钟的倒计时
    var interval = setInterval(function () {
      currentTime--;
      //每执行一次让倒计时秒数减一
      that.setData({
        currentTime: currentTime + 's', //按钮文字变成倒计时对应秒数
        disabled: true
      })
      //如果当秒数小于等于0时 停止计时器 且按钮文字变成重新发送 
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          currentTime: '重新发送',
          disabled: false,
          color: 'red'
        })
      }
    }, 1000);
  },
  //提交
  submit() {
    if (this.data.formData.captcha == '') {
      Toast('请输入验证码');
      return
    }
    bind_phone(this.data.formData).then(res => {
      if (res.data.code == 0) {
        Toast('保存成功');
        setTimeout(function () {
          wx.navigateBack()
        }, 2000)
      }
    }).catch(err => {
      Toast(err.data.message);
    })
  },


  // 认证信息
  toAuth(e) {
    Toast('暂未开放');
    return
    let id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: './authInfo/index?id=' + id,
    })
  },

  //  查看图片
  viewImage(e) {
    let current = this.data.imgUrl + e.currentTarget.dataset.url
    let urls = []
    urls.push(current)
    wx.previewImage({
      current: current,
      urls: urls,
    })
  },
  onShow() {

  },

})