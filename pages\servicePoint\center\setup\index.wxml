<!--pages/supplier/center/setup/index.wxml-->
<view class="container">

  <!-- <van-field  label="手机号" placeholder="请输入手机号" maxlength="11" bind:change="phone" error-message=""  /> -->
  <!-- <van-cell title="头像" wx:if="{{imgs != ''}}"  is-link>
      <view slot="">
          <updata-avatar fileList="{{fileList}}" bind:choosePortrait="choosePortrait"></updata-avatar>
      </view>
    </van-cell> -->
  <view class="content">



    <view class="info">
      <view class="name">门头照</view>
      <view style="color: gray;margin-left: 10rpx;">
        <image style="width: 200rpx;height: auto;" wx:if="{{formData.shop_head_img.name}}" src="{{imgUrl +formData.shop_head_img.name}}" data-url="{{formData.shop_head_img.name}}" bind:tap="viewImage" mode="widthFix" />
      </view>
    </view>

    <view class="info">
      <view class="name">支付手机号</view>
      <view style="color: gray;margin-left: 10rpx;">{{formData.pay_mobile}}</view>
    </view>

    <view class="info" style="flex-direction: column;" wx:if="{{isbindphone =='false'}}">
      <van-field  center clearable label="短信验证码" placeholder="请输入短信验证码" bind:change="sms" use-button-slot border="{{ false }}">
        <van-button slot="button" maxlength="6" size="small" type="primary" bind:click="sendCode">
          {{currentTime}}
        </van-button>
      </van-field>
      <view style="margin-top: 30rpx;width: 300rpx;">
        <van-button type="info" round   block bindtap="submit">保存</van-button>
      </view>
    </view>

    <view class="info">
      <view class="name">联系人</view>
      <view style="color: gray;margin-left: 10rpx;">{{formData.contact_user}}</view>
    </view>

    <!-- <view class="info">
      <view class="name">
      供应商服务费费率
      </view>
      <view style="color: gray;margin-left: 10rpx;">{{formData.supplier_service_fee}}%</view>
    </view> -->

    <view class="info">
      <view class="name">配送费服务费费率</view>
      <view style="color: gray;margin-left: 10rpx;">{{formData.deliver_service_fee}}%</view>
    </view>

    <view class="info">
      <view class="name" >地址</view>
      <view style="color: gray;flex: 1;">{{formData.address}}</view>
    </view>

    <view class="info">
      <view class="name">定位</view>
      <view style="color: gray;flex: 1;">{{formData.location.address}}</view>
    </view>

    <view class="info">
      <view class="name">开启状态</view>
      <van-tag wx:if="{{formData.is_open}}" type="primary">开启</van-tag>
      <van-tag wx:if="{{!formData.is_open}}" type="danger">关闭</van-tag>
    </view>

    <view class="info">
      <view class="name">配送方式</view>
      <view style="color: gray;" wx:if="{{formData.deliver_type == null}}">无</view>
      <view style="color: gray;display: flex;" wx:else>
        <view wx:for="{{formData.deliver_type}}" wx:key="item" style="margin-left: 10rpx;">
          <van-tag type="primary" wx:if="{{item == 1}}">配送到店</van-tag>
          <van-tag type="primary" wx:if="{{item == 2}}">自提</van-tag>
          <van-tag type="primary" wx:if="{{item == 3}}">物流</van-tag>
          <van-tag type="primary" wx:if="{{item == 4}}">即时配送</van-tag>
        </view>
      </view>
    </view>

    <view class="info" style="justify-content: space-between;align-items: center;"  bind:tap="toAuth" data-id="{{formData.id}}">
      <view class="name">认证信息</view>
      <view style="color: gray;">
        <image style="width: 40rpx;height: auto;" src="/static/point/right.png" mode="widthFix" />
      </view>
    </view>

    <view class="info" style="border-bottom: none;">
      <view class="name">创建时间</view>
      <view style="color: gray;">{{formData.created_at_fmt}}</view>
    </view>

  </view>


</view>

<van-toast id="van-toast" />
  <van-dialog id="van-dialog" />