import dayjs from "../../../../libs/dayjs"

import {
  allot_order_list,
  deliver_assign_qr,
  deliver_assign_delete,
} from '../../../../apis/servicePoint/delivery';


import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
  dealFenToYuan,
} from '../../../../utils/dict';

import regeneratorRuntime from 'regenerator-runtime'
import Toast from '@vant/weapp/toast/toast';

const app = getApp()
Component({

  properties: {
    refreshPart: {
      type: Object,
      observer: function (newVal, oldVal) {
        if (newVal.active === 2) {
          this.querylist()
        }
      }
    },
    minDate: {
      type: Number,
    },
    maxDate: {
      type: Number,
    },
    nowStamp: {
      type: Number,
      observer: function (newVal, oldVal) {
        if (newVal != 0) {
          this.initDate()
          this.querylist()
        }
      }
    }
  },


  /**
   * 组件的初始数据
   */
  data: {
    nowStampFmt: '',
    show: false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    servicePointID: "647d77ef1db1e622b23c3339",
    list: [], // 列表
    statusActive: "doing",
    currentLocation: {
      latitude: 25.03171,
      longitude: 102.75293
    },
    markers: [], //标记点列表
    markerss: [],
    showDeliver: false,
    selectDeliverInfo: {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    }, // 选中信息
    showQr: false,
    qrData: '',
    loading: false,
    showOperate: false,
    actions: [{
      name: '删除',
      color: '#ee0a24',
      subname: '谨慎删除，不可恢复',
      value: "del"
    }, ],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    dealTimeToDay(at) {
      return dayjs(at).format('MM-DD')
    },
    onDisplay() {
      this.setData({
        show: true
      });
    },
    onClose() {
      this.setData({
        show: false
      });
    },
    onConfirm(e) {
      const now = e.detail;
      let time_now = now.getTime()
      this.setData({
        show: false,
      });
      this.triggerEvent("calendarUpdate", time_now)
    },

    switchStatus(e) {
      let name = e.detail.name

      let showDeliver = -this.data.showDeliver

      if(name==="done"){
        showDeliver = false
      }
      let selectDeliverInfo = {
        buyer_num: 0,
        product_num: 0,
        weight: 0,
        weight_fmt: 0,
      } // 选中信息

      this.setData({
        showDeliver: showDeliver,
        statusActive: name,
        selectDeliverInfo: selectDeliverInfo,
      })
      this.querylist()
    },


    querylist() {
      let status = false
      if (this.data.statusActive == "done") {
        status = true
      }

      let data = {
        service_point_id: this.data.servicePointID,
        timestamp: this.properties.nowStamp,
        delivery_user_id: "",
        receive_has: status,
      }
      allot_order_list(data).then(res => {
        if (res.data.code == 0) {
          let list = []
          let markerss = []
          if (res.data.data) {
            list = res.data.data
            list.map((items, index) => {
              let content = {
                id: index,
                iconPath: "",
                latitude: items.address.location.latitude,
                longitude: items.address.location.longitude,
                width: 28,
                height: 32,
                callout: {
                  content: items.buyer_name,
                  fontSize: 16,
                  display: "ALWAYS"
                },
              }
              markerss.push(content)
            })
            list.forEach(item => {
              item.sort_weight_fmt = item.sort_weight / 1000

              item.selected = false
            })
          }

          this.setData({
            list,
            markerss: markerss
          })
        }
      })
    },

    // 导航
    mapNavigation(e) {
      console.log(e)
      let info = e.currentTarget.dataset.info
      wx.openLocation({
        latitude: info.address.location.latitude,
        longitude: info.address.location.longitude,
        name: info.buyer_name, //定位名称
        address: info.address.address, //具体地址
        scale: 15
      })
    },

    orderDetail(e) {
      let info = e.currentTarget.dataset.info
      console.log(info);
      let timestamp = this.properties.nowStamp
      let deliverStatus = this.data.statusActive
      let addr = JSON.stringify(info.address)
      let param = `?buyer_id=${info.buyer_id}&timestamp=${timestamp}&service_point_id=${this.data.servicePointID}&buyer_name=${info.buyer_name}&addr=${addr}&deliverStatus=${deliverStatus}`
      wx.navigateTo({
        url: '/pages/servicePoint/deliverDetail/index' + param,
      })
    },

    initDate() {
      let now = this.properties.nowStamp
      let nowStampFmt = this.dealTimeToDay(now)

      let selectDeliverInfo = {
        buyer_num: 0,
        product_num: 0,
        weight: 0,
        weight_fmt: 0,
      }

      this.setData({
        showDeliver: false,
        nowStampFmt: nowStampFmt,
        selectDeliverInfo: selectDeliverInfo,
      });
    },

    toDeliver() {
      // 
      this.setData({
        showDeliver: true,
      })
    },

    cancelDeliver() {
      let selectDeliverInfo = {
        buyer_num: 0,
        product_num: 0,
        weight: 0,
        weight_fmt: 0,
      } // 选中信息
      this.setData({
        showDeliver: false,
        selectDeliverInfo: selectDeliverInfo,
      })
    },


    select(e) {
      let index = e.currentTarget.dataset.index
      // let info = e.currentTarget.dataset.info

      let list = this.data.list

      let weight = 0
      let buyerSet = new Set()
      let product_num = 0

      list.forEach((item, i) => {
        if (index == i) {
          item.selected = !item.selected
        }

        if (item.selected) {
          weight += item.sort_weight
          buyerSet.add(item.buyer_id)
          product_num += item.sort_num
        }

      });

      let weight_fmt = (weight / 1000).toFixed(0)
      let selectDeliverInfo = {
        buyer_num: buyerSet.size,
        product_num,
        weight,
        weight_fmt
      }

      this.setData({
        list: list,
        selectDeliverInfo: selectDeliverInfo,
      })
    },

    getDeliverQr() {
      let buyer_id_list = []
      let list = this.data.list
      list.forEach((item, i) => {
        if (item.selected) {
          buyer_id_list.push(item.buyer_id)
        }
      });
      if (buyer_id_list.length < 1) {
        Toast("请选择配送会员")
        return
      }

      this.setData({
        loading: true,
      })

      console.log(buyer_id_list);

      let data = {
        service_point_id: this.data.servicePointID,
        buyer_id_list: buyer_id_list,
        timestamp: this.properties.nowStamp,
      }
      deliver_assign_qr(data).then(res => {
        if (res.data.code === 0) {
          this.setData({
            qrData: res.data.data,
            showQr: true,
          })
        }
      }).catch(err => {
        Toast(err.data.message)
      }).finally(f => {
        this.setData({
          loading: false,
        })
      })
    },

    onCloseShowQr() {
      this.querylist()
      this.setData({
        showQr: false,
      })
    },

    
  showOperate(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      info,
      showOperate: true,
    })
  },

  handleCloseAction(e) {
    this.setData({
      showOperate: false,
    })
  },

  hanldeSelect(e) {
    console.log(e);
    let v = e.detail.value
    if (v == "del") {
      // 删除
      let id = this.data.info.deliver_assign_id
      let data = {
        id,
      }

      deliver_assign_delete(data).then(res => {
        if (res.data.code === 0) {
          Toast("删除成功")
          this.querylist()
        }
      })
    }
  },

  },


  lifetimes: {
    attached: async function () {
      if (this.properties.nowStamp != 0) {
        this.querylist()
      }
    },
  },

})