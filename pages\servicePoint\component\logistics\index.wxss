
page{
  background-color: #f6f6f6;
}

.nav {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding-left: 20rpx;
}
.capsule-box{
  padding: 0 20rpx;
}

.calendar .van-popup{
  height: 300px !important;
}

.buyer_list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  padding: 10rpx;
}

.num_color{
  font-weight: bold;
}

.buyer_name {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.buyer_name .name{
  font-size: 32rpx;
  font-weight: bold;
}

.buyer_phone {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.buyer_phone_title {
  width: 4em;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantily_color {
  color: red;
}

.buyer_address,
.ship_quantily,
.quality_control_num {
  font-size: 26rpx;
}

.order_detail{
  font-size: 26rpx;
  background-color: orange;
  padding: 4rpx 14rpx;
  box-sizing: border-box;
  border-radius: 44rpx;
  color: #ffffff;
}


.footer_wrap{
  background-color: #ffffff;
  margin-top: 40rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.amount_to{
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* padding: 20rpx;
  box-sizing: border-box; */
  font-size: 28rpx;
}

.not-logistics-all{
  border: 1rpx solid red;
  white-space: nowrap;
  color: red;
  font-size: 30rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}