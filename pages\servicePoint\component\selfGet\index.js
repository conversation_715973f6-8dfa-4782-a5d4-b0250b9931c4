import dayjs from "../../../../libs/dayjs"

import {
  take_delivery_list,
} from '../../../../apis/servicePoint/takeDelivery';

import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
  dealFenToYuan,
} from '../../../../utils/dict';

import regeneratorRuntime from 'regenerator-runtime'

const app = getApp()
Component({

  properties: {
    refreshPart: {
      type: Object,
      observer: function (newVal, oldVal) {
        if (newVal.active === 0) {
          this.querylist()
        }
      }
    },
    minDate: {
      type: Number,
    },
    maxDate: {
      type: Number,
    },
    nowStamp: {
      type: Number,
      observer: function (newVal, oldVal) {
        if (newVal != 0) {
          this.initDate()
          this.querylist()
        }
      }
    }
  },

  data: {
    nowStampFmt: '',
    show: false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    servicePointID: "647d77ef1db1e622b23c3339",
    list: [], // 列表
  },

  /**
   * 组件的方法列表
   */
  methods: {
    dealTimeToDay(at) {
      return dayjs(at).format('MM-DD')
    },
    onDisplay() {
      this.setData({
        show: true
      });
    },
    onClose() {
      this.setData({
        show: false
      });
    },
    onConfirm(e) {
      const now = e.detail;
      let time_now = now.getTime()
      // let time_now_fmt = this.dealTimeToDay(time_now)
      this.setData({
        show: false,
      });
      this.triggerEvent("calendarUpdate", time_now)
      //  查询列表
      // this.querylist()
    },

    querylist() {
      //  查询列表
      let service_point_id = this.data.servicePointID
      let now = this.properties.nowStamp
      if (now == 0) {
        return
      }
      let data = {
        service_point_id: service_point_id,
        timestamp: now,
      }
      take_delivery_list(data).then(res => {
        if (res.data.code == 0) {
          let list = []
          if (res.data.data) {
            list = res.data.data
            list.forEach(item => {
              item.sort_weight_fmt = item.sort_weight / 1000
            })
          }
          this.setData({
            list
          })
        }
      })
    },

    orderDetail(e) {
      let info = e.currentTarget.dataset.info
      let timestamp = this.properties.nowStamp
      let addr = JSON.stringify(info.address)
      let param = `?buyer_id=${info.buyer_id}&timestamp=${timestamp}&service_point_id=${this.data.servicePointID}&buyer_name=${info.buyer_name}&addr=${addr}`
      console.log(param);
      wx.navigateTo({
        url: '/pages/servicePoint/selfGetDetail/index' + param,
      })
    },

    initDate() {
      let now = this.properties.nowStamp
      let nowStampFmt = this.dealTimeToDay(now)
      this.setData({
        nowStampFmt: nowStampFmt,
      });
    },
  },


  lifetimes: {
    attached: async function () {
      if (this.properties.nowStamp != 0) {
        this.querylist()
      }
    },
  },

})