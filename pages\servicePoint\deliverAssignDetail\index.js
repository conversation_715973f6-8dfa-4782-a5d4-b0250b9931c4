const app = getApp()
import {
  buyer_order_detail,
  confirm_receive,
  deliver_note_by_buyer,
  deliver_assign_list_order
} from '../../../apis/servicePoint/delivery';

import {
  logistics_Detail
} from '../../../apis/servicePoint/takeDelivery';
const uploadFile = require('../../../utils/uploadFile');
const util = require('../../../utils/util');
import {
  upload_sign,
} from '../../../utils/api';
import {
  categoryCoverProcess,
  dealTimeFormat1,
  deliverTypeDoor,
} from '../../../utils/dict';

import dayjs from "../../../libs/dayjs"

import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
Page({

  data: {
    fileList: [],
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    fileLists: [],
    fileListsLogistics: [],
    orderlistIds: [],
    imgList: [], //图片列表
    imgListLogistics: [], //图片列表
    deliver_note: {
      day_at: 0,
    }, // 配送单
    orderList: [],
    service_point_id: '',
    buyer_id: '',
    buyer_name: '',
    addr: {},
    timestamp: 0,
    logisticsImg: [],
    logisticsTime: 0,
    logisticsTimeFmt: 0,
    radio: '1',
    logisticsAutoReceiveHour: 12,
    //  操作栏
    showOperate: false,
    selectOrder: {}, // 选中订单
    showUploadLogistics: false, // 展示上传物流单
    deliverStatus:"doing",
    showUploadDeliver:false,
  },

  onLoad(options) {
    console.log(options, 890);

    let buyer_id = options.buyer_id
    let order_id_list = JSON.parse(options.order_id_list)
    let addr = JSON.parse(options.addr)

    let buyer_name = options.buyer_name
    let timestamp = parseInt(options.timestamp)
    if (!buyer_id || !timestamp) {
      wx.navigateBack()
    }

    this.setData({
      buyer_id,
      addr,
      buyer_name,
      timestamp,
      order_id_list,
    })

    this.queryOrderList()
  },

  returnBack() {
    wx.navigateBack()
  },

  dealTimeToDay(at) {
    return dayjs(at).format('YYYY-MM-DD')
  },

  dealTime(at) {
    return dayjs(at).format('YYYY-MM-DD HH:mm:ss')
  },


  queryOrderList() {
    let order_id_list = this.data.order_id_list
    
    let data = {
      order_id_list: order_id_list,
      // buyer_id: buyer_id,
    }
    deliver_assign_list_order(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data

        let showUploadDeliver = false
        list.forEach(item => {
          item.product_list.forEach(items => {
            items.sort_weight_fmt = items.sort_weight / 1000
          })
          if (item.order_status != 9) {
            showUploadDeliver = true
          }
        })

        this.setData({
          orderList:list,
          showUploadDeliver:showUploadDeliver,
        })
      }
    })
  },
 

  // 物流单
  previewRlogisticsImg(e) {
    console.log(4444, e)
    let src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src, // 当前显示图片的http链接
      urls: [src],
    })
  },
 

  // 时间
  onChange(e) {
    let a = e.detail
    this.setData({
      radio: a,
    })
  },


   // 上传
   afterRead(e) {
    console.log(e)
    let that = this
    let tempFilePaths = e.detail.file.url
    let fileType = e.detail.file.type
    let type = "order"
    // this.data.fileList = []
    upload_sign(type).then(res => {
      if (res.data.code == 0) {
        console.log(res)
        let uploadData = res.data.data;
        let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(tempFilePaths))
        uploadFile(uploadData.host, tempFilePaths, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
          if (data.statusCode == 200) {
            const {
              fileList = []
            } = that.data;
            fileList.push({
              url: that.data.imageUrl + newPath
            });
            let list = {
              type: fileType,
              origin_name: util.siding(tempFilePaths),
              name: newPath,
            }
            this.data.imgList.push(list)
            that.setData({
              fileLists: fileList,
              imgList: this.data.imgList
            });
            // this.data.fileLists.push(e.detail.list,
            // that.setData({
            //   "formData.video_file": list
            // })
          }
        })
      }
    })
  },

  delete(e) {
    let index = e.detail.index;
    this.data.fileLists.splice(index, 1);
    this.data.imgList.splice(index, 1);
    this.setData({
      fileLists: this.data.fileLists,
      imgList: this.data.imgList
    })
  },

  
  submit() {
    let that = this
    if (this.data.fileLists.length > 0) {
      Dialog.confirm({
          title: '提示',
          message: '确定交付订单',
        })
        .then(() => {
          // on confirm
          this.confirmReceive()
        })
        .catch(() => {
          // on cancel
        });
    } else {
      Toast('图片缺失');
    }
  },

 //配送完成
 confirmReceive() {
  let orderList = this.data.orderList
  let orderIDs = []

  orderList.forEach(item => {
    if (item.order_status != 9) {
      orderIDs.push(item.id)
    }
  });

  if (orderIDs.length < 1) {
    Toast("刷新")
    this.refresh()
    return
  }

  let data = {
    order_id_list: orderIDs,
    delivery_img_list: this.data.imgList
  }

  confirm_receive(data).then(res => {
    if (res.data.code == 0) {
      wx.showToast({
        title: '交付完成',
        icon: "none",
        duration: 1500
      })
      setTimeout(function () {
        this.refresh()
      }, 1500)
    }
  })
},

  refresh() {
    this.setData({
      showUploadDeliver:false,
    })
    this.queryOrderList()
  },

  openShowOperate(e) {
    let info = e.currentTarget.dataset.info
    console.log(info);
    if (info.logistics_time != 0) {
      info.logistics_time_fmt = dealTimeFormat1(info.logistics_time)
    }
    if (info.order_status === 9) {
      info.order_status_record.receive_time_fmt = dealTimeFormat1(info.order_status_record.receive_time)
    }

    this.setData({
      showOperate: true,
      selectOrder: info,
    })
  },

  closeShowOperate() {
    this.setData({
      showOperate: false,
    })
  },

  previewImg(e) {
    let src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src,
      urls: [src],
    })
  },

   // 导航
   mapNavigation(e) {
    console.log(e)
    let info = e.currentTarget.dataset.info
    wx.openLocation({
      latitude: info.address.location.latitude,
      longitude: info.address.location.longitude,
      name: info.buyer_name, //定位名称
      address: info.address.address, //具体地址
      scale: 15
    })
  },


})