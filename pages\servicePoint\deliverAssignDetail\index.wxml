<view class="container">
  <view class="buyer" style="padding:20rpx;box-sizing: border-box;">
    <view class="buyer-name" style="font-weight: bold;">
      {{buyer_name}}
    </view>
    <view style="font-size: 22rpx;margin-top: 20rpx;">
      <view class="user">
        <view class="name">{{addr.contact.name}} </view>
        <!-- <view>{{addr.contact.mobile_fmt}} </view> -->
      </view>
      <view class="custom">{{addr.address}}</view>
      <view class="location">{{addr.location.address}}</view>
    </view>
  </view>

  <view class="product_list_wrap">
    <view wx:for="{{orderList}}" wx:key="key" style="margin-bottom: 30rpx;">
      <view style="display: flex;align-items: center;">
        <view style="flex: 1;">
          <van-divider dashed contentPosition="left" textColor="#1989fa" borderColor="#1989fa" customStyle="margin:0">
            {{item.supplier_name}}
          </van-divider>
        </view>
        <image src="{{imageUrl+'icon/ellipsis.png'}}" data-info="{{item}}" bind:tap="openShowOperate" mode="widthFix" style="height: auto;width: 40rpx;padding-left: 20rpx;padding-right: 10rpx;"></image>
      </view>
      <view style="display: flex;align-items: center;">
        <view class="id-num">订单编号:{{item.id_num}}</view>
        <van-tag type="warning" wx:if="{{item.order_status===8}}">待收货</van-tag>
        <van-tag type="success" wx:if="{{!item.order_refund_all&&item.order_status===9}}">已收货</van-tag>
        <van-tag type="danger" wx:if="{{item.order_refund_all&&item.order_status===9}}">全缺</van-tag>
      </view>

      <view class="product_list" wx:for="{{item.product_list}}" wx:for-item="items" wx:key="key">
        <view class="product_cover">
          <image class="product_cover" src="{{imageUrl +categoryCoverProcess+ items.product_cover_img.name}}" mode="widthFix" />
        </view>
        <view class="product_content" style="{{items.sort_num==0?'color:#999999':''}}">
          <view class="product_title">{{items.product_title}}
            <van-tag type="danger" wx:if="{{items.sort_num===0}}">缺货</van-tag>
          </view>
          <view style="font-size: 24rpx; color: orange;">{{items.sku_name}}</view>
          <view class="product_tag">
            <!-- <view wx:if="{{items.non_standard_attr.fruit_class_name}}" style="{{items.sort_num==0?'color:#999999':'color:#0000ff;'}}">{{items.non_standard_attr.fruit_class_name}}</view>
            <view wx:if="{{items.width>0}}" style="margin-left: 10rpx;border: 2rpx solid #000000;border-radius: 66rpx;padding: 4rpx 10rpx;">{{items.width}}果</view> -->
            <!-- <view class="check_weight_tip">{{items.is_check_weight?'称重销售':'按件销售'}}</view> -->
          </view>
          <!-- <view class="order_param">订单数量<text class="sun_color" style="{{items.sort_num==0?'color:#999999':''}}">[{{items.num}}]</text>件/品控分拣<text class="sun_color" style="{{items.sort_num==0?'color:#999999':''}}">[{{items.sort_num}}]</text>件 <text style="margin-left: 20rpx;">重量: {{items.sort_weight_fmt}}kg</text></view> -->
          <view class="order_param">
            <text>分拣/订单: </text>
            <text style="color: {{items.sort_num==items.num?'':'red'}};">{{items.sort_num}}/{{items.num}}</text>
            <text style="margin-left: 50rpx;" wx:if="{{items.sort_num!=0}}">重量: {{items.sort_weight_fmt}} kg</text>
          </view>
        </view>
      </view>
      <view class="note" wx:if="{{item.order_note!=''}}">
        <text style="white-space: nowrap;color: red;">留言：</text>
        <view>
          {{item.order_note}}
        </view>
      </view>
    </view>
  </view>

  <view class="deliver-note" wx:if="{{deliver_note.day_at!==0}}">
    <!-- <van-button round type="info" size="small" bind:tap="upsertDeliverNote" wx:if="{{deliver_note.day_at===0}}">主动生成配送单</van-button> -->
    <!-- <view class="deliver-note-per"> -->
    <view class="notes">
      <view>
        <image src="{{imageUrl+'icon/deliverNote.png'}}" mode="widthFix" style="height: auto;width: 30rpx;margin-right: 10rpx;"></image>
        <text style="font-size: 36rpx;">{{deliver_note.day_at_format}} </text>
        <text style="font-size: 36rpx;">配送单</text>
      </view>
      <view style="padding: 20rpx 0;">
        <text style="font-size: 24rpx;">订单区间：</text>
        <view style="font-size: 24rpx;">[{{deliver_note.begin_at_format}} - {{deliver_note.end_at_format}}]</view>
      </view>
      <view>
        <text style="font-size: 24rpx;">生成时间：</text>
        <text style="font-size: 24rpx;">{{ deliver_note.created_at_format}} </text>
      </view>
    </view>
    <view class="look" bindtap="handleViewDeliverNote" data-info="{{deliver_note}}" data-path="{{deliver_note.file_path}}">查看</view>
    <!-- </view> -->
    <!-- <van-button round type="info" bind:tap="upsertDeliverNote" size="small">更新配送单</van-button> -->
    <!-- <van-button round plain  type="info" bind:tap="refreshDeliverNote" size="small" style="margin: 0 10rpx;">刷新</van-button> -->
  </view>


  <view class="receive" wx:if="{{showUploadDeliver}}">
    <view>订单交付</view>
    <view class="photograph">
      <van-uploader bind:delete="delete" file-list="{{ fileLists }}" max-count="3" name="" accept="image" bind:after-read="afterRead" />
    </view>
    <view class="submit" bindtap="submit">
      确认交付
    </view>
  </view>


  <view style="width: 100%;height: 140rpx;">
  </view>


  <!-- 订单信息 -->
  <van-action-sheet style="margin-bottom: 90rpx;" show="{{ showOperate  }}" bind:close="closeShowOperate" close-on-click-overlay>
    <view style="margin: 30rpx 30rpx;">
      <view>
        <van-divider dashed contentPosition="left" textColor="#1aad19" borderColor="#1aad19" customStyle="margin:0">收货信息</van-divider>
        <view wx:if="{{!selectOrder.order_refund_all&&selectOrder.order_status===9}}">
          <view style="font-size: 28rpx;padding: 10rpx 0;">收货时间：{{selectOrder.order_status_record.receive_time_fmt}}</view>
          <view wx:if="{{selectOrder.delivery_img_list&&selectOrder.delivery_img_list.length>0}}">
            <view wx:for="{{selectOrder.delivery_img_list}}" wx:key="keys" style="display: flex;flex-wrap: wrap;gap: 30rpx;">
              <van-image width="100" radius="10" height="100" src="{{imageUrl + item.name}}" data-src="{{imageUrl + item.name}}" bind:tap="previewImg" />
            </view>
          </view>
        </view>
        <view wx:if="{{selectOrder.order_status===8}}" style="font-size: 28rpx;">
          待收货
        </view>
      </view>
    </view>
  </van-action-sheet>

  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>