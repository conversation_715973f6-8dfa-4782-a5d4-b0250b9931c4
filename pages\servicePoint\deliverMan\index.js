import dayjs from "../../../libs/dayjs"

import {
  deliver_assign_list,
} from '../../../apis/servicePoint/delivery';

import {
  categoryCoverProcess,
  userIDKey,
} from '../../../utils/dict';

import regeneratorRuntime from 'regenerator-runtime'
import Toast from '@vant/weapp/toast/toast';

const app = getApp()

Page({

  data: {
    key: '',
    location: {
      latitude: 25.03171,
      longitude: 102.75293
    },
    nowStampFmt: '',
    nowStamp: 0,
    show: false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    statusActive: "doing",
    markers: [], //标记点列表
    markerss: [],
    showDeliver: false,
    selectDeliverInfo: {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    }, // 选中信息
    showQr: false,
    qrData: '',
    loading: false,

    list: [], // 列表
    // showOperate: true,
    info: {},

  },

  async onLoad(options) {

  },

  async onShow() {
    await this.initDate()
    this.queryList()
  },


  initDate() {
    return new Promise(callback => {
      let nowStamp = dayjs().valueOf()
      let nowStampFmt = dayjs(nowStamp).format('MM-DD')

      this.setData({
        nowStamp,
        nowStampFmt
      })
      callback()
    })
  },

  // 位置
  queryLocation() {
    return new Promise(callback => {
      let that = this
      wx.getLocation({
        type: 'wgs84',
        success(res) {
          const latitude = res.latitude
          const longitude = res.longitude
          let location = {
            latitude: latitude,
            longitude: longitude
          }
          that.setData({
            location
          })
        }
      })
      callback()
    })
  },

  queryList() {
    let user_id = wx.getStorageSync(userIDKey)
    let now = this.data.nowStamp
    let data = {
      user_id: user_id,
      timestamp: now,
      
    }
    deliver_assign_list(data).then(res => {
      if (res.data.code === 0) {
        let list = []
        let markerss = []
        if (res.data.data) {
          list = res.data.data
          list.forEach((item, index) => {
            item.sort_weight_fmt = (item.sort_weight / 1000).toFixed(1)
            let mobile = item.address.contact.mobile
            item.address.contact.mobile_fmt = this.dealMobile(mobile)

            let content = {
              id: index,
              iconPath: "",
              latitude: item.address.location.latitude,
              longitude: item.address.location.longitude,
              width: 28,
              height: 32,
              callout: {
                content: item.buyer_name,
                fontSize: 16,
                display: "ALWAYS"
              },
            }
            markerss.push(content)
          });
        }


        this.setData({
          list,
          markerss
        })
      }
    })
  },

  copyMobile(e) {
    let mobile = e.currentTarget.dataset.mobile
    // wx.setClipboardData({
    //   data: mobile,
    // })
    wx.makePhoneCall({
      phoneNumber: mobile,
    })
  },

  dealMobile(s) {
    const reg = /(\d{3})\d{4}(\d{4})/;
    return s.replace(reg, "$1****$2");
  },

  orderDetail(e) {
    let info = e.currentTarget.dataset.info
    console.log(info);
    let timestamp = this.properties.nowStamp
    let param = `?buyer_id=${info.buyer_id}&timestamp=${timestamp}`
    let addr = JSON.stringify(info.address)
    param += `&addr=${addr}`
    param += `&buyer_name=${info.buyer_name}`

    let order_id_list = JSON.stringify(info.order_id_list)
    param += `&order_id_list=${order_id_list}`

    console.log(param);
    wx.navigateTo({
      url: '/pages/servicePoint/deliverAssignDetail/index' + param,
    })
  },

  mapNavigation(e) {
    console.log(e)
    let info = e.currentTarget.dataset.info
    wx.openLocation({
      latitude: info.address.location.latitude,
      longitude: info.address.location.longitude,
      name: info.buyer_name, //定位名称
      address: info.address.address, //具体地址
      scale: 15
    })
  },

  showOperate(e) {
    let info = e.currentTarget.dataset.info

    this.setData({
      info,
      showOperate: true,
    })
  },

  handleCloseAction(e) {
    this.setData({
      showOperate: false,
    })
  },

  onUnload() {
  },


  onPullDownRefresh() {

  },
})