<view class="nav" style="position: fixed;z-index: 2;">
  <view>时间：{{nowStampFmt}}</view>
</view>

<view class="container" style="position: relative;">
  <view class="buyer_list_wrap" wx:if="{{list}}" style="box-sizing: border-box;">
    <view class="buyer_list" wx:for="{{list}}" wx:key="key" data-info="{{item}}">
      <view class="buyer_content" style="width:100%">
        <view class="buyer_name">
          <view class="name">
            {{item.buyer_name}}
          </view>
          <view style="display: flex;align-items: center;">
            <view wx:if="{{!item.receive}}" data-info="{{item}}" class="doing">未完成</view>
            <view wx:if="{{item.receive}}" data-info="{{item}}" class="done">已完成</view>
            <view catch:tap="mapNavigation" data-info="{{item}}" class="not">导航 ></view>
          </view>
        </view>
        <view style="display: flex;justify-content: space-between;">
          <view class="left">
            <view class="buyer_phone" style="margin: 10rpx 0;">
              <view>虚拟电话: {{item.address.contact.mobile_fmt}}</view>
              <view class="copy" bind:tap="copyMobile" data-mobile="{{item.address.contact.mobile}}">拨打</view>
              <view style="margin-left: 20rpx;">联系人: {{item.address.contact.name}}</view>
            </view>
            <view class="buyer_address" style="margin: 10rpx 0;">定位: {{item.address.location.address}}</view>
            <view class="buyer_address" style="margin: 10rpx 0;">地址: {{item.address.address}}</view>
            <view style="display: flex;gap: 40rpx;">
              <view class="ship_quantily">发货: <text class="num_color">{{item.sort_num}} 件</text></view>
              <view class="quality_control_num">重量: <text class="num_color">{{item.sort_weight_fmt}} kg</text></view>
              <view>
              </view>
            </view>
          </view>
          <view class="right" style="display: flex;align-items: center;">
            <view data-info="{{item}}" bindtap="orderDetail" style="padding: 20rpx 10rpx;">
              <image src="{{imageUrl+'icon/right-slim.png'}}" mode="widthFix" style="height: auto;width: 40rpx;"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view wx:if="{{showDeliver}}" class="deliver-part">
    <view style="display: flex;justify-content: space-between;">
      <view class="left">
        <view style="font-size: 34rpx;font-weight: bold;margin-bottom: 30rpx;">
          配送指派
        </view>
        <view style="display: flex;">
          <view class="title">会员：</view>
          <view class="value">{{selectDeliverInfo.buyer_num}}</view>
        </view>
        <view style="display: flex;">
          <view class="title">商品：</view>
          <view class="value">{{selectDeliverInfo.product_num}}</view>
          <view class="value" style="margin-left: 10rpx;">({{selectDeliverInfo.weight_fmt}}kg)</view>
        </view>
      </view>
      <view class="right">
        <view class="right-qr" bind:tap="getDeliverQr">
          <image src="{{imageUrl+'icon/qr.png'}}" mode="heightFix" class="img-qr" bindtap=""></image>
          <view style="font-size: 28rpx;">
            配送码
          </view>
        </view>
      </view>
    </view>

  </view>

  <view wx:if="{{list.length>0}}" class="delivery_maps">
    <map id="map" longitude="{{location.longitude}}" latitude=" {{location.latitude}}" markers="{{markerss}}" scale="12" show-location style="width: 100%; height: 100%;"></map>
  </view>

  <van-empty wx:else description="暂无内容" />

  <view style="width: 100%;height: 240rpx;">

  </view>
</view>



<van-dialog use-slot title="" show="{{ showQr }}" bind:close="onCloseShowQr" bind:confirm="onCloseShowQr">
  <view style="text-align: center;padding: 20rpx;">扫码配送码，绑定配送信息</view>
  <view style="display: flex;align-items: center;justify-content: center;">
    <image src="data:image/png;base64,{{qrData}}" style="width: 400rpx;margin-bottom: 30rpx; height: auto;" mode="widthFix"></image>
  </view>
</van-dialog>




<van-toast id="van-toast" />
<van-dialog id="van-dialog" />