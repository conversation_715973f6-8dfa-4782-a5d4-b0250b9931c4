.container {
  padding-top: 40rpx;
  background-color: #f6f6f6;
}

.nav {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding-left: 20rpx;
}

.capsule-box {
  padding: 0 20rpx;
}

.calendar .van-popup {
  height: 300px !important;
}

.buyer_list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  padding: 10rpx;
}

.num_color {
  font-weight: bold;
}

.buyer_name {
  display: flex;
  justify-content: space-between;
}

.buyer_name .name {
  font-size: 32rpx;
  font-weight: bold;
}

.buyer_phone {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.copy{
  color: #9c9c9c;
  border: 1rpx solid #9c9c9c;
  border-radius: 8rpx;
  box-sizing: border-box;
  font-size:24rpx;
  margin-left: 8rpx;
  cursor: pointer;
}

.buyer_phone_title {
  width: 4em;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantily_color {
  color: red;
}

.buyer_address,
.ship_quantily,
.quality_control_num {
  font-size: 26rpx;
}

.order_detail {
  font-size: 26rpx;
  background-color: orange;
  padding: 4rpx 14rpx;
  box-sizing: border-box;
  border-radius: 44rpx;
  color: #ffffff;
}


.footer_wrap {
  background-color: #ffffff;
  margin-top: 40rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.amount_to {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* padding: 20rpx;
  box-sizing: border-box; */
  font-size: 28rpx;
}

.not {
  border: 1rpx solid #00897b;
  white-space: nowrap;
  color: #00897b;
  font-size: 26rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.doing{
  background-color:red;
  white-space: nowrap;
  color: #ffffff;
  font-size: 26rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.done{
  background-color: #07c160;
  white-space: nowrap;
  color: #ffffff;
  font-size: 26rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.delivery_maps {
  background-color: #e5e5e5;
  width: 100%;
  /* height: 180rpx; */
  height: 800rpx;
  margin-top: 20rpx;
}


/*  配送 */
.deliver-icon {
  color: #ffffff;
  background-color: #1989fa;
  border-radius: 12rpx;
  padding: 2rpx 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.deliver-part {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  padding: 10rpx;
  z-index: 1;
}

.deliver-part .title {
  font-size: 26rpx;
  padding: 4rpx 0;
}

.deliver-part .value {
  font-size: 26rpx;
  padding: 4rpx 0;
}


.deliver-part .right{
  margin-right: 30rpx;
  position: relative;
  box-sizing: border-box;
}


.deliver-part .right-qr{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.deliver-part .img-qr{
  position: relative;
  flex: 1;
  height: 100%;
  width: auto;
}