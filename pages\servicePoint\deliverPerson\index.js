import {
  dealTimeFormat2,
  servicePointIDKey
} from '../../../utils/dict';
import {
  delivery_man_list,
  delivery_man_create,
  delivery_man_update,
  delivery_man_delete
} from '../../../apis/servicePoint/deliveryPerson'
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';


Page({

  data: {
    list: [],
    show_dialog: false,
    user_name: '',
    mobile: '',
    desc: '',
    is_submit: true,
    id: '',
    show_active: false,
    actions: [

      {
        id: 1,
        name: '编辑',
      },
      {
        id: 2,
        name: '删除',
        color: '#ee0a24',
        subname: '删除后不可恢复',
      },

    ],
  },

  onLoad(options) {
    this.deliveryManList()
  },
  // 配送员列表
  deliveryManList() {
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey)
    }
    delivery_man_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if (!list) {
          list = []
        }
        list.forEach(ele => {
          ele.created_at_fmt = dealTimeFormat2(ele.created_at)
        })
        this.setData({
          list,
        })
      }
    }).catch(err => {
      Toast(err.data.message)
    })

  },

  //添加
  handleAdd() {
    this.setData({
      show_dialog: true,
      mobile: '',
      user_name: '',
      desc: '',
      id: '',
    })
  },
  // 名字
  inputName(e) {
    let value = e.detail.value
    this.setData({
      user_name: value
    })
  },
  // 手机号
  inputMobile(e) {
    let phoneNumber = (e.detail.value).replace(/\s+/g, "");
    phoneNumber = phoneNumber.slice(0, 11)
    this.setData({
      mobile: phoneNumber
    })
  },

  // 描述
  inputDesc(e) {
    let desc = e.detail.value
    this.setData({
      desc,
    })
  },

  // 提交
  submit() {
    if (this.data.user_name == '') {
      Toast('请填写配送员姓名')
      return
    }

    if (this.data.mobile == '') {
      Toast('请填写配送员电话')
      return
    }
    this.setData({
      is_submit: false
    })
    let data = {
      mobile: this.data.mobile,
      service_point_id: wx.getStorageSync(servicePointIDKey),
      user_name: this.data.user_name,
      desc: this.data.desc
    }

    delivery_man_create(data).then(res => {
      if (res.data.code == 0) {
        Toast('添加成功')
        this.deliveryManList()
        setTimeout(() => {
          this.handleCancle()
        }, 500)
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_submit: true
      })
    })
  },
  // 取消
  handleCancle() {
    this.setData({
      mobile: '',
      user_name: '',
      desc: '',
      id: '',
      show_dialog: false,
      is_submit: true,
    })
  },

  handleMore(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      show_active: true,
      id: info.id,
      user_name: info.user_name,
      desc: info.desc,
      mobile: info.mobile,
    })
  },
  onClose() {
    this.setData({
      show_active: false,
    })
  },
  onSelect(e) {
    let select_name = e.detail.name
    if (select_name == '编辑') {
      this.setData({
        show_dialog: true,
      })
    }
    if (select_name == '删除') {

      this.hadleDel()
    }
  },

  // 保存
  handleSave() {
    if (this.data.user_name == '') {
      Toast('请填写配送员姓名')
      return
    }

    this.setData({
      is_submit: false
    })
    let data = {
      id: this.data.id,
      user_name: this.data.user_name,
      desc: this.data.desc
    }

    delivery_man_update(data).then(res => {
      if (res.data.code == 0) {
        Toast('保存成功')
        this.deliveryManList()
        setTimeout(() => {
          this.handleCancle()
        }, 500)

      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_submit: true
      })
    })
  },
  // 删除
  hadleDel() {
    Dialog.confirm({
        title: '删除',
        message: '确认删除？',
      })
      .then(() => {
        let data = {
          id: this.data.id
        }
        delivery_man_delete(data).then(res => {
          if (res.data.code == 0) {
            Toast('删除成功')
            this.deliveryManList()
          }
        }).catch(err => {
          Toast(err.data.message)
        })
      })
      .catch(() => {});
  },

  onShow() {

  },

})