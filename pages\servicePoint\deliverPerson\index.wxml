<view class="container">

  <view wx:for="{{list}}" wx:key="id" class="list">
    <view style="font-size: 30rpx;margin: 10rpx 0;">
      <text style="margin-right: 10rpx;">{{item.user_name}}</text>
      <text style="color: #888686;">{{item.mobile}}</text>
    </view>

    <view class="desc">{{item.desc}}</view>
    <!-- <view class="btn">
      <view class="edit" bind:tap="handleEdit" data-info="{{item}}">编辑</view>
      <view class="del" bind:tap="hadleDel" data-id="{{item.id}}">删除</view>
    </view> -->

    <view class="btn">
      <view style="font-size: 26rpx;color: #888686;">{{item.created_at_fmt}}</view>
      <view style="color: grey;font-size: 26rpx;" bind:tap="handleMore" data-info="{{item}}">更多</view>
    </view>
  </view>
</view>
<view class="add" bind:tap="handleAdd">
  <view class="add-btn">添加</view>
</view>


<van-dialog use-slot show="{{ show_dialog }}" show-cancel-button='{{false}}' zIndex="999" showConfirmButton='{{false}}'>
  <view style="padding: 20rpx;">
    <view style="text-align: center;">{{id == ""?'添加':'编辑'}}</view>
    <view>
      <view style="box-sizing: border-box;display: flex;align-items: center;">
        <text style="width: 140rpx;white-space: nowrap;">姓名：</text>
        <input class="edit-input" bindinput="inputName" maxlength="8" value="{{ user_name}}" />
      </view>

      <view style="box-sizing: border-box;display: flex;align-items: center;margin-top: 20rpx;">
        <text style="width: 140rpx;white-space: nowrap;">电话：</text>
        <input class="edit-input" type="number" bindinput="inputMobile" disabled="{{id == ''? false:true}}" value="{{ mobile}}" />
      </view>

      <view style="box-sizing: border-box;margin-top: 20rpx;">
        <text>描述：</text>
        <textarea class="desc-input" rows="2" bindinput="inputDesc" maxlength="20" value="{{ desc}}" style="height: 150rpx;"></textarea>
      </view>
    </view>
    <view style="display: flex;margin-top: 50rpx;justify-content: space-around;">
      <view class="cancle" bind:tap="handleCancle">取消</view>
      <view wx:if="{{is_submit && id == ''}}" class="confirm" bind:tap="submit">提交</view>
      <view wx:if="{{!is_submit && id == ''}}" class="confirm">提交中...</view>

      <view wx:if="{{is_submit && id !== ''}}" class="confirm" bind:tap="handleSave">保存</view>
      <view wx:if="{{!is_submit && id !== ''}}" class="confirm">保存中...</view>
    </view>

  </view>
</van-dialog>

<van-action-sheet show="{{ show_active }}" actions="{{ actions }}" bind:cancel="onClose" bind:close="onClose" bind:select="onSelect" cancel-text="取消" close-on-click-overlay close-on-click-action/>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />