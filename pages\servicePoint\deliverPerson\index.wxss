.container {
  min-height: 100vh;
  background-color: #eee;
  padding: 20rpx;
  box-sizing: border-box;
}

.list {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.desc {
  font-size: 26rpx;
  color: #888686;
}

.add {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: center;
}
.add-btn{
  color: #fff;
  font-size: 34rpx;
  background-color: #ff9937;
  border-radius: 40rpx;
  padding: 20rpx 0rpx;
  width: 300rpx;
  text-align: center;
  margin: 20rpx 0 100rpx 0;
}


.btn {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.edit {
  background-color: #f7d28e;
  color: #fff;
  border-radius: 10rpx;
  padding: 8rpx 20rpx;
  margin-right: 10rpx;
  font-size: 26rpx;
}

.del {
  background-color: #f39393;
  color: #fff;
  border-radius: 10rpx;
  padding: 8rpx 20rpx;
  margin-right: 10rpx;
  font-size: 26rpx;
}

.edit-input {
  border-bottom: 1rpx solid #d6d4d4;
  padding: 10rpx;
  color: #5f5f5f;
}

.desc-input {
  border: 1rpx solid #d6d4d4;
  padding: 10rpx;
  color: #5f5f5f;
  margin-top: 10rpx;
  box-sizing: border-box;
}

.cancle {
  background-color: #a7a7a7;
  border-radius: 10rpx;
  padding: 8rpx 0;
  width: 150rpx;
  text-align: center;
  color: #fff;
}

.confirm {
  background-color: #f7bb4d;
  border-radius: 10rpx;
  padding: 8rpx 0;
  width: 240rpx;
  text-align: center;
  color: #fff;

}