import dayjs from "../../../libs/dayjs"
import {
  allot_order_list,
  deliver_assign_qr,
  deliver_assign_delete,
  deliver_assign_create
} from '../../../apis/servicePoint/delivery';
import {
  delivery_man_list
} from '../../../apis/servicePoint/deliveryPerson'

import {
  categoryCoverProcess,
  servicePointIDKey,
  dealTimeFormat2
} from '../../../utils/dict';
import Toast from '@vant/weapp/toast/toast';


const app = getApp()

Page({
  data: {
    nowStampFmt: 0,
    show: false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    servicePointID: "",
    currentLocation: {
      latitude: 25.03171,
      longitude: 102.75293
    },
    statusActive: "doing",
    markers: [], //标记点列表
    markerss: [],
    showDeliver: false,
    selectDeliverInfo: {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    }, // 选中信息
    list: [], // 列表
    minDate: 0,
    maxDate: 0,
    now: 0,
    showQr: false,
    qrData: '',
    loading: false,
    showOperate: false,
    actions: [{
      name: '删除指派',
      color: '#ee0a24',
      subname: '谨慎删除，不可恢复',
      value: "del"
    }, ],
    is_delivery: false,
    show_active: false,
    delivery_actions_list: [],
    delivery_info: {},
    is_sure: true,
    buyer_id_list: []
  },

  onLoad() {
    let time = app.globalData.delivery_time //全局时间
    let point_id = wx.getStorageSync(servicePointIDKey)
    let minDate = dayjs().subtract(1, "month").startOf('day').valueOf()
    let maxDate = dayjs().endOf('day').valueOf()
    this.setData({
      now: time,
      servicePointID: point_id,
      nowStampFmt: this.dealTimeToDay(time),
      minDate: minDate,
      maxDate: maxDate,
    })
    this.querylist()
  },


  // 查询服务仓配送员信息
  queryDeliveryManList() {
    let data = {
      service_point_id: this.data.servicePointID
    }
    delivery_man_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if (!list) {
          list = []
        }
        this.setData({
          show_active: true,
          delivery_actions_list: list
        })
      }
    }).catch(err => {
      Toast(err.data.message)
    })
  },

  handleClose() {
    this.setData({
      show_active: false,
    })
  },
  hanldeSelectDelivery(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      show_active: false,
      delivery_info: info,
      is_delivery: true
    })
  },
  handleCancle() {
    this.setData({
      is_delivery: false
    })
  },

  handleSure() {
    if (this.data.buyer_id_list.length < 1) {
      Toast("请选择配送会员")
      return
    }

    let data = {
      delivery_man_id: this.data.delivery_info.id,
      service_point_id: this.data.servicePointID,
      buyer_id_list: this.data.buyer_id_list,
      timestamp: this.data.now,
      deliver_type: 1
    }

    deliver_assign_create(data).then(res => {
      if (res.data.code == 0) {
        Toast('指派成功')
        this.querylist()
        this.cancelDeliver()
        this.setData({
          is_sure: true,
          is_delivery: false
        })
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_sure: true
      })
    })

  },

  // 电话拨打
  makePhoneCall(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone //仅为示例，并非真实的电话号码
    })
  },

  dealMoney(fen) {
    return Math.round(fen) / 100
  },
  dealTimeToDay(at) {
    return dayjs(at).format('MM-DD')
  },
  onDisplay() {
    this.setData({
      show: true
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },

  onConfirm(e) {
    const now = e.detail;
    let time_now = now.getTime()
    let time_now_fmt = this.dealTimeToDay(time_now)
    app.globalData.delivery_time = now.getTime()
    this.setData({
      show: false,
      now: time_now,
      nowStampFmt: time_now_fmt
    });
    //  查询列表
    this.querylist()
  },


  switchStatus(e) {
    let name = e.detail.name
    let showDeliver = -this.data.showDeliver
    if (name === "done") {
      showDeliver = false
    }
    let selectDeliverInfo = {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    } // 选中信息

    this.setData({
      showDeliver: showDeliver,
      statusActive: name,
      selectDeliverInfo: selectDeliverInfo,
    })
    this.querylist()
  },

  querylist() {
    //  查询列表
    let status = false
    if (this.data.statusActive == "done") {
      status = true
    }
    let data = {
      service_point_id: this.data.servicePointID,
      timestamp: this.data.now,
      delivery_user_id: "",
      receive_has: status,
    }
    allot_order_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        let markerss = []
        if (res.data.data) {
          list = res.data.data
          list.map((items, index) => {
            let content = {
              id: index,
              iconPath: "",
              latitude: items.address.location.latitude,
              longitude: items.address.location.longitude,
              width: 28,
              height: 32,
              callout: {
                content: items.buyer_name,
                fontSize: 16,
                display: "ALWAYS"
              },
            }

            markerss.push(content)
          })
          list.forEach(item => {
            item.sort_weight_fmt = item.sort_weight / 1000
            item.selected = false
          })
        }

        this.setData({
          list,
          markerss: markerss,
        })
      }
    })
  },
  // 导航
  mapNavigation(e) {
    let info = e.currentTarget.dataset.info
    wx.openLocation({
      latitude: info.address.location.latitude,
      longitude: info.address.location.longitude,
      name: info.buyer_name, //定位名称
      address: info.address.address, //具体地址
      scale: 15
    })
  },


  orderDetail(e) {
    let info = e.currentTarget.dataset.info
    let timestamp = this.data.now
    let deliverStatus = this.data.statusActive
    let addr = JSON.stringify(info.address)
    let addr_id = info.address.address_id
    let param = `?buyer_id=${info.buyer_id}&timestamp=${timestamp}&service_point_id=${this.data.servicePointID}&buyer_name=${info.buyer_name}&addr_id=${addr_id}&deliverStatus=${deliverStatus}`
    wx.navigateTo({
      url: '/pages/servicePoint/deliverDetail/index' + param,
    })
  },


  toDeliver() {
    // 
    this.setData({
      showDeliver: true,
    })
  },

  cancelDeliver() {
    let selectDeliverInfo = {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    } // 选中信息
    let list = this.data.list
    list.forEach((item, i) => {
      if (item.selected) {
        item.selected = !item.selected
      }
    });
    this.setData({
      list,
      showDeliver: false,
      buyer_id_list: [],
      selectDeliverInfo: selectDeliverInfo,
      delivery_info: {},
      is_delivery: false
    })
  },

  select(e) {
    let index = e.currentTarget.dataset.index
    let list = this.data.list
    let weight = 0
    let buyerSet = new Set()
    let buyer_id_list = []
    let product_num = 0
    list.forEach((item, i) => {
      if (index == i) {
        item.selected = !item.selected
      }
      if (item.selected) {
        weight += item.sort_weight
        buyerSet.add(item.buyer_id)
        product_num += item.sort_num
        buyer_id_list.push(item.buyer_id)
      }
    });

    let weight_fmt = (weight / 1000).toFixed(0)
    let selectDeliverInfo = {
      buyer_num: buyerSet.size,
      product_num,
      weight,
      weight_fmt
    }
    this.setData({
      list: list,
      buyer_id_list,
      selectDeliverInfo: selectDeliverInfo,
    })
  },
  // getDeliverQr() {
  //   let buyer_id_list = []
  //   let list = this.data.list
  //   list.forEach((item, i) => {
  //     if (item.selected) {
  //       buyer_id_list.push(item.buyer_id)
  //     }
  //   });
  //   if (buyer_id_list.length < 1) {
  //     Toast("请选择配送会员")
  //     return
  //   }
  //   this.setData({
  //     loading: true,
  //   })

  //   let data = {
  //     service_point_id: this.data.servicePointID,
  //     buyer_id_list: buyer_id_list,
  //     timestamp: this.data.now,
  //   }
  //   deliver_assign_qr(data).then(res => {
  //     if (res.data.code === 0) {
  //       this.setData({
  //         qrData: res.data.data,
  //         showQr: true,
  //       })
  //     }
  //   }).catch(err => {
  //     Toast(err.data.message)
  //   }).finally(f => {
  //     this.setData({
  //       loading: false,
  //     })
  //   })
  // },

  onCloseShowQr() {
    this.querylist()
    this.setData({
      showQr: false,
    })
  },

  showOperate(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      info,
      showOperate: true,
    })
  },

  handleCloseAction(e) {
    this.setData({
      showOperate: false,
    })
  },

  hanldeSelect(e) {
    let v = e.detail.value
    if (v == "del") {
      let id = this.data.info.deliver_assign_id
      let data = {
        id,
      }

      deliver_assign_delete(data).then(res => {
        if (res.data.code === 0) {
          Toast("删除成功")
          this.querylist()
        }
      })
    }
  },


  onShow() {

  },



})