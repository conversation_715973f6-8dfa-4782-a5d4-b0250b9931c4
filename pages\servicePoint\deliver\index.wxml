<view>
  <view class="nav">
    <view class="capsule-box">
      <view style="display: flex;">
        <view bindtap="onDisplay">时间：{{nowStampFmt}}</view>
        <view bindtap="toDeliver" class="deliver-icon" wx:if="{{statusActive=='doing'&&!showDeliver}}">指派配送员</view>
        <view bindtap="cancelDeliver" class="deliver-icon" wx:if="{{statusActive=='doing'&&showDeliver}}">取消指派</view>
      </view>
      <view>
        <van-tabs style="position: relative;" active="{{ statusActive }}" bind:change="switchStatus" animated>
          <van-tab title="配送中" name="doing"></van-tab>
          <van-tab title="已配送" name="done"></van-tab>
        </van-tabs>
      </view>
      <van-calendar class="calendar" show="{{ show }}" default-date="{{ now}}" max-date="{{maxDate}}" min-date="{{ minDate }}" show-confirm="{{false}}" show-title="{{false}}" bind:close="onClose" bind:confirm="onConfirm" allow-same-day="true" z-index="9999" />
    </view>
  </view>

  <view class="container" style="padding-top: 60rpx;">
    <view class="buyer_list_wrap" wx:if="{{list}}" style="padding-top: 0 !important;box-sizing: border-box;margin-top:110rpx;">
      <view class="buyer_list" wx:for="{{list}}" wx:key="key" data-info="{{item}}">
        <view>
          <image src="/static/point/select.png" wx:if="{{showDeliver&&item.selected}}" bind:tap="select" data-info="{{item}}" data-index="{{index}}" mode="widthFix" style="height: auto;width: 50rpx;"></image>
          <image src="/static/point/select_.png" wx:if="{{showDeliver&&!item.selected}}" bind:tap="select" data-info="{{item}}" data-index="{{index}}" mode="widthFix" style="height: auto;width: 50rpx;"></image>
        </view>
        <view class="buyer_content" style="{{deliveryActive==0?'width:calc(100% - 80rpx)':'width:100%'}}">
          <view class="buyer_name">
            <view class="name">
              {{item.buyer_name}}
            </view>

            <view style="display: flex;align-items: center;">
              <view wx:if="{{item.deliver_assign&&statusActive=='doing'}}" data-info="{{item}}" class="has-assign">已指派</view>
              <view wx:if="{{!item.deliver_assign&&statusActive=='doing'}}" data-info="{{item}}" class="not-assign">未指派</view>
              <view catch:tap="mapNavigation" data-info="{{item}}" class="not">导航 ></view>
            </view>
          </view>
          <view style="display: flex;justify-content: space-between;">
            <view class="left">
              <view class="buyer_phone" style="margin: 10rpx 0;">
                <view bind:tap="makePhoneCall" data-phone="{{item.address.contact.mobile}}">电话: {{item.address.contact.mobile}}
                  <!-- <van-icon name="phone-o" /> -->
                  <image src="/static/point/phone.png" mode="widthFix" style="width: 30rpx;height: auto;" />
                </view>
                <view style="margin-left: 20rpx;">联系人: {{item.address.contact.name}}</view>
              </view>
              <view class="buyer_address" style="margin: 10rpx 0;">定位: {{item.address.location.address}}</view>
              <view class="buyer_address" style="margin: 10rpx 0;">地址: {{item.address.address}}</view>
              <view style="display: flex;gap: 40rpx;">
                <view class="ship_quantily">发货: <text class="num_color">{{item.sort_num}} 件</text></view>
                <view class="quality_control_num">重量: <text class="num_color">{{item.sort_weight_fmt}} kg</text></view>
              </view>
              <view style="font-size: 24rpx;margin: 10rpx 0;" wx:if="{{item.delivery_user_name}}">配送人: {{item.delivery_user_name}}</view>
            </view>
            <view class="right" style="display: flex;align-items: center;flex-direction: column;">
              <view data-info="{{item}}" bindtap="orderDetail" style="padding: 0 10rpx;flex: 1;display: flex;align-items: center;">
                <image src="{{imageUrl+'icon/right-slim.png'}}" mode="widthFix" style="height: auto;width: 40rpx;"></image>
              </view>
              <view style="height: 44rpx;" wx:if="{{statusActive=='doing'}}">
                <image src="{{imageUrl+'icon/ellipsis.png'}}" mode="widthFix" style="height: auto;width: 40rpx;" data-info="{{item}}" bind:tap="showOperate"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view wx:if="{{showDeliver}}" class="deliver-part">
      <view style="display: flex;justify-content: space-between;align-items: center;">
        <view class="left">
          <view style="font-size: 34rpx;font-weight: bold;margin-bottom: 30rpx;">
            配送指派
          </view>
          <view style="display: flex;">
            <view class="title">会员：</view>
            <view class="value">{{selectDeliverInfo.buyer_num}}</view>
          </view>
          <view style="display: flex;">
            <view class="title">商品：</view>
            <view class="value">{{selectDeliverInfo.product_num}}</view>
            <view class="value" style="margin-left: 10rpx;">({{selectDeliverInfo.weight_fmt}}kg)</view>
          </view>
        </view>
        <view class="delivery-man" bind:tap="queryDeliveryManList" wx:if="{{!is_delivery}}">选择配送员</view>
        <view wx:else style="font-size: 26rpx;color: grey;">
          <view >配送员</view>
          <view style="margin: 10rpx 0;">
            <text style="color: #000;margin-right: 10rpx;">{{delivery_info.user_name}}</text>
            <text>{{delivery_info.mobile}}</text>
          </view>
          <view style="display: flex;justify-content: space-between;">
            <text class="cancle" bind:tap="handleCancle" wx:if="{{is_sure}}">取消</text>
            <text class="confirm" wx:if="{{is_sure}}" bind:tap="handleSure">确定</text>
            <!-- <text class="confirm" wx:else>确定</text> -->
          </view>

        </view>
        <!-- <view class="right">
          <view class="right-qr" bind:tap="getDeliverQr">
            <image src="{{imageUrl+'icon/qr.png'}}" mode="heightFix" class="img-qr" bindtap=""></image>
            <view style="font-size: 28rpx;">
              配送码
            </view>
          </view>
        </view> -->
      </view>

    </view>

    <view wx:if="{{list.length>0}}" class="delivery_maps">
      <map id="map" longitude="{{currentLocation.longitude}}" latitude=" {{currentLocation.latitude}}" markers="{{markerss}}" scale="12" show-location style="width: 100%; height: 100%;z-index: 10;"></map>
    </view>

    <van-empty wx:else description="暂无内容" />

    <view style="width: 100%;height: 240rpx;">

    </view>
  </view>

</view>


<van-action-sheet show="{{ showOperate }}" actions="{{ actions }}" bind:cancel="handleCloseAction" bind:close="handleCloseAction" bind:select="hanldeSelect" cancel-text="取消" close-on-click-overlay />

<van-action-sheet show="{{ show_active }}" title="配送员" bind:close="handleClose">
  <view style="padding-bottom: 150rpx;">
    <view wx:for="{{delivery_actions_list}}" wx:key="id" style="padding: 20rpx 0;text-align: center;">
      <view bind:tap="hanldeSelectDelivery" data-info="{{item}}"> {{item.user_name}}</view>
    </view>
  </view>
</van-action-sheet>


<van-dialog use-slot title="" show="{{ showQr }}" bind:close="onCloseShowQr" bind:confirm="onCloseShowQr">
  <view style="text-align: center;padding: 20rpx;">扫码配送码，绑定配送信息</view>
  <view style="display: flex;align-items: center;justify-content: center;">
    <image src="data:image/png;base64,{{qrData}}" style="width: 400rpx;margin-bottom: 30rpx; height: auto;" mode="widthFix"></image>
  </view>
</van-dialog>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />

<tabBar active="{{2}}"></tabBar>