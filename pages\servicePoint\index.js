// pages/servicePoint/index.js

import {
  take_over_list,
  confirm_take_over
} from '../../apis/servicePoint/takeOver';

import {
  // delivery_list,
  to_do_allot_list,
  order_list,
  allot_list,
  allot_order_list,
  warn_receive,
  // confirm_receive
} from '../../apis/servicePoint/delivery';
import {
  service_point_detail,
  user_balance
} from '../../apis/servicePoint/center';
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import {
  userIDKey
} from '../../utils/dict';
const app = getApp()
const util = require('../../utils/util');
import dayjs from "../../libs/dayjs"

Page({

  /**
   * 页面的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    active: 2,
    selectTakeOverIds: [],
    servicePointDetail: "", //服务点详情
    // serviceLocation:"",//服务点位置
    userBalance: "",
    buyerids: [], //采购商id列表
    allSum: 0,
    allWeight: 0,
    allSelect: false, //全选

    locations: {
      longitude: "",
      latitude: "",
    }, //
    takeDelivery: {
      times: 0, //筛选时间
      newTimes: 0, //筛选时间
      calendarShow: false,
    },
    currentLocation: {},
    markers: [], //标记点列表
    markerss: [],
    deliveryActive: 0,
    delivery: {
      times: 0, //筛选时间
      newTimes: 0, //筛选时间
      calendarShow: false,
      deliveryMan: "全部>",
      action: [],
      actions: [],
      deliveryManPop: false,
      deliveryManPops: false,
    },
    nowStamp: 0,
    minDate: 0,
    maxDate: 0,
    refreshPart: {
      active: 0,
      at: 0,
    },
    self_get_num: 0,
    logistics_num: 0,
    deliver_num: 0,
  },
  //跳转钱包
  jumpWallet() {
    if (this.data.userBalance) {
      if (this.data.servicePointDetail.is_mobile_verify) {
        let servicePointId = this.data.servicePointDetail.id
        wx.navigateTo({
          url: '/pages/supplier/mine/wallet/index?from=servicePoint'+ '&servicePointId=' + servicePointId,
        })
      } else {
        Toast('请先去设置中认证手机号');
      }
    } else {
      Toast('非管理员账户');
    }

  },
  //跳转设置
  jumpSetup(e) {
    if (this.data.userBalance) {
      wx.navigateTo({
        url: '/pages/servicePoint/center/setup/index?id=' + this.data.servicePointDetail.id +  '&isbindphone='+ this.data.servicePointDetail.is_mobile_verify ,
      })
    } else {
      Toast('非管理员账户');
    }
  },

  //切换tab
  switchTab(event) {
    // event.detail 的值为当前选中项的索引
    this.setData({
      active: event.detail
    });

    if (this.data.active == 0) {} else if (this.data.active == 1) {} else if (this.data.active == 2) {
      // this.deliveryList()
      // this.toDoAllotList()
      // this.deliveryManList()
      this.toDoAllotList()
    } else if (this.data.active == 3) {

    }

    this.queryReceiveWarn()
  },

  // 导航
  mapNavigation(e) {
    let info = e.currentTarget.dataset.info
    wx.openLocation({
      latitude: info.address.location.latitude,
      longitude: info.address.location.longitude,
      name: info.buyer_name, //定位名称
      address: info.address.address, //具体地址
      scale: 15
    })
  },
  makePhoneCall(e) {
    console.log(e.currentTarget.dataset.phone)
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone //仅为示例，并非真实的电话号码
    })
  },
  // makePhoneCall(e) {
  //   // console.log(e.currentTarget.dataset.phone)
  //   wx.makePhoneCall({
  //     phoneNumber: this.data.servicePointDetail.pay_mobile //仅为示例，并非真实的电话号码
  //   })
  // },

  //自提日历
  openTakeDeliveryCalendar(e) {
    this.setData({
      'takeDelivery.calendarShow': true,
    })
  },
  takeDeliveryCalendarConfirm(e) {
    let t = e.detail.getTime()
    this.setData({
      'takeDelivery.calendarShow': false,
      'takeDelivery.times': t,
      nowStamp: t,
    });

  },
  takeDeliveryCalendarClose(e) {
    this.setData({
      'takeDelivery.calendarShow': false,
    });
  },


  // 配送
  deliveryClass(e) {
    // console.log(e)
    this.setData({
      deliveryActive: e.detail.index,
      deliveryList: []
    })

    this.deliveryLists()
  },

  //打开配送日历
  openDeliveryCalendar() {
    this.setData({
      'delivery.calendarShow': true,
    })
  },

  calendarConfirm(event) {
    let t = event.detail.getTime()
    this.setData({
      'delivery.calendarShow': false,
      'delivery.times': t,
      nowStamp: t,
      deliveryList: []
    });

    this.deliveryLists()
  },
  calendarClose() {
    this.setData({
      'delivery.calendarShow': false,
    });
  },
  //打开配送人员弹窗
  openDeliveryManPop() {
    this.setData({
      'delivery.deliveryManPops': true

    })
  },
  //配送弹窗关闭
  deliveryManPopCloses() {
    this.setData({
      'delivery.deliveryManPops': false
    })
  },
  //配送弹窗关闭
  deliveryManPopClose() {
    this.setData({
      'delivery.deliveryManPop': false
    })
  },
  // 配送弹窗选择
  deliveryManSelect(e) {
    console.log(e)
    let id = e.detail.id
    let that = this
    that.setData({
      'delivery.deliveryManPop': false,
      deliveryManId: e.detail.id,
      'delivery.deliveryMan': e.detail.name
    })
    let data = {
      service_point_id: that.data.servicePointDetail.id,
      buyer_id_list: that.data.buyerids,
      delivery_user_id: that.data.deliveryManId,
      timestamp: that.data.nowStamp,
    }
    Dialog.confirm({
        title: '提示',
        message: '确定分配',
      })
      .then(() => {
        allot_list(data).then(res => {
          if (res.data.code == 0) {
            that.setData({
              deliveryManId: "",
              'delivery.deliveryMan': "请选择"
            })
            Toast('分配成功');
            that.toDoAllotList()
          }
        })

      })
      .catch(() => {
        console.log(99999)
        // on cancel
      });

  },

  //配送弹窗选择
  deliveryManSelects(e) {
    this.setData({
      'delivery.deliveryManPops': false,
      deliveryManId: e.detail.id,
      'delivery.deliveryMan': e.detail.name
    })
    this.deliveryLists()
  },

  //
  deliveryLists() {
    if (this.data.deliveryActive == 0) {
      this.toDoAllotList()
      // this.allotOrderList(true)
    } else if (this.data.deliveryActive == 1) {
      this.allotOrderList(false)
      // this.allotOrderList(true)
    } else {
      this.allotOrderList(true)
    }
    this.setData({
      markerss: []
    })
  },
  // 选择分配
  selects(e) {
    console.log(e.currentTarget.dataset)
    let index = e.currentTarget.dataset.index
    let buyerid = e.currentTarget.dataset.info.buyer_id
    let num = e.currentTarget.dataset.info.sort_num
    let weight = e.currentTarget.dataset.info.sort_weight
    let location = e.currentTarget.dataset.info.address.location
    let content = {
      id: index,
      iconPath: "",
      latitude: location.latitude,
      longitude: location.longitude,
      width: 28,
      height: 32,
      callout: {
        content: e.currentTarget.dataset.info.buyer_name,
        fontSize: 16,
        display: "ALWAYS"
      },
    }

    this.data.deliveryList[index].select = !this.data.deliveryList[index].select
    this.setData({
      deliveryList: this.data.deliveryList,

    })
    // console.log(this.data.markers)
    if (this.data.deliveryList[index].select) {
      this.data.markers.push(content)
      this.data.buyerids.push(buyerid)
      this.setData({
        markers: this.data.markers
      })

      this.setData({
        allSum: this.data.allSum + num,
        allWeight: this.data.allWeight + weight,
      })
    } else {
      this.data.buyerids = this.data.buyerids.filter(item => {
        return item != buyerid;
      }); //反选移除已经选中的
      this.data.markers = this.data.markers.filter(items => {
        return items.id != index;
      })
      this.setData({
        markers: this.data.markers,
        allSum: this.data.allSum - num,
        allWeight: this.data.allWeight - weight,
      })
    }

    if (this.data.buyerids.length == this.data.deliveryList.length) {
      this.setData({
        allSelect: true
      })
    } else {
      this.setData({
        allSelect: false
      })
    }


    this.setData({
      buyerids: this.data.buyerids
    })

  },



  //订单详情
  orderDetail(e) {
    let info = e.currentTarget.dataset.info
    let buyerid = e.currentTarget.dataset.info.buyer_id
    let timestamp = this.data.nowStamp
    let deliver_type = info.deliver_type
    let servicepointid = this.data.servicePointDetail.id
    console.log()
    let actives = this.data.active
    wx.navigateTo({
      url: '/pages/servicePoint/delivery/deliveryDetail/index?buyerid=' + buyerid + '&timestamp=' + timestamp + '&servicepointid=' + servicepointid + '&active=' + this.data.deliveryActive + '&actives=' + actives + '&deliver_type=' + deliver_type,
    })
  },

  orderDetails(e) {
    // console.log(e.currentTarget.dataset.info.buyer_id)
    let info = e.currentTarget.dataset.info
    let buyerid = e.currentTarget.dataset.info.buyer_id
    let timestamp = this.data.nowStamp
    // let timestamp = 1686124896000
    let servicepointid = this.data.servicePointDetail.id
    let deliver_type = info.deliver_type
    let actives = this.data.active
    wx.navigateTo({
      url: '/pages/servicePoint/delivery/deliveryDetail/index?buyerid=' + buyerid + '&timestamp=' + timestamp + '&servicepointid=' + servicepointid + '&active=' + this.data.deliveryActive + '&actives=' + actives + '&deliver_type=' + deliver_type,
    })
  },

  isAllSelect() {
    this.data.deliveryList.map(item => {
      if (item.select == false) {
        this.setData({
          allSelect: false
        })
      }
    })
  },

  allSelect() {
    this.data.allSum = 0,
      this.data.allWeight = 0
    if (this.data.allSelect) {
      this.data.deliveryList.forEach(item => {
        item.select = false

      })
      this.setData({
        markers: [],
        buyerids: []
      })
      this.setData({
        allSum: 0,
        allWeight: 0,
      })
    } else {
      this.data.buyerids = []
      this.data.deliveryList.forEach((item, index) => {
        item.select = true
        let content = {
          id: index,
          iconPath: "",
          latitude: item.address.location.latitude,
          longitude: item.address.location.longitude,
          width: 28,
          height: 32,
          callout: {
            content: item.buyer_name,
            fontSize: 16,
            display: "ALWAYS"
          },
        }
        this.setData({
          allSum: this.data.allSum + item.sort_num,
          allWeight: this.data.allWeight + item.sort_weight,
        })
        this.data.buyerids.push(item.buyer_id)
        this.data.markers.push(content)


      })
      // console.log(this.data.markers)

    }
    this.setData({
      deliveryList: this.data.deliveryList,
      allSelect: !this.data.allSelect,
      markers: this.data.markers,
      buyerids: this.data.buyerids
    })
  },


  //计算总数
  calculateSum() {
    this.setData({
      allSum: allSum + 1
    })
  },




  //切换角色
  switchRole() {
    wx.redirectTo({
      url: '../index/index',
    })
  },






  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.servicePointDetail()
    let that = this

    // let now  = dayjs().valueOf()
    // let begin = dayjs().subtract(30, 'day').valueOf()

    let now = dayjs()
    let minDate = now.subtract(15, 'day').valueOf()
    let maxDate = now.valueOf()
    let nowStamp = now.valueOf()

    this.setData({
      // 'takeDelivery.times': nowStamp,
      // 'takeDelivery.newTimes': begin,
      // 'delivery.times': nowStamp,
      // 'delivery.newTimes': begin,
      nowStamp: nowStamp,
      minDate: minDate,
      maxDate: maxDate,
    })

    this.queryReceiveWarn()
  },

  //确认分配
  confirmReceive() {
    if (this.data.buyerids.length == 0) {
      Toast('请选择');
    } else {


      // this.openDeliveryManPop()
      this.setData({
        'delivery.deliveryManPop': true
      })
    }



  },

  //配送员列表
  // deliveryManList() {
  //   delivery_man_list({
  //     service_point_id: this.data.servicePointDetail.id
  //   }).then(res => {
  //     if (res.data.code == 0 && res.data.data) {
  //       let newList = []
  //       res.data.data.map(item => {
  //         let content = {
  //           name: item.user_name,
  //           service_point_id: item.service_point_id,
  //           id: item.user_id
  //         }
  //         newList.push(content)
  //       })


  //       let newLists = []
  //       res.data.data.map(item => {
  //         let content = {
  //           name: item.user_name,
  //           service_point_id: item.service_point_id,
  //           id: item.user_id
  //         }
  //         newLists.push(content)
  //       })
  //       let content = {
  //         name: '全部',
  //         service_point_id: this.data.servicePointDetail.id,
  //         id: ""
  //       }
  //       newLists.unshift(content)
  //       this.setData({
  //         'delivery.action': newList,
  //         'delivery.actions': newLists,
  //       })

  //       console.log(this.data.delivery)
  //     }
  //   })
  // },


  //配送订单列表
  toDoAllotList() {
    this.setData({
      deliveryList: []
    })
    let data = {
      service_point_id: this.data.servicePointDetail.id,
      timestamp: this.data.nowStamp,
      // timestamp: 1686124896000,
    }
    to_do_allot_list(data).then(res => {
      if (res.data.code == 0 && res.data.data) {
        res.data.data.forEach(item => {
          item['select'] = false
        });

        this.setData({
          deliveryList: res.data.data,
          allSum: 0,
          allWeight: 0,
          buyerids: []
        })
        this.isAllSelect()
      }
    })
  },

  allotOrderList(status) {
    let data = {
      service_point_id: this.data.servicePointDetail.id,
      timestamp: this.data.nowStamp,
      delivery_user_id: "",
      receive_has: status,
      // timestamp: 1686124896000,
    }
    allot_order_list(data).then(res => {
      if (res.data.code == 0 && res.data.data) {
        res.data.data.forEach(item => {
          item['select'] = false
        });


        res.data.data.map((items, index) => {
          let content = {
            id: index,
            iconPath: "",
            latitude: items.address.location.latitude,
            longitude: items.address.location.longitude,
            width: 28,
            height: 32,
            callout: {
              content: items.buyer_name,
              fontSize: 16,
              display: "ALWAYS"
            },
          }

          this.data.markerss.push(content)
        })

        // console.log(this.data.markerss)
        this.setData({
          deliveryList: res.data.data,
          markerss: this.data.markerss
        })
      }
    })
  },


  //服务点详情
  servicePointDetail() {
    let that = this
    service_point_detail(wx.getStorageSync(userIDKey)).then(res => {
      if (res.data.code == 0) {
        this.setData({
          servicePointDetail: res.data.data,
          currentLocation: {
            latitude: res.data.data.location.latitude,
            longitude: res.data.data.location.longitude
          }
        })

        if (this.data.active == 0) {}
        // if (res.data.data.is_mobile_verify) {
        // that.userBalance()
        // }\
        // if (res.data.data.user_id == wx.getStorageSync(userIDKey)) {
          that.userBalance()
        // }
      }
    })
  },

  //用户余额
  userBalance() {
    let data = {
      service_point_id: this.data.servicePointDetail.id,
    }
    user_balance(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          userBalance: res.data.data
        })
      }
    })
  },

  queryReceiveWarn() {
    this.requestWarnReceive(1)
    this.requestWarnReceive(3)
    this.requestWarnReceive(2)
  },

  requestWarnReceive(deliver_type) {
    let data = {
      timestamp: this.data.nowStamp,
      deliver_type: deliver_type,
    }
    warn_receive(data).then(res => {
      if (res.data.code == 0) {
        let num = res.data.data
        switch (deliver_type) {
          case 1:
            this.setData({
              deliver_num: num
            })
            break
          case 2:
            this.setData({
              self_get_num: num
            })
            break
          case 3:
            this.setData({
              logistics_num: num
            })
            break
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // this.deliveryLists()
    if (this.data.active == 3) {
      this.servicePointDetail()
    }

    this.refresh()
  },

  // 刷新列表
  refresh() {
    let now = dayjs().valueOf()
    let refreshPart = {
      active: this.data.active,
      at: now,
    }
    this.setData({
      refreshPart,
    })
  },

  calendarUpdate(e) {
    let t = e.detail
    this.setData({
      nowStamp: t,
    })
  }

})