<!--pages/servicePoint/index.wxml-->

<import src="./center/index" />

<!-- <template wx:if="{{active==0}}" data="{{takeOverList}}" is="1" /> -->

<!-- <template wx:if="{{active==1}}" data="{{takeDeliveryList,takeDelivery,navHeights,navBarHeight,menuHeight,nowStamp,minDate,maxDate}}" is="2" /> -->
<!-- <template wx:if="{{active==2}}" data="{{deliveryList,markers,markerss,serviceLocation,allSelect,delivery,deliveryActive,buyerids,allSum,allWeight,navHeights,navBarHeight,menuHeight,currentLocation,nowStampminDate,nowStamp,minDate,maxDate}}" is="3" /> -->
<template wx:if="{{active==3}}" data="{{servicePointDetail,imageUrl,userBalance}}" is="4" />
<view class="container">

  <!--  自提 -->
  <self-get refreshPart="{{refreshPart}}" minDate="{{minDate}}" maxDate="{{maxDate}}" nowStamp="{{nowStamp}}" 
  bind:calendarUpdate="calendarUpdate" wx:if="{{active==0}}"></self-get>
  <!--  物流 -->
  <logistics refreshPart="{{refreshPart}}" minDate="{{minDate}}" maxDate="{{maxDate}}" nowStamp="{{nowStamp}}" 
  bind:calendarUpdate="calendarUpdate"   wx:if="{{active==1}}" ></logistics>
  <!-- 配送 -->
  <deliver refreshPart="{{refreshPart}}"  minDate="{{minDate}}" maxDate="{{maxDate}}" nowStamp="{{nowStamp}}" 
  bind:calendarUpdate="calendarUpdate"   wx:if="{{active==2}}" ></deliver>

  <van-tabbar active="{{ active }}" bind:change="switchTab">
    <van-tabbar-item  info="{{self_get_num>0?self_get_num:''}}">
      <image slot="icon" src="{{imageUrl+ 'icon/point.png'}}" mode="widthFix" style="width: 40rpx; height: auto;" />
      <image slot="icon-active" src="{{imageUrl+ 'icon/point-active.png'}}"  mode="widthFix" style='width: 40rpx; height: auto;' />
      自提{{self_get_num}}
    </van-tabbar-item>
    <van-tabbar-item  info="{{logistics_num>0?logistics_num:''}}">
      <image slot="icon" src="{{imageUrl+ 'icon/Logistics.png'}}"  mode="widthFix" style="width: 40rpx; height: auto;" />
      <image slot="icon-active" src="{{imageUrl+ 'icon/Logistics-active.png'}}" mode="widthFix" style='width: 40rpx; height: auto;' />
      物流{{logistics_num}}
    </van-tabbar-item>
    <van-tabbar-item  info="{{deliver_num>0?deliver_num:''}}">
      <image slot="icon" src="{{imageUrl+ 'icon/deliver.png'}}" mode="widthFix" style="width: 40rpx; height: auto;" />
      <image slot="icon-active" src="{{imageUrl+ 'icon/deliver-active.png'}}"  mode="widthFix" style="width: 40rpx; height: auto;" />
      配送{{deliver_num}}
    </van-tabbar-item>
    <van-tabbar-item>
      <image slot="icon" src="{{imageUrl+ 'icon/mine.png'}}" mode="widthFix" style="width: 40rpx; height: auto;" />
      <image slot="icon-active" src="{{imageUrl+ 'icon/mine-active.png'}}" mode="widthFix" style="width: 40rpx; height: auto;" />
      我的
    </van-tabbar-item>
  </van-tabbar>
  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>