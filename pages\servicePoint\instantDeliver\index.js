import dayjs from "../../../libs/dayjs"

import {
  instant_list,
} from '../../../apis/servicePoint/takeDelivery';

import {
  categoryCoverProcess,
  servicePointIDKey
} from '../../../utils/dict';
import {
  delivery_man_list
} from '../../../apis/servicePoint/deliveryPerson'
import {
  deliver_assign_delete,
  deliver_assign_create
} from '../../../apis/servicePoint/delivery';
import Toast from '@vant/weapp/toast/toast';

import {
  dealTimeFormatMMDD,
} from '../../../utils/dict';

const app = getApp()

Page({
  data: {
    nowStampFmt: 0,
    show: false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    servicePointID: "",
    list: [], // 列表
    minDate: 0,
    maxDate: 0,
    now: 0,
    markerss: [],
    currentLocation: {
      latitude: 25.03171,
      longitude: 102.75293
    },
    showDeliver: false,
    selectDeliverInfo: {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    }, // 选中信息
    show_active: false,
    delivery_actions_list: [],
    delivery_man_id: '',
    is_sure: true,
    buyer_id_list: [],
    showOperate: false,
    actions: [{
      name: '删除指派',
      color: '#ee0a24',
      subname: '谨慎删除，不可恢复',
      value: "del"
    }, ],
  },

  onLoad(options) {
    let time = app.globalData.delivery_time //全局时间
    let point_id = wx.getStorageSync(servicePointIDKey)
    let minDate = dayjs().subtract(4, "month").startOf('day').valueOf()
    let maxDate = dayjs().endOf('day').valueOf()
    let from = options.from
    this.setData({
      now: time,
      servicePointID: point_id,
      nowStampFmt: dealTimeFormatMMDD(time),
      minDate: minDate,
      maxDate: maxDate,
    })
    this.querylist()

  },

  toDeliver() {
    this.setData({
      showDeliver: true,
    })
  },

  cancelDeliver() {
    let selectDeliverInfo = {
      buyer_num: 0,
      product_num: 0,
      weight: 0,
      weight_fmt: 0,
    }
    let list = this.data.list
    list.forEach((item, i) => {
      if (item.selected) {
        item.selected = !item.selected
      }
    });
    this.setData({
      list,
      showDeliver: false,
      buyer_id_list: [],
      selectDeliverInfo: selectDeliverInfo,
      delivery_info: {},
      is_delivery: false
    })
  },

  select(e) {
    let index = e.currentTarget.dataset.index
    let info = e.currentTarget.dataset.info
    let list = this.data.list
    let weight = 0
    let buyerSet = new Set()
    let buyer_id_list = []
    let product_num = 0
    if (info.deliver_assign) {
      Toast('该订单已指派')
      return
    }
    list.forEach((item, i) => {
      if (index == i) {
        item.selected = !item.selected
      }
      if (item.selected) {
        weight += item.sort_weight
        buyerSet.add(item.buyer_id)
        product_num += item.sort_num
        buyer_id_list.push(item.buyer_id)
      }
    });

    let weight_fmt = (weight / 1000).toFixed(0)
    let selectDeliverInfo = {
      buyer_num: buyerSet.size,
      product_num,
      weight,
      weight_fmt
    }
    this.setData({
      list: list,
      buyer_id_list,
      selectDeliverInfo: selectDeliverInfo,
    })
  },

  // 查询服务仓配送员信息
  queryDeliveryManList() {
    let data = {
      service_point_id: this.data.servicePointID
    }
    delivery_man_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if (!list) {
          list = []
        }
        this.setData({
          show_active: true,
          delivery_actions_list: list
        })
      }
    }).catch(err => {
      Toast(err.data.message)
    })
  },

  handleClose() {
    this.setData({
      show_active: false,
    })
  },

  hanldeSelectDelivery(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      show_active: false,
      delivery_info: info,
      is_delivery: true
    })
  },

  handleCancle() {
    this.setData({
      is_delivery: false
    })
  },

  handleSure() {
    if (this.data.buyer_id_list.length < 1) {
      Toast("请选择配送会员")
      return
    }

    let data = {
      delivery_man_id: this.data.delivery_info.id,
      service_point_id: this.data.servicePointID,
      buyer_id_list: this.data.buyer_id_list,
      timestamp: this.data.now,
      deliver_type: 4
    }

    deliver_assign_create(data).then(res => {
      if (res.data.code == 0) {
        Toast('指派成功')
        this.querylist()
        this.cancelDeliver()
        this.setData({
          is_sure: true,
          is_delivery: false
        })
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_sure: true
      })
    })

  },

  showOperate(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      info,
      showOperate: true,
    })
  },

  handleCloseAction(e) {
    this.setData({
      showOperate: false,
    })
  },

  hanldeSelect(e) {
    let v = e.detail.value
    if (v == "del") {
      let id = this.data.info.deliver_assign_id
      let data = {
        id,
      }

      deliver_assign_delete(data).then(res => {
        if (res.data.code === 0) {
          Toast("删除成功")
          this.querylist()
        }
      })
    }
  },

  copyInfo(e) {
    let info = e.currentTarget.dataset.info
    let v = info.address.contact.name + "，" + info.address.contact.mobile + "，" + info.address.location.address + " " + info.address.address
    wx.setClipboardData({
      data: v,
      success: function () {}
    })
  },

  // 电话拨打
  makePhoneCall(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone //仅为示例，并非真实的电话号码
    })
  },

  querylist() {
    //  查询列表
    let service_point_id = this.data.servicePointID
    let now = this.data.now
    if (now == 0) {
      return
    }
    let data = {
      service_point_id: service_point_id,
      timestamp: now,
    }
    instant_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        let markerss = []
        if (res.data.data) {
          list = res.data.data
          list.forEach((item, index) => {
            item.sort_weight_fmt = item.sort_weight / 1000

            let content = {
              id: index,
              iconPath: "",
              latitude: item.address.location.latitude,
              longitude: item.address.location.longitude,
              width: 28,
              height: 32,
              callout: {
                content: item.buyer_name,
                fontSize: 16,
                display: "ALWAYS"
              },
            }

            markerss.push(content)
          })
        }
        this.setData({
          list,
          markerss: markerss,
        })
      }
    })
  },

  onConfirm(e) {
    app.globalData.delivery_time = e.detail.getTime()
    const now = e.detail;
    let time_now = now.getTime()
    let time_now_fmt = this.dealTimeToDay(time_now)
    this.setData({
      show: false,
      now: time_now,
      nowStampFmt: time_now_fmt
    });
    //  查询列表
    this.querylist()
  },

  orderDetail(e) {
    let info = e.currentTarget.dataset.info
    let timestamp = this.data.now
    let addr = encodeURIComponent(JSON.stringify(info.address))
    let param = `?buyer_id=${info.buyer_id}&timestamp=${timestamp}&service_point_id=${this.data.servicePointID}&buyer_name=${info.buyer_name}&addr=${addr}&instant_deliver_name=${info.instant_deliver_name}`
    wx.navigateTo({
      url: '/pages/servicePoint/instantDeliver/detail/index' + param,
    })
  },



  dealMoney(fen) {
    return Math.round(fen) / 100
  },
  dealTimeToDay(at) {
    return dayjs(at).format('MM-DD')
  },
  onDisplay() {
    this.setData({
      show: true
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },


  onShow() {

  },



})