
page{
  background-color: #f6f6f6;
  min-height: 100vh;
}
.nav {
  /* position: fixed; */
  top: 1rpx;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding-left: 20rpx;
}
.capsule-box{
  padding: 0 20rpx 20rpx 0;
}

.calendar .van-popup{
  height: 500px !important;
  padding-bottom: 30rpx;
}
.van-calendar__body{
  padding-bottom: 150rpx !important;
}
.instant_deliver_name{
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}
.deliver-icon {
  color: #ffffff;
  background-color: #1989fa;
  border-radius: 12rpx;
  padding: 2rpx 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 20rpx;
  font-size: 26rpx;
}

.deliver-part {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  padding: 10rpx;
  z-index: 1;
}
.has-assign {
  border: 1rpx solid #07c160;
  white-space: nowrap;
  color: #07c160;
  font-size: 22rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
  white-space: nowrap;
}
.has-over{
  white-space: nowrap;
  color: #fff;
  font-size: 22rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
  white-space: nowrap;
  background-color: #07c160;
}
.not-assign {
  border: 1rpx solid red;
  white-space: nowrap;
  color: red;
  font-size: 22rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;

}
.deliver-part .title {
  font-size: 26rpx;
  padding: 4rpx 0;
}

.deliver-part .value {
  font-size: 26rpx;
  padding: 4rpx 0;
}
.deliver-part .right {
  margin-right: 30rpx;
  position: relative;
  box-sizing: border-box;
}
.delivery-man{
  background-color: orange;
  padding: 6rpx 10rpx;
  color: #fff;
  border-radius: 10rpx;
  font-size: 26rpx;
}

.cancle{
  background-color: #eee;
  padding: 6rpx 15rpx;
  border-radius: 10rpx;
}
.confirm{
  background-color: orange;
  padding: 6rpx 25rpx;
  color: #fff;
  border-radius: 10rpx;
}
.not-ok{
  background-color: red;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
}

.buyer_list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  padding: 10rpx;
}

.num_color{
  font-weight: bold;
}

.buyer_name {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.buyer_name .name{
  font-size: 30rpx;
  display: flex;
  align-items: center;
}
.copy{
  border: 1px solid #999898;
  border-radius: 10rpx;
  margin-left: 10rpx;
  font-size: 22rpx;
  padding: 0 10rpx;
  white-space: nowrap;
}
.buyer_phone {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.buyer_phone_title {
  width: 4em;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantily_color {
  color: red;
}

.buyer_address,
.ship_quantily,
.quality_control_num {
  font-size: 26rpx;
}

.order_detail{
  font-size: 26rpx;
  background-color: orange;
  padding: 4rpx 14rpx;
  box-sizing: border-box;
  border-radius: 44rpx;
  color: #ffffff;
}


.footer_wrap{
  background-color: #ffffff;
  margin-top: 40rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.amount_to{
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* padding: 20rpx;
  box-sizing: border-box; */
  font-size: 28rpx;
}

.not-all{
  border: 1rpx solid red;
  white-space: nowrap;
  color: red;
  font-size: 30rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.back-box {
  display: flex;
  justify-content: left;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.back {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  z-index: 99;
}

.self {
  position: absolute;
  top: 0;
  width: 100%;
  text-align: center;
}

.delivery_maps {
  background-color: #e5e5e5;
  width: 100%;
  height: 800rpx;
  margin-top: 20rpx;
}