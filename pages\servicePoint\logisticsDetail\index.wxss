page{
  background-color: #f6f6f6;
}
.buyer-name{
  display: flex;
  align-items: center;
}
.container{
  padding: 0 20rpx;
}
.copy-info-class{
  border: 1px solid #999898;
  border-radius: 10rpx;
  margin-left: 10rpx;
  font-size: 24rpx;
  padding: 0 10rpx;
  font-weight: normal;
}
/*  会员信息 */
.buyer{
  margin-top: 20rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
}

.product_title{
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.buyer .user{
  display:  flex;
}
.buyer .user .name{
  margin-right: 30rpx;
}

.product_list_wrap {
  margin-top: 20rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  padding:  10rpx 0;
}

.product_list {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-top: 30rpx;
}

.product_cover {
  width: 150rpx;
  height: 150rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
}


.product_content {
  width: calc(100% - 170rpx);
}

.sun_color {
  color: red;
}

.product_tag {
  font-size: 22rpx;
  margin-top: 10rpx;
  border: 1rpx solid #07c160;
  color: #07c160;
  text-align: center;
  border-radius: 10rpx;
  padding: 0 4rpx;
}

.order_param  {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.id-num{
  font-size: 24rpx;
}



.sorting_times {
  font-size: 24rpx;
}

.photograph_wrap {
  margin-top: 50rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.photograph {
  margin-top: 30rpx;
}

.submit {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin: 20rpx 80rpx;
  background-color: red;
  border-radius: 44rpx;
  color: #ffffff;
}

.delivery_info {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
}

.van-nav-bar__arrow {
  color: #000000 !important;
}

/*  配送单 */

/*  上传 */
.logistics-upload{
  margin-top: 30rpx;
  box-sizing: border-box;
  align-items: center;
  padding: 14rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
}

.deliver-note {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  box-sizing: border-box;
  align-items: center;
  padding: 14rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
}

.look {
  color: rgb(27, 65, 235);
  text-align: center;
  padding: 20rpx;
}

.note{
  /* border: 1px solid red; */
  margin: 20rpx;
  /* padding: 20rpx; */
  display: flex;
  /* height: 200rpx; */
  max-height: 150rpx;
  overflow: auto;
  border-radius: 20rpx;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
  background-color: #faf5f5;
  
}
/* .logisticsImg{
  width: 100rpx;
  height: auto;
  border: 1px solid red;
} */
.receipt_img_list {
  padding: 30rpx;
}

.check_weight_tip {
  border: 1rpx solid #bec7b4;
  /* display: inline-flex; */
  white-space: nowrap;
  color: #63933a;
  font-size: 20rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}