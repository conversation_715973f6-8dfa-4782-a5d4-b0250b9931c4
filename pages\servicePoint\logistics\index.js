import dayjs from "../../../libs/dayjs"


import {
  logistics_list,
} from '../../../apis/servicePoint/takeDelivery';

import {
  categoryCoverProcess,
  dealTimeFormatMMDD,
  servicePointIDKey
} from '../../../utils/dict';

const app = getApp()

Page({
  data: {
    nowStampFmt: '',
    show: false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    servicePointID: "",
    list: [], // 列表
    minDate: 0,
    maxDate: 0,
    now: 0
  },

  onLoad() {
    let time = app.globalData.delivery_time //全局时间
    let point_id = wx.getStorageSync(servicePointIDKey)
    let minDate = dayjs().subtract(1, "month").startOf('day').valueOf()
    let maxDate = dayjs().endOf('day').valueOf()
    this.setData({
      now: time,
      servicePointID: point_id,
      nowStampFmt: dealTimeFormatMMDD(time),
      minDate: minDate,
      maxDate: maxDate,
    })
    this.querylist()
  },

  // 电话拨打
  makePhoneCall(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone //仅为示例，并非真实的电话号码
    })
  },

  querylist() {
    //  查询列表
    let service_point_id = this.data.servicePointID
    let now = this.data.now
    if (now == 0) {
      return
    }
    let data = {
      service_point_id: service_point_id,
      timestamp: now,
    }
    logistics_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data) {
          list = res.data.data
          list.forEach(item => {
            item.sort_weight_fmt = item.sort_weight / 1000
          })
        }
        this.setData({
          list
        })
      }
    })
  },

  onConfirm(e) {
    const now = e.detail;
    let time_now = now.getTime()
    let time_now_fmt = this.dealTimeToDay(time_now)
    app.globalData.delivery_time = now.getTime()
    this.setData({
      show: false,
      now: time_now,
      nowStampFmt: time_now_fmt
    });
    //  查询列表
    this.querylist()
  },

  orderDetail(e) {
    let info = e.currentTarget.dataset.info
    let timestamp = this.data.now
    let addr = JSON.stringify(info.address)
    let param = `?buyer_id=${info.buyer_id}&timestamp=${timestamp}&service_point_id=${this.data.servicePointID}&buyer_name=${info.buyer_name}&addr=${addr}&instant_deliver_name=${info.instant_deliver_name}`
    wx.navigateTo({
      url: '/pages/servicePoint/logisticsDetail/index' + param,
    })
  },


  dealMoney(fen) {
    return Math.round(fen) / 100
  },
  dealTimeToDay(at) {
    return dayjs(at).format('MM-DD')
  },
  onDisplay() {
    this.setData({
      show: true
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },


  onShow() {

  },



})