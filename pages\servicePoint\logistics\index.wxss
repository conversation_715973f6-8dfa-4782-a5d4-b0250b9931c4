
page{
  background-color: #f6f6f6;
  min-height: 100vh;
}

.nav {
  position: fixed;
  top: -1rpx;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding:0 20rpx;
}
.capsule-box{
  padding: 0 20rpx 20rpx 0;
}

.calendar .van-popup{
  height: 500px !important;
}
.van-calendar__body{
  padding-bottom: 150rpx !important;
}
.buyer_list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 22rpx;
  padding: 10rpx;
}

.num_color{
  font-weight: bold;
}

.buyer_name {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.buyer_name .name{
  font-size: 32rpx;
  font-weight: bold;
}

.buyer_phone {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.buyer_phone_title {
  width: 4em;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantily_color {
  color: red;
}

.buyer_address,
.ship_quantily,
.quality_control_num {
  font-size: 26rpx;
}

.order_detail{
  font-size: 26rpx;
  background-color: orange;
  padding: 4rpx 14rpx;
  box-sizing: border-box;
  border-radius: 44rpx;
  color: #ffffff;
}


.footer_wrap{
  background-color: #ffffff;
  margin-top: 40rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.amount_to{
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
}

.not-all{
  border: 1rpx solid red;
  white-space: nowrap;
  color: red;
  font-size: 30rpx;
  padding: 1rpx 6rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
  box-sizing: border-box;
}

.back-box {
  display: flex;
  justify-content: left;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.self {
  position: absolute;
  top: 0;
  width: 100%;
  text-align: center;
}
.tagOne{
  color: #ffffff;
  background-color: #245e56;
  font-size: 22rpx;
  box-sizing: border-box;
  padding: 2rpx 10rpx;
}

.tagTwo{
  color: #ffffff;
  background-color: #ee0a24;
  font-size: 22rpx;
  box-sizing: border-box;
  padding: 2rpx 10rpx;
}