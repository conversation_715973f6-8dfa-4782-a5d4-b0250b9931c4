import dayjs from "../../../../libs/dayjs";
const app = getApp();
import {
  deliver_detail,
  deliver_detail_monthly,
} from "../../../../apis/servicePoint/center";
Page({
  data: {
    deliver_show: false, //配送费明细
    delivertDate: dayjs().valueOf(),
    minDate: dayjs().subtract(4, "month").startOf("month").valueOf(),
    maxDate: dayjs().valueOf(),
    currentDate: dayjs().valueOf(),
    deliver_text: dayjs().format("YYYY-MM"),
    deliver_info: null,
    deliver_list: [],
    show: false,
    now: 0,
    nowStampText: "",
    atMaxDate: 0,
    minMaxDate: 0,
  },

  onLoad(options) {
    this.time();
    this.getDeliverinfo();
    this.getDeliverDetailMonthly();
  },

  time() {
    this.setData({
      now: dayjs().valueOf(),
      nowStampText: dayjs().format("YYYY-MM-DD"),
      atMaxDate: dayjs().endOf("day").valueOf(),
      minMaxDate: dayjs().subtract(160, "day").startOf("day").valueOf(),
    });
  },

  handleDeliverTime() {
    this.setData({
      deliver_show: true,
    });
  },
  confirmProfit(e) {
    this.setData({
      delivertDate: e.detail,
      currentDate: e.detail,
      deliver_text: dayjs(e.detail).format("YYYY年M月"),
      deliver_show: false,
    });
    this.getDeliverinfo();
    this.getDeliverDetailMonthly();
  },
  // 查看配送明细
  getDeliverinfo() {
    let data = {
      month_timestamp: this.data.delivertDate,
    };
    deliver_detail(data)
      .then((res) => {
        if (res.data.code == 0) {
          let info = res.data.data;
          info.final_deliver_fee_fmt = (info.final_deliver_fee / 100).toFixed(
            2
          );
          info.subsidy_deliver_fee_fmt = (
            info.subsidy_deliver_fee / 100
          ).toFixed(2);
          info.total_deliver_fee_fmt = (info.total_deliver_fee / 100).toFixed(
            2
          );
          this.setData({
            deliver_info: info,
          });
        }
      })
      .catch((err) => {
        wx.showToast({
          title: err.message,
          icon: "error",
        });
      });
  },

  handleTime() {
    this.setData({
      show: true,
    });
  },

  getDeliverDetailMonthly() {
    let data = {
      day_timestamp: this.data.now,
    };
    deliver_detail_monthly(data).then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data;
        list.forEach((ele) => {
          ele.list.forEach((item) => {
            item.order_created_at_fmt = this.dealTime(item.order_created_at);
            item.deliver_type_text = this.getDeliverText(item.deliver_type);
            item.total_deliver_fee_fmt = (item.total_deliver_fee / 100).toFixed(
              2
            ); //总配送费
            item.final_deliver_fee_fmt = (item.final_deliver_fee / 100).toFixed(
              2
            ); //最终配送费
            item.subsidy_deliver_fee_fmt = (
              item.subsidy_deliver_fee / 100
            ).toFixed(2); // 补贴配送费
          });
        });

        console.log(list);

        this.setData({
          deliver_list: list,
        });
      }
    });
  },

  onConfirm(e) {
    const now = e.detail;
    let time_now = now.getTime();
    let time_now_fmt = this.dealTimeToDay(time_now);
    this.setData({
      show: false,
      now: time_now,
      nowStampText: time_now_fmt,
    });
    //  查询列表
    this.getDeliverDetailMonthly();
  },

  dealTimeToDay(at) {
    return dayjs(at).format("YYYY-MM-DD");
  },

  dealTime(at) {
    return dayjs(at).format("HH:mm");
  },

  getDeliverText(e) {
    let text = "";
    switch (e) {
      case 1:
        text = "送货到店";
        break;
      case 2:
        text = "自提";
        break;
      case 3:
        text = "第三方物流";
        break;
      case 4:
        text = "即时配送";
        break;
    }

    return text;
  },
});
