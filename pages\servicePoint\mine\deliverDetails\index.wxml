<view class="deliverBox">
  <view class="deliver" bind:tap="handleDeliverTime">
    <view>配送明细</view>
    <view>{{deliver_text}} ></view>
  </view>
  <view wx:if="{{deliver_info}}" class="info-deliver">
    <view class="item">
      <view>总配送费</view>
      <view>{{deliver_info.total_deliver_fee_fmt}}</view>
    </view>
    <view class="item">
      <view>配送费补贴</view>
      <view>{{deliver_info.subsidy_deliver_fee_fmt}}</view>
    </view>
    <view class="item">
      <view>最终配送费</view>
      <view>{{deliver_info.final_deliver_fee_fmt}}</view>
    </view>
  </view>
</view>
<!-- 将日期单独提取出来 -->
<view class="date-container">
  <view class="date">
    <view>日期</view>
    <view catch:tap="handleTime">{{nowStampText}}></view>
  </view>
</view>
<view class="list">
  <view>
    <view wx:for="{{deliver_list}}" wx:key="index" class="list-item">
      <view style="margin-bottom: 10rpx;">{{item.buyer_name}}</view>
      <view style="display: flex;flex-direction: column;gap: 20rpx;">
        <view wx:for="{{item.list}}" wx:for-item="ele" wx:key="id">
          <view style="font-size: 26rpx;color: #333;margin-bottom: 8rpx;display: flex;justify-content: space-between;gap:10rpx">
            <view>{{index+1}}.订单时间：{{ele.order_created_at_fmt}}</view>
            <view>
              {{ele.deliver_type_text}}
              <text wx:if="{{ele.deliver_type==4}}">({{ele.instant_deliver_name}})</text>
            </view>
          </view>
          <view style="font-size: 26rpx;color: #777676;margin-top: 10rpx;display: flex;justify-content: space-between;gap:10rpx">
            <view>配送费:{{ele.total_deliver_fee_fmt}}</view>
            <view>补贴:{{ele.subsidy_deliver_fee_fmt}}</view>
            <view>支付:{{ele.final_deliver_fee_fmt}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<view style="height: 100rpx;"></view>
<van-calendar class="calendar" show="{{ show }}" default-date="{{ now}}" max-date="{{atMaxDate}}" min-date="{{ minMaxDate }}" show-confirm="{{false}}" show-title="{{false}}" bind:close="onClose" bind:confirm="onConfirm" allow-same-day="true" />
<van-action-sheet show="{{ deliver_show }}" title="时间" bind:close="onCloseProfit">
  <view>
    <van-datetime-picker type="year-month" value="{{ delivertDate }}" min-date="{{ minDate }}" max-date="{{ maxDate }}" bind:confirm="confirmProfit" bind:cancel="cancelProfit" />
  </view>
</van-action-sheet>