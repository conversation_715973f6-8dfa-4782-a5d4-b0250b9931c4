.deliverBox {
  background-color: #fff;
  padding: 20rpx;
  border: 1px solid #dad9d9;
  margin: 20rpx;
  border-radius: 10rpx;
}

.deliver {
  display: flex;
  justify-content: space-between;
}

.info-deliver {
  display: flex;
  justify-content: space-between;
  padding-top: 30rpx;
  font-size: 26rpx;
}

.item {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
  color: #636262;
}

/* 日期容器样式 */
.date-container {
  padding: 10rpx 20rpx;
  margin: 0 20rpx;
}

.list {
  margin: 0 20rpx;
  border-radius: 10rpx;
}

.date {
  display: flex;
  justify-content: space-between;
}

.list-item {
  border: 1px solid #d3d2d2;
  margin-top: 10rpx;
  padding: 10rpx;
  border-radius: 10rpx;
}