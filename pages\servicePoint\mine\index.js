import {
  get_service_point_detail,
  merchant_balance,
  deliver_detail
} from '../../../apis/servicePoint/center';
import {
  userIDKey,
  servicePointIDKey,
} from '../../../utils/dict';
const app = getApp()
import dayjs from "../../../libs/dayjs"
import Toast from '@vant/weapp/toast/toast';
import regeneratorRuntime from 'regenerator-runtime'
Page({

  data: {
    servicePointDetail: {},
    imageUrl: app.globalData.imageUrl,
    userBalance: {
      integerPart: 0,
      decimalPart: 0
    },
    time: '5', // 当前选中的日期
    startDate: '2024-04-01', // 开始日期
    endDate: '2024-04-30', // 结束日期
    time_stamp: 0,
    balance: 0,
    balance_next: 0,
   
  },
  async onLoad(options) {
    this.merchantBalance()
    this.servicePointDetail()
  },


  // 账户查询中心仓
  merchantBalance() {
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey)
    }
    merchant_balance(data).then(res => {
      if (res.data.code == 0) {
        let accountInfoList = res.data.data.accountInfoList
        accountInfoList.forEach(ele => {
          if (ele.accountType == 'FUND_ACCOUNT') {
            this.setData({
              balance: ele.balance
            })
          }

          if (ele.accountType == 'SETTLE_ACCOUNT') {
            this.setData({
              balance_next: ele.balance
            })
          }
        })
      }

    }).catch(err => { })
  },

  dealTimeToDay(at) {
    return dayjs(at).format('YYYY-MM')
  },

  bindTimeChange: function (e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    this.setData({
      time: e.detail.value
    })
  },
  //服务点详情
  servicePointDetail() {
    let data = {
      user_id: wx.getStorageSync(userIDKey)
    }
    get_service_point_detail(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          servicePointDetail: res.data.data,
          currentLocation: {
            latitude: res.data.data.location.latitude,
            longitude: res.data.data.location.longitude
          }
        })
        if (this.data.active == 0) { }
      }
    })
  },

  // 配送员跳转
  jumpDeliveryDriver() {
    wx.navigateTo({
      url: '/pages/servicePoint/deliverPerson/index',
    })
  },

  //跳转设置
  jumpSetup(e) {
    if (this.data.userBalance) {
      wx.navigateTo({
        url: '/pages/servicePoint/center/setup/index?id=' + this.data.servicePointDetail.id + '&isbindphone=' + this.data.servicePointDetail.is_mobile_verify,
      })
    } else {
      Toast('非管理员账户');
    }
  },

  jumpDeliver(){
    wx.navigateTo({
      url: '/pages/servicePoint/mine/deliverDetails/index',
    })
  },

  //跳转钱包
  jumpWallet() {
    if (this.data.userBalance) {
      let servicePointId = this.data.servicePointDetail.id
      wx.navigateTo({
        url: '/pages/servicePoint/mine/wallet/index?from=servicePoint' + '&servicePointId=' + servicePointId,
      })
    } else {
      Toast('非管理员账户');
    }
  },




 

  onShow() { },

})