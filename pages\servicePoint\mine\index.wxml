<view class="container">
  <view class="base_bg">
    <image class="bg" src="{{imageUrl+'/icon/bg.jpg'}}" mode="widthFix" />
    <view class="base_info">
      <view class="store_cover">
        <image class="store_cover" src="{{servicePointDetail.shop_head_img.name?imageUrl + servicePointDetail.shop_head_img.name:''}}" mode="aspectFill" />
      </view>
      <view style="margin-left: 30rpx;margin-top: 10rpx;" class="">
        {{servicePointDetail.name}}
      </view>
    </view>

    <view class="amount_module_wrap">
      <view class="amount_module">
        <view class="amount_module_list" bindtap="jumpWallet">
          <view style="font-size: 30rpx;color: #6e6e6e;">余额(元)</view>
          <view style="display: flex;align-items: flex-end;margin-top: 20rpx;">
            <view style="font-size: 40rpx;font-weight: bold;">{{ balance}}</view>
          </view>
        </view>
        <view class="amount_module_list">
          <view style="font-size: 30rpx;color: #6e6e6e;">次日到账</view>
          <view style="display: flex;align-items: flex-end;margin-top: 20rpx;">
            <view style="font-size: 40rpx;font-weight: bold;">{{ balance_next}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="function">
    <view bind:tap="jumpDeliveryDriver" class="functionBox">
      <image src="/static/point/point.png" style="width: 40rpx;height: auto;" mode="widthFix" />
      <view>配送员</view>
    </view>
    <view bind:tap="jumpSetup" class="functionBox" style="border-left: 1rpx solid #eee;">
      <image src="/static/point/setup.png" style="width: 40rpx;height: auto;" mode="widthFix" />
      <view>设置</view>
    </view>

    <view bind:tap="jumpDeliver" class="functionBox" style="border-left: 1rpx solid #eee;">
      <image src="/static/point/order.png" style="width: 40rpx;height: auto;" mode="widthFix" />
      <view>配送明细</view>
    </view>
  </view>

  <view style="height: 200rpx;">
  </view>
</view>



<van-toast id="van-toast" />