Page {
  background-color: #eee;
}

.container {
  box-sizing: border-box;
}

.base_bg {
  position: relative;

}

.bg {
  width: 100%;
  position: absolute;
  top: 0;
  z-index: -1;
}

.base_info {
  display: flex;
  align-items: flex-start;
  padding: 0rpx 30rpx;
  box-sizing: border-box;
  margin-top: 90rpx;
  position: absolute;
  top: 0;
}

.store_cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.amount_module_wrap {
  box-sizing: border-box;
  position: relative;
  bottom: -320rpx;
}

.amount_module {
  width: 100%;
  height: 160rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  box-sizing: border-box;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  padding:0 40rpx;
  box-sizing: border-box;
}

.amount_module_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.function {
  display: flex;
  background-color: #fff;
  font-size: 30rpx;
  margin-top: 20rpx;
  border-bottom: 1px solid #eee;
  margin-top: 350rpx;
}

.functionBox {
  flex: 1;
  width: calc(100% / 3);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  justify-content: center;
  box-sizing: border-box;
  gap: 10rpx;
}

.func_icon {
  width: 50rpx;
  height: auto;
}

.calendar .van-popup {
  height: 500px !important;
  padding-bottom: 30rpx;
}
