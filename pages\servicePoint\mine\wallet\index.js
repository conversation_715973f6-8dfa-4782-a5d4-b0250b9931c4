// pages/supplier/center/wallet/index.js
import {
  merchant_balance
} from '../../../../apis/servicePoint/center';
import {
  withdraw_list
} from '../../../../apis/warehouse/center';
import {
  servicePointIDKey
} from '../../../../utils/dict';
const app = getApp()
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
Page({

  data: {
    from: '',
    service_point_id: ''
  },
  onLoad(options) {
    this.setData({
      from: options.from,
      service_point_id: options.servicePointId
    })
  },

   //跳转提现页面
   withdraw() {
    let from = this.data.from
    let servicePointId = this.data.service_point_id
    wx.navigateTo({
      url: '/pages/servicePoint/mine/wallet/withdraw/index?from=' + from + '&servicePointId=' + servicePointId,
    })
  },

  //中心仓
  userBalance() {
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
    }
    merchant_balance(data).then(res => {
      if (res.data.code == 0) {
        let accountInfoList = res.data.data.accountInfoList
        accountInfoList.forEach(ele => {
          if (ele.accountType == 'FUND_ACCOUNT') {
            this.setData({
              userBalance: ele.balance
            })
          }
        })
      }
    })
  },
  withdrawList() {
    let data = {
      service_point_id: this.data.service_point_id,
    }
    withdraw_list(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          withdrawList: res.data.data
        })
      }
    }).catch(err => {})
  },

  onReady() {

  },

  onShow() {
    if (this.data.from == 'servicePoint') {
      this.userBalance()
    }

    if (this.data.from != '') {
      this.withdrawList()
    }
  },

})