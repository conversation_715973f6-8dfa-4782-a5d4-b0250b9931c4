<!--pages/supplier/center/wallet/index.wxml-->
<wxs src="../../../../utils/tool.wxs" module="tool" />
<view class=" container">
  <view class="balance_wrap">
    <view class="balance">
      <view>我的余额</view>
      <view class="money">
        <text>￥</text>
        <text>{{userBalance}}</text>
      </view>
    </view>
    <view class="withdraw" bindtap="withdraw">提现</view>
  </view>


  <view class="detail_record">
    <view class="detail_record_title">
      <view>余额明细</view>
      <view></view>
    </view>
    <view>

      <view class="record_list_" wx:for="{{withdrawList}}" wx:key="key">
        <view class="record_list_left">
          <view>提现<van-tag color="#f2826a" wx:if="{{item.pay_status==3}}"  style="margin-left:10rpx" plain type="primary">失败</van-tag>
            <van-tag color="#1989fa"  wx:if="{{item.pay_status==99}}" style="margin-left:10rpx" plain type="primary">处理中</van-tag>
          </view>
          <view style="margin: 10rpx 0;">卡号：{{item.bank_card_no}}</view>
          <view style="font-size: 28rpx;">{{tool.filtersData(item.created_at)}}</view>
        </view>
        <view class="record_value_">{{tool.toFixedTwo(item.amount / 100)}}元</view>
      </view>

    </view>

  </view>
  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>