import {
  withdraw_by_point,
  merchant_balance,
  merchant_Withdrawal_point,
} from '../../../../../apis/servicePoint/center';

import {
  servicePointIDKey
} from '../../../../../utils/dict';
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';

import regeneratorRuntime from 'regenerator-runtime'

Page({
  data: {
    amount: 0,
    userBalance: "", //用户余额
    from: '',
    service_point_id: '',
    withdraw_info: {},
  },

  async onLoad(options) {
    this.setData({
      service_point_id: wx.getStorageSync('x_service_point_id')
    })
    await this.getWithdrawalInfo()
    this.userBalanceByPoint()
  },

  //中心仓余额
  userBalanceByPoint() {
    let data = {
      service_point_id: this.data.service_point_id
    }
    merchant_balance(data).then(res => {
      if (res.data.code == 0) {
        let accountInfoList = res.data.data.accountInfoList
        accountInfoList.forEach(ele => {
          if (ele.accountType == 'FUND_ACCOUNT') {
            this.setData({
              balance_amount_fmt: ele.balance
            })
          }
        })
      }
    })
  },

  // 查询提现卡
  getWithdrawalInfo() {
    return new Promise((resolve) => {
      let data = {
        service_point_id: wx.getStorageSync(servicePointIDKey),
      }
      merchant_Withdrawal_point(data).then(res => {
        if (res.data.code == 0) {
          let list = res.data.data.bankCardAccountList
          if (!list) {
            list = []
          }
          this.setData({
            withdraw_info: list
          })
        }
      }).catch(err => {}).finally(() => {
        resolve()
      })
    })
  },

  inputAmount(e) {
    this.setData({
      amount: e.detail.value,
    })
  },
  //提现申请
  withdrawApply() {
    if (parseInt(this.data.amount * 100) < 101) {
      Toast('提现不能小于1.01');
      return
    }
    if (parseInt(this.data.amount*100) > this.data.balance_amount_fmt *100 ) {
      Toast('提现不能超过可提现金额');
      return
    }
    let account_no = this.data.withdraw_info[0].accountNo
    let data = {
      amount: parseInt(this.data.amount * 100),
      account_no: account_no,
      service_point_id: this.data.service_point_id,
    }
    Dialog.confirm({
        title: '提示',
        message: '确认提现',
      })
      .then(() => {
        withdraw_by_point(data).then(res => {
          if (res.data.code == 0) {
            Toast('提现成功');
            setTimeout(function () {
              wx.navigateBack()
            }, 2000)
          }
        }).catch(err => {
          Toast(err.data.message);
        })
      })
      .catch(() => {})
  },


  onShow() { },
})