<!--pages/supplier/center/wallet/withdraw/index.wxml-->
<wxs src="../../../../../utils/tool.wxs" module="tool" />
<view>
  <view>
    <view style="padding: 0 30rpx;font-size: 28rpx;color: #4b4a4a;" wx:for="{{withdraw_info}}" wx:key="index">
      <view style="display: flex;align-items: center;">
        <view style="width:180rpx;">银行卡类型:</view>
        <view wx:if="{{item.bankCardType == 'DEBIT_CARD'}}">借记卡</view>
        <view wx:if="{{item.bankCardType == 'ENTERPRISE_ACCOUNT'}}">对公账户</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">开户名:</view>
        <view>{{item.accountName}}</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">银行账号:</view>
        <view>{{item.accountNo}}</view>
      </view>
    </view>

    <van-cell title="提现金额(可提现金额￥{{ balance_amount_fmt }})" border="{{false}}" />

    <view class="inputs">
      <view class="unit">￥</view>
      <input type="digit" placeholder="{{amount}}" maxlength="8" bindinput="inputAmount" />
    </view>
    <view class="withdraw">
      <view bindtap="withdrawApply" class="btn">提现</view>
    </view>
  </view>


  <view class="tips">
    提示：
    <view>a.每笔提现代扣支付机构1元手续费</view>
    <view>b.每日最多进行5笔提现</view>
    <view>c.提现时间：8:00至20:00</view>
    <view>d.提现前需签署提现协议</view>
  </view>


  <van-dialog id="van-dialog" />
  <van-dialog use-slot title="" show="{{ show_dialog }}" show-confirm-button="{{false}}" bind:close="onClose" closeOnClickOverlay>
    <view style="text-align: center;margin: 30rpx 0;font-size: 28rpx;">绑定银行卡信息</view>
    <view style="padding: 20rpx 6rpx;">
      <van-cell-group>
        <van-field value="{{ account_no }}" type="number" clearable label="卡号" placeholder="请输入卡号" hide-clear bind:input="inputNo" />
        <van-field value="{{ bank_code }}" clearable label="开户行" placeholder="请输入开户行" hide-clear bind:input="inputCode" />
      </van-cell-group>

      <view style="width: 100%;display: flex;margin-top: 50rpx; justify-content: space-around;">
        <view class='cancelbnt' bindtap='onClose'>取消</view>
        <view class='wishbnt' bindtap='handleSure' wx:if="{{!is_wish}}">确定</view>
        <view class='wishbnt' wx:if="{{is_wish}}">绑定中...</view>
      </view>
    </view>
  </van-dialog>

  <van-toast id="van-toast" style="z-index: 99999;position: fixed;" />
</view>