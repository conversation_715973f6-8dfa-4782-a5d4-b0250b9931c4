/* pages/supplier/center/wallet/withdraw/index.wxss */

.inputs{
  box-sizing: border-box;
  display: flex;
  align-items: flex-end;
  padding: 0 30rpx;
  height: 80rpx;
  box-sizing: border-box;
}
.inputs input{
  width: 100%;
  font-size: 80rpx;
  height: 80rpx;
  border-bottom: 3rpx solid #999999;
}
.withdraw{
  margin-top: 60rpx;
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  box-sizing: border-box;
}

.btn{
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 44rpx;
  color: #ffffff;
  background-color:#FC4840;
}
.tips{
  padding: 30rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  color: #999999;
}
.unit{
  font-size: 50rpx;
  color: #000000;
}

.elseShow{
  padding: 30rpx;
}


.cancelbnt{
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx 50rpx;
  color: #07c060;
  margin-right: 20rpx;
}
.wishbnt{
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 18rpx 100rpx;
  color: white;
}