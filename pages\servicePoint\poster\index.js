import dayjs from '../../../libs/dayjs'
import {
  dealTimess
} from '../../../utils/dict'
import {
  categoryCoverProcess,
} from '../../../utils/dict'
const app = getApp()
Page({

  data: {
    imgUrl: app.globalData.imageUrl,
    poster_list: [],
    categoryCoverP: categoryCoverProcess, //
    now_time: '',
    head_img: '',
    navHeights: app.globalData.navHeight , //导航栏总高度
    menuBottom: app.globalData.menuBottom,
    menuHeight: app.globalData.menuHeight,
    navBarHeight: app.globalData.navBarHeight,
  }, 
  toProductLibary() {
    let from = 'poster'
    let list =JSON.stringify(this.data.poster_list) 
    wx.navigateTo({
      url: '/pages/supplier/product/pool/index?from=' + from + '&list=' + list,
    })
  },

  onLoad(options) {
    if (Object.keys(options).length !== 0) {
      let list = JSON.parse(options.list)
      list.forEach(ele => {
        if (ele.title.length > 10) {
          ele.title = ele.title.substring(0, 10) + '...';
        }
        ele = this.dealProduct(ele)
      });

      let time = dayjs().valueOf()
      let a = dealTimess(time)

      this.setData({
        poster_list: list,
        now_time: a
      })
    }
  },

  dealProduct(info) {
    info.price_fmt = (info.start_price / 100).toFixed(0)
    if (info.sku_list) {
      let index = info.sku_list.findIndex(e => {
        return e.price === info.start_price
      })
      if (index >= 0) {
        info.price_per_fmt = ((info.start_price / info.sku_list[index].rough_weight) * 10).toFixed(2)
      }
    }
    return info
  },

  uploadImgs(e) {
    let uploadName = e.detail.file.url
    this.setData({
      head_img: uploadName
    })
  },



})