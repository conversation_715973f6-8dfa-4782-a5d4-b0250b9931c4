
<view class="product" bind:tap="toProductLibary">选择商品</view>
<van-uploader accept="image" bind:after-read="uploadImgs" wx:if="{{poster_list.length > 0}}">
  <view class="product">更新主题</view>
</van-uploader>

<view wx:if="{{poster_list.length > 0}}" style="padding: 10rpx 20rpx;background-color: #fff;">
  <view style="background-color: #fff;">
    <image src="/static/point/logo.png" mode="widthFix" lazy-load="{{true}}" style="width:150rpx;" />
    <view class="head">
      <view>昆明金马畅销水果行情表</view>
      <view style="font-size: 26rpx;color: #a6a6a6;">({{now_time}})</view>
    </view>

  </view>
  <view wx:if="{{head_img}}" style="background-color: #faebd0;">
    <image lazy-load="{{true}}" src="{{head_img}}" mode="widthFix" class="head-img" />
  </view>
  <view class="content">
    <view wx:for="{{poster_list}}" wx:key="id" class="list">
      <image lazy-load="{{true}}" src="{{item.cover_img.name?imgUrl+categoryCoverP+item.cover_img.name:''}}" class="goods_cover" mode="aspectFill" />
      <view class="title">{{item.title}}</view>
      <view class="text-style">
        <view>{{item.price_fmt}}/件</view>
        <view class="uni-style">{{item.price_per_fmt}}/kg</view>
      </view>
    </view>
  </view>

  <view class="bot">
    <view class="icon-img">
      <view class="img">
        <image lazy-load="{{true}}" src="{{imgUrl+'icon/mini_qr_430.jpg'}}" class="icon" mode="widthFix" />
      </view>
      <view class="content-text">
        <view>
          <view>长按图片前往小程序</view>
          <view>果蔬团服务</view>
        </view>
        <view class="tip">本行情仅供参考，最终以下单的链接为准！</view>
      </view>
    </view>

  </view>
</view>
<view class="btn" wx:if="{{poster_list.length > 0}}">截长屏保存</view>
