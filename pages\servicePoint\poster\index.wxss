.product {
  background-color: #1cd66c;
  display: inline-block;
  margin-left: 20rpx;
  padding: 10rpx 25rpx;
  font-size: 30rpx;
  color: #fff;
  border-radius: 10rpx;
}

.head {
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx 0;
  font-weight: bold;
  font-size: 40rpx;
  color: #eb691a;
}

.list {
  background-color: #fff;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  text-align: center;
  flex: 0 0 32%;
  margin-left: 8rpx;
  margin-top: 10rpx;
}

.goods_cover {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  position: relative;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: #faebd0;
  color: #747272;
  font-size: 38rpx;
  z-index: 99;
  padding: 10rpx;
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

.head-img {
  width: 100%;
}

.text-style {
  display: flex;
  margin-top: 16rpx;
  color: #000;
  font-size: 22rpx;
  align-items: flex-end;
  font-weight: bold;
}

.uni-style {
  font-size: 18rpx;
  color: #eb691a;
  margin-left: 10rpx;
  font-weight: bold;
}

.title {
  font-size: 24rpx;
  text-align: left;
}

.bot {
  background-color: #f1d7a5;
}

.icon-img {
  display: flex;
  height: 180rpx;
  align-items: center;
}

img {
  width: 160rpx;
  height: 160rpx;
  background-color: #fff;
}

.icon {
  width: 150rpx;
  height: 150rpx;
  margin-left: 20rpx;
}

.content-text {
  margin-left: 20rpx;
  color: #eb691a;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tip {
  color: #222;
  font-size: 20rpx;
  font-weight: bold;
  padding-bottom: 10rpx;
  box-sizing: border-box;
  text-align: right;
  margin-top: 20rpx;
}

.btn {
  text-align: center;
  color: #808081;
  padding-bottom: 80rpx;
  margin-top: 10rpx;
}
