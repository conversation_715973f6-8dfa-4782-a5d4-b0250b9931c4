import dayjs from '../../../libs/dayjs';
import {
  upload_sign,
} from '../../../utils/api';
import {
  dealTimeFormat3,
  servicePointIDKey,
} from '../../../utils/dict';
import {
  promte_create,
  promte_list,
  promte_delete
} from '../../../apis/promte'
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
const util = require('../../../utils/util');
const uploadFile = require('../../../utils/uploadFile');
const app = getApp()
Page({

  data: {
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    imageUrl: app.globalData.imageUrl,
    promote_new_info: {},
    is_dialog: false,
    is_yesterday: false,
    is_today: true,
    date_time: 0,
    titile_value: '',
    radio: 'img',
    display_file: [],
    carouselImgList: [],
    carouselVideoList: [],
    video_file: {
      name: ''
    },
    promte_new_list: [],
    is_submit: false,
  },

  onLoad(options) {
    let time = dayjs().startOf('day').valueOf()
    let promote_new_info = {}
    let is_dialog = false
    if (Object.keys(options).length !== 0) {
      promote_new_info = JSON.parse(options.info),
        is_dialog = true
    }
    this.setData({
      promote_new_info: promote_new_info,
      is_dialog,
      date_time: time
    })
    this.getPromteNewList()
  },

  back() {
    wx.redirectTo({
      url: '/pages/index/index',
    })
  },

  getPromteNewList() {
    let point_id = wx.getStorageSync(servicePointIDKey)
    let data = {
      service_point_id: point_id,
      timestamp: this.data.date_time
    }
    promte_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if (!list) {
          list = []
        }
        list.forEach(item => {
          item.created_at_fmt = dealTimeFormat3(item.created_at)
          item.link_product_price_fmt = item.link_product_price / 100
          item.is_play = false
        })
        this.setData({
          promte_new_list: list
        })
      }
    })
  },


  toProductLibary() {
    let from = 'morning'
    wx.navigateTo({
      url: '/pages/supplier/product/pool/index?from=' + from,
    })
  },


  changeYesterday() {
    let time = dayjs().subtract(1, 'day').startOf('day').valueOf()
    this.setData({
      is_yesterday: true,
      is_today: false,
      date_time: time
    })
    this.getPromteNewList()
  },

  changeToday() {
    let time = dayjs().startOf('day').valueOf()
    this.setData({
      is_today: true,
      is_yesterday: false,
      date_time: time
    })
    this.getPromteNewList()
  },
  onChange(e) {
    // 图片
    let carouselImgList = this.data.carouselImgList
    let display_file = this.data.display_file
    // 视频
    let video_file = this.data.video_file
    let carouselVideoList = this.data.carouselVideoList
    if (e.detail == 'video') {
      carouselImgList = []
      display_file = []
    }
    if (e.detail == 'img') {
      video_file = {
        name: ''
      }
      carouselVideoList = []
    }
    this.setData({
      radio: e.detail,
      carouselImgList,
      display_file,
      video_file,
      carouselVideoList,
    })
  },

  inputTitle(e) {
    this.setData({
      titile_value: e.detail.value
    })
  },

  //图片模块删除
  deletes(e) {
    let name = e.detail.name
    if (name == '图片') {
      let index = e.detail.index;
      this.data.display_file.splice(index, 1);
      this.data.carouselImgList.splice(index, 1);
      this.setData({
        carouselImgList: this.data.carouselImgList,
        formData: this.data.formData
      })
    }
  },

  uploadImgs(e) {
    let that = this
    let tempFilePaths = e.detail.file
    let fileType = 'image'
    let type = "promote"
    tempFilePaths.forEach(item => {
      upload_sign(type).then(res => {
        if (res.data.code == 0) {
          let uploadData = res.data.data;
          let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(item.url))
          uploadFile(uploadData.host, item.url, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
            if (data.statusCode == 200) {
              const {
                carouselImgList = []
              } = that.data;
              carouselImgList.push({
                url: that.data.imageUrl + newPath
              });
              let list = {
                type: fileType,
                origin_name: util.siding(item.url),
                name: newPath,
              }
              that.data.display_file.push(list)
              that.setData({
                carouselImgList: carouselImgList,
                cutImages: [],
                images: []
              });
            }
          })
        }
      })
    })

  },

  // 视频
  delete(e) {
    let index = e.detail.index;
    this.data.carouselVideoList.splice(index, 1);
    this.setData({
      video_file: {
        name: ''
      },
      carouselVideoList: this.data.carouselVideoList
    })
  },

  afterRead(e) {
    let that = this
    let tempFilePaths = e.detail.file.url
    let fileType = e.detail.file.type
    let type = "promote"
    upload_sign(type).then(res => {
      if (res.data.code == 0) {
        let uploadData = res.data.data;
        let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(tempFilePaths))
        uploadFile(uploadData.host, tempFilePaths, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
          if (data.statusCode == 200) {
            const {
              carouselVideoList = []
            } = that.data;
            carouselVideoList.push({
              url: that.data.imageUrl + newPath
            });
            let list = {
              type: fileType,
              origin_name: util.siding(tempFilePaths),
              name: newPath,
            }
            that.setData({
              carouselVideoList: carouselVideoList,
            });
            that.setData({
              video_file: list
            })
          }
        })
      }
    })
  },


  handleCancel() {
    this.setData({
      is_dialog: false
    })
  },
  submit() {
    let display_file = this.data.display_file
    let video_file = this.data.video_file
    let promote_new_info = this.data.promote_new_info
    let carouselVideoList = this.data.carouselVideoList
    let point_id = wx.getStorageSync(servicePointIDKey)
    if (this.data.titile_value == '') {
      Toast('请输入内容')
      return
    }

    if (this.data.radio == 'img') {
      if (display_file.length == 0) {
        Toast('请上传图片')
        return
      }
    }
    if (this.data.radio == "video") {
      if (carouselVideoList.length == 0) {
        Toast('请上传视频')
        return
      }
    }
    this.setData({
      is_submit: true
    })

    let data = {
      service_point_id: point_id,
      title: this.data.titile_value,
      file_type: this.data.radio,
      img_file_list: display_file,
      video: video_file,
      link_product_id: promote_new_info.id
    }

    promte_create(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          is_dialog: false,
          is_submit: false,
        })
        this.getPromteNewList()
      }

    }).catch(err => {
      this.setData({
        is_submit: false
      })
    })
  },
  handleDel(e) {
    Dialog.confirm({
        title: '删除',
        message: '确认删除？',
      })
      .then(() => {
        let data = {
          id: e.currentTarget.dataset.id
        }
        promte_delete(data).then(res => {
          if (res.data.code == 0) {
            this.getPromteNewList()
          }
        })
      })
      .catch(() => {

      });
  },

  toProductInfo(e) {
    let info = e.currentTarget.dataset.info
    let id = info.link_product_id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id + `&from=promote`,
    })
  },

  previewReceive(e) {
    // 交付回单
    let src = e.currentTarget.dataset.src
    let list = e.currentTarget.dataset.list
    let src_list = []
    list.forEach(ele=>{
      src_list.push(this.data.imageUrl + ele.name)
    })
    wx.previewImage({
      current: src, // 当前显示图片的http链接
      urls:src_list,
    })
  },
  onPlay(e) {
    let id = e.target.id.slice(1)
    let list = this.data.promte_new_list
    list.forEach(ele => {
      if (ele.id == id) {
        ele.is_play = true
      }
    })
    this.setData({
      promte_new_list: list
    })
  },
  // 暂停播放
  onTouchMove(e) {
    let list = this.data.promte_new_list
    list.forEach(ele => {
      if (ele.is_play == true) {
        // 获取视频上下文
        const videoContext = wx.createVideoContext('a' + ele.id, this);
        const query = this.createSelectorQuery()
        query.select('#a' + ele.id).boundingClientRect(rect => {
          if (rect) {
            if (rect.top < 40 || rect.bottom > 800) {
              videoContext.pause();
            }
          }
        }).exec();
      }
    })
  },

})