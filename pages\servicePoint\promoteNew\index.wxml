<view class="nav" style="height:{{navBarHeight}}px;z-index: 999;">
  <view class="capsule-box" style="position: relative;height: {{menuHeight}}px;top:{{navBarHeight-menuHeight-4}}px;">
    <image src="/static/point/left.png" mode="widthFix" style="width: 40rpx;height: auto;" bind:tap="back" />
    <view>今日推荐</view>
    <view style="width: 90rpx;"></view>
  </view>
</view>

<view style="position: relative;top: {{navBarHeight+10}}px;" bindtouchmove="onTouchMove">
  <view class="product" bind:tap="toProductLibary">去增加商品 +</view>
  <view class="day">
    <view class="today {{is_today?'back':''}}" bind:tap="changeToday">今日</view>
    <view class="yesterday {{is_yesterday?'back':''}}" bind:tap="changeYesterday">昨日</view>
  </view>
  <view style="margin-top: 40rpx;padding-bottom: 80rpx;">
    <view wx:for="{{promte_new_list}}" wx:key="id" class="list">
      <view class="line">
        <view class="round">{{promte_new_list.length-index}}</view>
        <view style="font-size: 28rpx;width: 100%;">
          <view class="times">
            <view>{{item.created_at_fmt}}</view>

          </view>

          <view style="display: flex;align-items: flex-start;gap: 50rpx;">
            <view>
              <view style="margin: 6rpx 0;margin-top: 10rpx;">{{item.title}}</view>
              <view class="image" wx:if="{{item.file_type == 'img'}}">
                <view wx:for="{{item.img_file_list}}" wx:for-item="ele" wx:key="index" style="margin-right: 10rpx;">
                  <van-image bindtap="previewReceive" data-src="{{imageUrl + ele.name}}" data-list="{{item.img_file_list}}" width="80" height="80" wx:if="{{ele.name!==''}}" src="{{imageUrl + ele.name}}" />
                </view>
              </view>
              <view wx:if="{{item.file_type == 'video'}}">
                <video enable-play-gesture="{{true}}" object-fit="contain" show-mute-btn="{{true}}" show-fullscreen-btn="{{true}}" show-center-play-btn="{{true}}" controls="{{true}}" style="width:400rpx;height: 300rpx;" src="{{imageUrl +item.video.name}}" poster="{{imageUrl +item.video.poster}}" bindplay="onPlay" id="{{'a'+item.id}}"></video>
              </view>
              <view class="link-info" bind:tap="toProductInfo" data-info="{{item}}">
                <view>
                  <image src="{{imageUrl + item.link_product_cover.name}}" mode="widthFix" class="product-image" />
                </view>
              
                <view class="link-product">
                  <view style="padding-right: 20rpx;">{{item.link_product_title}}</view>
                  <view class="bottom">
                    <view style="color: red;display: flex;align-items: flex-end;gap: 20rpx;">
                      <view style="font-weight: bold;font-size: 34rpx;" wx:if="{{item.link_product_price>0}}">￥{{item.link_product_price_fmt}}/件</view>
                      <view style="font-weight: bold;font-size: 34rpx;" wx:if="{{item.link_product_price==0}}">￥***/件</view>
                    </view>
                  </view>
                </view>



              </view>
            </view>

            <view bind:tap="handleDel" data-id="{{item.id}}" class="del">删除</view>

          </view>

        </view>
      </view>
    </view>

  </view>
</view>


<van-dialog use-slot title="商品" show="{{ is_dialog }}" showConfirmButton="{{false}}">
  <view class="content">
    <view class="products">
      <view>
        <image src="{{promote_new_info.cover_img?imageUrl + promote_new_info.cover_img.name:''}}" mode="widthFix" style="width: 150rpx; height: auto;" />
      </view>
      <view style="margin-left: 10rpx;font-size: 26rpx;">
        <view> {{promote_new_info.title}}</view>
        <view style="margin-top: 20rpx;display: flex;align-items: flex-end;gap: 20rpx;color: red;">
          <view> <text wx:if="{{promote_new_info.origin_price > 0}}">会员价:</text> ￥{{promote_new_info.price_fmt}}</view>
          <view style="font-size: 22rpx;" wx:if="{{promote_new_info.origin_price > 0}}">(市场价: ￥{{promote_new_info.origin_price_fmt}})</view>
        </view>
      </view>
    </view>
    <view style="margin: 30rpx 0;font-size: 30rpx;">
      <textarea class="desc-input" rows="3" cols="40" bindinput="inputTitle" placeholder="我对客户说点什么..." maxlength="100" value="{{ titile_value}}" style="width: 100%;height: 150rpx;"></textarea>
    </view>
    <view style="display: flex;align-items: center;font-size: 30rpx;gap: 20rpx;">
      <van-radio-group value="{{ radio }}" bind:change="onChange">
        <view style="display: flex;align-items: center;gap: 40rpx;">
          <van-radio name="img">图片</van-radio>
          <van-radio name="video">视频</van-radio>
          <van-radio name="no">无</van-radio>
        </view>
      </van-radio-group>
    </view>

    <view wx:if="{{radio == 'img'}}" class="img">
      <!-- <view style="white-space: nowrap;margin-bottom: 10rpx;">图片：</view> -->
      <view>
        <van-uploader bind:delete="deletes" file-list="{{ carouselImgList }}" multiple="{{true}}" max-count="6" name="图片" accept="image" bind:after-read="uploadImgs" />
      </view>
    </view>


    <view wx:if="{{radio == 'video'}}" class="img">
      <!-- <view style="white-space: nowrap;margin-bottom: 10rpx;">视频：</view> -->
      <view>
        <van-uploader bind:delete="delete" file-list="{{ carouselVideoList }}" max-count="1" name="轮播视频" accept="video" bind:after-read="afterRead" />
      </view>
    </view>

    <view style="display: flex;align-items: center;gap: 30rpx;margin-top: 50rpx;margin-bottom: 30rpx;justify-content: center;">
      <view class="cancel" bind:tap="handleCancel">取消</view>
      <view class="sure" bind:tap="submit" wx:if="{{!is_submit}}">提交</view>
      <view class="sure" wx:if="{{is_submit}}">提交中...</view>
    </view>
  </view>

  <van-toast id="van-toast" />
</van-dialog>

<van-dialog id="van-dialog" style="z-index: 99;" />