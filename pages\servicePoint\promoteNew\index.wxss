.nav {
  position: fixed;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.capsule-box {
  margin-left: 20rpx;
  width: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
}

.product {
  background-color: #1cd66c;
  display: inline-block;
  margin-left: 20rpx;
  padding: 10rpx 25rpx;
  font-size: 30rpx;
  color: #fff;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
}

.content {
  margin: 20rpx;
}

.day {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20rpx;
}

.yesterday {
  background-color: #c0c0c0;
  color: #fff;
  width: 140rpx;
  height: 54rpx;
  border-top-right-radius: 50rpx;
  border-bottom-right-radius: 50rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.today {
  background-color: #c0c0c0;
  color: #fff;
  border-top-left-radius: 50rpx;
  border-bottom-left-radius: 50rpx;
  font-size: 30rpx;
  width: 140rpx;
  height: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}


.back {
  background-color: #ff6a07;
  font-size: 34rpx;
  font-weight: bold;
}
.products {
  display: flex;
  align-items: flex-start;
  margin: 20rpx 0;
  background-color: #f8f7f7;
  border-radius: 20rpx;
  padding: 20rpx;
}

.desc-input {
  /* border: 1rpx solid #d6d4d4; */
  padding: 10rpx;
  color: #5f5f5f;
  box-sizing: border-box;
}

.img {
  margin-top: 30rpx;
  font-size: 30rpx;
}

.cancel {
  background-color: #c0c0c0;
  border-radius: 20rpx;
  padding: 16rpx 40rpx;
  color: #fff;
}

.sure {
  background-color: #fcc95b;
  border-radius: 20rpx;
  padding: 16rpx 80rpx;
  color: #fff;
}

.list {
  padding: 0 30rpx;
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.link-info {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  background-color: #f0efef;
  padding: 10rpx;
  border-radius: 10rpx;
  flex: 1;
}

.round {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #f8bd4e;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: absolute;
  top: -10rpx;
  left: -15rpx;
}

.line {
  padding: 20rpx 0rpx 80rpx 30rpx;
  width: 100%;
  position: relative;
  border-left: 2rpx dashed #c0c0c0;
}

.image {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.times {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: absolute;
  width: 95%;
  top: -16rpx;
}

.del {
  background-color: #ee5e5e;
  padding: 6rpx 20rpx;
  color: #fff;
  border-radius: 10rpx;
  white-space: nowrap;
  margin-top: 10rpx;
}
.product-image{
  width: 145rpx;
  height: auto;
  border-top-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}
.link-product {
  flex: 1;
  height: 140rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-right: 20rpx;
}