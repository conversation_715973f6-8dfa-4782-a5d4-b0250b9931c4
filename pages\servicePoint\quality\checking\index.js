import dayjs from '../../../../libs/dayjs';
import {
  upload_sign,
} from '../../../../utils/api';
import {
  warehouse_total_statistics_temp,
  warehouse_product_statistics_temp,
  quality_search,
  quality_list_temp,
  quality_updata_temp,
  order_warning_weight
} from '../../../../apis/warehouse/qualityControl';

import {
  dealTimeFormat2,
  servicePointIDKey,
  stationInfoKey
} from '../../../../utils/dict';
import Toast from '@vant/weapp/toast/toast';
import {
  dealTimes
} from '../../../../utils/check';
const uploadFile = require('../../../../utils/uploadFile');
const util = require('../../../../utils/util');
const app = getApp()
Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    times: 0,
    qualityActive: 0,
    warehouseProductStatistics: "", //统计列表
    warehouseTotalStatistics: "", //统计总数据
    active: 1, //
    nowTimes: 0,
    timesPop: false,
    statisticsTitleList: [
      "分类", "商品", "批次", "品控", "供应商"
    ],
    qualityParam: {}, // 品控请求
    showQualityReasonAction: false,
    amount_fmt: 0, //采购总金额
    amount: 0, //采购总金额

    qualityReasonRadio: '2', // 2 质量不合格 4 缺货  5 规格不符
    qualityReasonImg: {}, // 品控原因图片
    qualityReasonImgPreviewList: [], // 品控原因预览图片
    showQualityReasonImgWarn: false,
    from_quality_id: "", // 品控单id
    show_purchase_amount: false,
    inner_supplier_list: ["6481ef7527b29d1c3e17c056", "654388f8c9b3b227f6ab997a", "6456032ff99212289203a499", "64560330f99212289203a49d", "6456032df99212289203a48f", "6455fac1f99212289203a477", "647e9d95b1fd9a6008654e69"],
    weight_list: [],
    is_open: false,
    activeNames: '2',
    env: app.globalData.env
  },

  onLoad(options) {
    let now = dayjs()
    let newTimes = now.subtract(60, 'day').valueOf()
    let maxTimes = now.add(8, 'day').valueOf()
    let time = app.globalData.quality_time //全局时间

    this.setData({
      times: time,
      nowTimes: dealTimeFormat2(time),
      maxTimes: maxTimes,
      newTimes: newTimes,
    });
    this.switchTab()
    this.warningWeight()
  },
  onChange(event) {
    this.setData({
      activeNames: event.detail,
    });
  },

  warningWeight() {
    if (app.globalData.env == 3) {
      order_warning_weight().then(res => {
        if (res.data.code == 0) {
          let list = res.data.data
          if (!list) {
            list = []
          }
          list.forEach(ele => {
            ele.stock_up_day_time_fmt = dealTimes(ele.stock_up_day_time)
            ele.due_weight_fmt = ele.due_weight / 1000
            ele.sort_weight_fmt = ele.sort_weight / 1000
            ele.over_weight_fmt = ele.over_weight / 1000
          });
          this.setData({
            weight_list: list
          })
        }
      })
    }
  },

  //打开时间弹窗
  openTimePop() {
    console.log(333)
    this.setData({
      timesPop: true,
    })
  },

  closeCarendar(e) {
    this.setData({
      timesPop: false,
    })
  },
  // 搜索
  onCancelForQuality(e) {
    let status = Number(e.currentTarget.dataset.status)
    if (status == 1) {
      this.setData({
        searchQualityValue: "",
        from_quality_id: "",
        fromQualityTotal: false,
      })
      this.qualityList()
    } else {
      this.setData({
        searchQualityValue: ""
      })
      this.qualityListHas()
    }
  },

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    let from = 'quality'
    wx.navigateTo({
      url: `/pages/supplier/product/info/index?id=${id}&from=${from}`,
    })
  },

  viewOrderList(e) {
    let servicepointid = e.currentTarget.dataset.servicepointid
    let stockupid = e.currentTarget.dataset.stockupid
    wx.navigateTo({
      url: '/packageSort/sortingDetail/index?stockupid=' + stockupid + '&servicepointid=' + servicepointid + '&readOnly=' + 1,
    })
  },
  stockUpNum2(e) {
    let index = e.currentTarget.dataset.index //外层下标  1
    this.data.qualityList[index].quality_num = Number(e.detail)
  },

  stockUpNumHas(e) {
    let index = e.currentTarget.dataset.index //外层下标  1
    this.data.qualityListHas[index].quality_num = Number(e.detail)
  },

  //品控更新
  qualityUpdata(e) {
    let that = this
    let id = e.currentTarget.dataset.info.id
    let index = e.currentTarget.dataset.index //外层下标  1
    let reasonImg = e.currentTarget.dataset.info.reason_img
    let dueNum = parseInt(e.currentTarget.dataset.info.quality_due_num)
    let supplier_id = e.currentTarget.dataset.info.supplier_id
    let change_num = parseInt(e.currentTarget.dataset.info.quality_due_change_num)
    let num = parseInt(this.data.qualityList[index].quality_num)
    let amount = e.currentTarget.dataset.info.amount
    let amount_fmt = this.dealMoney(amount)
    let param = {
      id: id,
      quality_num: num,
      due_num: dueNum,
      change_num: change_num,
    }

    let f = true

    this.setData({
      qualityParam: param,
      showQualityReasonAction: true,
      show_purchase_amount: f,
      amount_fmt: amount_fmt,
      amount: amount,
    })

    if (dueNum != num) {
      let imgList = []
      if (reasonImg.name && reasonImg.name != '') {
        imgList.push({
          url: that.data.imageUrl + reasonImg.name,
          name: '图片2',
          isImage: true,
          deletable: true,
        });
      }

      this.setData({
        qualityReasonImgPreviewList: imgList,
        qualityReasonImg: reasonImg,
      })
    }
  },

  dealMoney(fen) {
    return Math.round(fen) / 100
  },

  //品控更新
  qualityUpdataHas(e) {
    let that = this
    let id = e.currentTarget.dataset.info.id
    let index = e.currentTarget.dataset.index //外层下标  1
    let reasonImg = e.currentTarget.dataset.info.reason_img
    let supplier_id = e.currentTarget.dataset.info.supplier_id
    let dueNum = parseInt(e.currentTarget.dataset.info.quality_due_num)
    let change_num = parseInt(e.currentTarget.dataset.info.quality_due_change_num)
    let num = parseInt(this.data.qualityListHas[index].quality_num)
    let amount = e.currentTarget.dataset.info.amount
    let reason_type = e.currentTarget.dataset.info.reason_type.toString()

    console.log(e.currentTarget.dataset.info)

    let f = true

    let amount_fmt = this.dealMoney(amount)

    let param = {
      id: id,
      quality_num: num,
      due_num: dueNum,
      change_num: change_num,
    }

    this.setData({
      show_purchase_amount: f,
      qualityParam: param,
      amount: amount,
      amount_fmt: amount_fmt,
      showQualityReasonAction: true,
      qualityReasonRadio: reason_type
    })

    if (dueNum != num) {
      let imgList = []
      if (reasonImg.name && reasonImg.name != '') {
        imgList.push({
          url: that.data.imageUrl + reasonImg.name,
          name: '图片2',
          isImage: true,
          deletable: true,
        });
      }

      this.setData({
        qualityReasonImgPreviewList: imgList,
        qualityReasonImg: reasonImg,
      })
    }
  },

  //切换品控
  switchQualityList(e) {
    this.setData({
      qualityActive: e.detail.name
    })
    if (e.detail.name == 0) {
      this.warehouseTotalStatistics()
      this.warehouseProductStatistics()
    } else if (e.detail.name == 1) {
      this.qualityList()
    } else if (e.detail.name == 2) {
      this.qualityListHas()
    } else {

    }
  },

  //集中仓总数据统计
  warehouseTotalStatistics() {
    let env = app.globalData.env
    let data = {
      timestamp: this.data.times,
    }
    if (env == 3) {
      data.service_point_id = wx.getStorageSync(servicePointIDKey)
    }
    if (env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      data.service_point_id = station_info.service_point_id
      data.station_id = station_info.id
    }
    warehouse_total_statistics_temp(data).then(res => {
      if (res.data.code == 0) {
        res.data.data.total_weight_fmt = (res.data.data.total_weight / 1000).toFixed(1)
        this.setData({
          warehouseTotalStatistics: res.data.data
        })
      }
    })
  },

  //集中仓数据列表统计
  warehouseProductStatistics() {
    let data = {
      timestamp: this.data.times,
    }
    if (app.globalData.env == 3) {
      data.service_point_id = wx.getStorageSync(servicePointIDKey)
    }
    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      data.service_point_id = station_info.service_point_id
      data.station_id = station_info.id
    }
    warehouse_product_statistics_temp(data).then(res => {
      if (res.data.code == 0) {
        let info =  res.data.data
        
        if(info){
          info.forEach(ele=>{
            if(ele.list){
              ele.list.forEach(item=>{
                item.supplier_name_fmt = item.supplier_name.slice(0,2)
              })
            }
          })
        }
        this.setData({
          warehouseProductStatistics: info
        })
      }
    })
  },

  switchTab() {
    this.warehouseTotalStatistics()
    this.warehouseProductStatistics()
  },

  //  品控数据列表
  qualityList() {
    if (this.data.fromQualityTotal) {
      let status = 1
      //  执行搜索并切换
      this.qualitySearch(status)
    } else {
      let data = {
        timestamp: this.data.times,
        quality_status: 'no'
      }
      if (app.globalData.env == 3) {
        data.service_point_id = wx.getStorageSync(servicePointIDKey)
      }
      if (app.globalData.env == 7) {
        let station_info = wx.getStorageSync(stationInfoKey)
        data.service_point_id = station_info.service_point_id
        data.station_id = station_info.id
      }

      quality_list_temp(data).then(res => {
        if (res.data.code == 0) {
          this.setData({
            qualityList: res.data.data
          })
        }
      })
    }

  },
  

  //  品控数据列表--已品控
  qualityListHas() {
    if (this.data.fromQualityTotal) {
      let status = 2
      //  执行搜索并切换
      this.qualitySearch(status)
    } else {
      let data = {
        timestamp: this.data.times,
        quality_status: 'yes'
      }
      if (app.globalData.env == 3) {
        data.service_point_id = wx.getStorageSync(servicePointIDKey)
      }
      if (app.globalData.env == 7) {
        let station_info = wx.getStorageSync(stationInfoKey)
        data.service_point_id = station_info.service_point_id
        data.station_id = station_info.id
      }

      quality_list_temp(data).then(res => {
        if (res.data.code == 0) {
          this.setData({
            qualityListHas: res.data.data
          })
        }
      })
    }
  },

  // 品控搜索
  onSearchForQuality(e) {
    let status = Number(e.currentTarget.dataset.status)
    let param = {
      timestamp: this.data.times,
      content: e.detail,
      quality_status: status
    }
    if (app.globalData.env == 3) {
      param.service_point_id = wx.getStorageSync(servicePointIDKey)
    }
    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      param.service_point_id = station_info.service_point_id
      param.station_id = station_info.id
    }
    quality_search(param).then(res => {
      if (status == 1) {
        this.setData({
          qualityList: res.data.data,
          searchQualityValue: e.detail
        })
      }
      if (status == 2) {
        this.setData({
          qualityListHas: res.data.data,
          searchQualityValue: e.detail
        })
      }
    }).catch(err => {
      Toast(err.data.message)
    })
  },
  qualitySearch(e) {
    let param = {
      timestamp: this.data.times,
      content: "",
      quality_id: this.data.from_quality_id,
      quality_status: e, // 0 全部  1 未品控 2 已品控
    }
    if (app.globalData.env == 3) {
      param.service_point_id = wx.getStorageSync(servicePointIDKey)
    }
    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      param.service_point_id = station_info.service_point_id
      param.station_id = station_info.id
    }

    const that = this
    quality_search(param).then(res => {
      that.setData({
        searchQualityValue: "",
        fromQualityTotal: false,
        from_quality_id: "",
      })
      if (e == 1) {
        that.setData({
          qualityList: res.data.data,
        })
      }
      if (e == 2) {
        that.setData({
          qualityListHas: res.data.data,
        })
      }

    }).catch(err => {
      Toast(err.data.message)
    })
  },


  // 确认时间
  confirmTime(e) {
    app.globalData.quality_time = e.detail.getTime()

    this.setData({
      timesPop: false,
      times: e.detail.getTime(),
      nowTimes: dealTimeFormat2(e.detail.getTime())
    })
    if (this.data.qualityActive == 0) {
      this.warehouseTotalStatistics()
      this.warehouseProductStatistics()
    } else if (this.data.qualityActive == 1) {
      this.qualityList()
    } else {
      this.qualityListHas()
    }
  },

  //  跳转品控列表并搜索
  toQualityList(e) {
    let info = e.currentTarget.dataset.info
    let quality_id = info.quality_id
    let quality_has = info.quality_has

    let qualityActive = 1
    if (quality_has) {
      qualityActive = 2
    }
    this.setData({
      fromQualityTotal: true,
      from_quality_id: quality_id,
      qualityActive: qualityActive,
    })
  },

  onChangeQualityReason(e) {
    let check = e.detail
    this.setData({
      qualityReasonRadio: check,
      showQualityReasonImgWarn: false,
    })
  },

  closeQualityReasonAction() {
    Toast("已取消品控")
    this.setData({
      showQualityReasonAction: false,
      showQualityReasonImgWarn: false,
      qualityReasonRadio: '2',
      qualityReasonImg: {},
      qualityReasonImgPreviewList: [],
    })
    this.switchTab()
    let qualityActive = this.data.qualityActive
    if (qualityActive == 1) {
      this.qualityList()
    }
    if (qualityActive == 2) {
      this.qualityListHas()
    }
  },

  submitQualityReason() {
    let img = this.data.qualityReasonImg
    let param = this.data.qualityParam
    param.amount = this.data.amount

    if (param.quality_num != param.due_num) {
      if ((this.data.qualityReasonRadio === '2' || this.data.qualityReasonRadio === '5') && (img.name == null || img.name === '')) {
        this.setData({
          showQualityReasonImgWarn: true,
        })
        console.log(111);
        return
      }
    }
    param.reason_img = img
    param.reason_type = parseInt(this.data.qualityReasonRadio)
    console.log(param, 767);
    quality_updata_temp(param).then(res => {
      if (res.data.code == 0) {
        Toast('保存品控成功');
        this.qualityList()
        this.qualityListHas()
      }
    }).catch(err => {
      Toast(err.data.message);
      // console.log(err.data.message)
    }).finally(() => {
      this.setData({
        showQualityReasonImgWarn: false,
        showQualityReasonAction: false,
      })
    })
  },

  //  上传品控原因图
  uploadQualityReason(e) {
    console.log(e)
    let that = this
    let tempFilePath = e.detail.file
    let fileType = 'image'
    let type = "product"
    let item = tempFilePath
    upload_sign(type).then(res => {
      if (res.data.code == 0) {
        let uploadData = res.data.data;
        let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(item.url))
        uploadFile(uploadData.host, item.url, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
          if (data.statusCode == 200) {
            const {
              imgList = []
            } = that.data;
            imgList.push({
              url: that.data.imageUrl + newPath,
              name: '图片2',
              isImage: true,
              deletable: true,
            });

            let img = {
              type: fileType,
              origin_name: util.siding(item.url),
              name: newPath,
            }
            that.setData({
              qualityReasonImg: img,
              qualityReasonImgPreviewList: imgList,
              showQualityReasonImgWarn: false,
            });
          }
        })
      }
    })
  },

  deletes(e) {
    this.setData({
      qualityReasonImg: {},
      qualityReasonImgPreviewList: [],
    });
  },

  // 输入采购总额
  inputContent(e) {
    let v = e.detail.value
    let amount_fmt = this.PointNum(v)
    let amount = parseInt(amount_fmt * 100)
    this.setData({
      amount: amount,
      amount_fmt: amount_fmt,
    })
  },

  blurBuyAmount(e) {
    let v = parseFloat(e.detail.value)
    let amount = parseInt(v * 100)
    let amount_fmt = this.dealMoney(amount)
    this.setData({
      amount: amount,
      amount_fmt: amount_fmt,
    })
  },

  PointNum: function (obj) {
    obj = obj.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
    obj = obj.replace(/^\./g, ""); //验证第一个字符是数字
    obj = obj.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
    obj = obj.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj = obj.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    let dotIndex = obj.indexOf(".")
    if (dotIndex != -1 && dotIndex == obj.length - 1) {
      return
    }

    if (obj.indexOf(".") < 0 && obj != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      obj = parseFloat(obj);
    }
    if (!obj || obj == '0' || obj == '0.0' || obj == '0.00') {
      return;
    }
    return parseFloat(obj);
  },


  onShow() {

  },


})