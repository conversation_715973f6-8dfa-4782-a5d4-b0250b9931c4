<view class="nav" style="position: fixed;width: 100%;z-index: 99999;top: 0;height: 50rpx;">
  <view class="capsule-box">
    <view bindtap="openTimePop">时间：{{nowTimes}}</view>
  </view>
</view>

<view class="container">
  <view>
    <view>
      <van-tabs active="{{ qualityActive }}" bind:change="switchQualityList" sticky z-index="999" offset-top="20">
        <van-tab title="品控总览">

          <van-collapse value="{{ activeNames }}" bind:change="onChange" class="van-collapse" accordion="{{true}}" wx:if="{{weight_list.length > 0 && qualityActive == 0 && env == 3}}">
            <van-collapse-item title="品控超重商品列表（超重部分客户可免单）" name="1">
              <view style="margin: 10rpx;" wx:if="{{weight_list.length > 0}}">
                <view class="weight">
                  <view style="flex: 1;">商品名称</view>
                  <view style="width: 110rpx;">数量</view>
                  <view style="width: 120rpx;">重量(kg)</view>
                  <view style="width: 220rpx;">备货</view>
                </view>
                <view>
                  <view wx:for="{{weight_list}}" wx:key="id" class="weight-list">
                    <view class="children" style="flex: 1;text-align: start;">{{item.product_title}}</view>
                    <view class="childrens" style="width: 110rpx;">
                      <view>订单:{{item.num}}</view>
                      <view>分拣:{{item.sort_num}}</view>
                    </view>
                    <view class="childrens" style="width: 120rpx;">
                      <view>订单:{{item.due_weight_fmt}}</view>
                      <view>分拣:{{item.sort_weight_fmt}}</view>
                      <view>超重:{{item.over_weight_fmt}}</view>
                    </view>
                    <view class="childrens" style="width: 220rpx;">
                      <view>商家：{{item.supplier_name}}</view>
                      <view>时间：{{item.stock_up_day_time_fmt}}</view>
                    </view>
                  </view>
                </view>
              </view>
            </van-collapse-item>
          </van-collapse>
          <view>
            <!-- 总表单 -->
            <view class="form_table_wrap">
              <view class="form_table">
                <view class="form_table_content">
                  <view class="form_table_content_">
                    <view>单品数</view>
                    <view>{{warehouseTotalStatistics.total_single}}</view>
                  </view>
                  <view class="form_table_content_">
                    <view>重量(kg)</view>
                    <view>{{warehouseTotalStatistics.total_weight_fmt}}</view>
                  </view>
                  <view class="form_table_content_">
                    <view>数量</view>
                    <view>{{warehouseTotalStatistics.total_product}}</view>
                  </view>
                  <view class="form_table_content_">
                    <view>采购商</view>
                    <view>{{warehouseTotalStatistics.total_buyer}}</view>
                  </view>
                  <view class="form_table_content_">
                    <view>商家</view>
                    <view>{{warehouseTotalStatistics.total_supplier}}</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 详细表单列表 -->
            <view class="detail_form_table_wrap">
              <view class="detail_form_table">
                <view class="detail_form_title_wrap">
                  <view style="width: 150rpx;border-left: 3rpx solid #e5e5e5;">分类</view>
                  <view style="width: 330rpx;">商品</view>
                  <view style="width: 62rpx;">批次</view>
                  <view style="width: 102rpx;display: flex;flex-direction: column;">
                    <view style="white-space: nowrap;">品控</view>
                    <view style="white-space: nowrap;">总-已</view>
                  </view>
                  <view style="width: 80rpx;">商家</view>
                </view>
                <view class="detail_form_table_content" wx:for="{{warehouseProductStatistics}}" wx:key="key">
                  <view class="from_product_class">{{item.category_name}}</view>
                  <view class="from_product_list_wrap">
                    <view wx:for="{{item.list}}" wx:key="key" class="from_product_list" wx:for-item="items" wx:for-index="childindex">
                      <view class="from_product_title" catchtap="jumpProductDetail" data-info="{{items}}">
                        <view>{{items.product_title}} <text style="color: #da571b;">[{{items.sku_name}}]</text> </view>
                      </view>
                      <view style="display: flex;flex-direction: column;">
                        <block wx:for="{{items.list}}" wx:key="key" wx:for-item="batchItem" wx:for-index="batchIndex">
                          <view style="display: flex;align-items: stretch;flex: 1;">
                            <view style="width: 60rpx; border-bottom: #e5e5e5 1rpx solid;text-align: center;display: flex;flex-direction: column;justify-content: center;">
                              <text wx:if="{{items.list.length>1}}"> P{{batchItem.stock_up_no}}</text>
                            </view>
                            <!--  品控数据 -->
                            <view class="from_product_quality_due_num" data-info="{{batchItem}}" bindtap="toQualityList">
                              <text>{{batchItem.quality_due_num}}</text>
                              <text>-</text>
                              <text wx:if="{{batchItem.quality_has}}">{{batchItem.quality_has_num}}</text>
                              <text wx:else style="color: red;">{{batchItem.quality_has_num}}</text>
                            </view>
                          </view>
                        </block>
                      </view>
                      <view class="from_supplier">{{items.supplier_name_fmt}}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </van-tab>
        <van-tab title="未品控列表">
          <van-search value="{{ searchQualityValue }}" data-status="{{1}}" placeholder="请输入商品标题" show-action bind:search="onSearchForQuality" bind:cancel="onCancelForQuality" />
          <view style="margin: 0 20rpx;" wx:for="{{qualityList}}" wx:key="key">
            <view style="margin-bottom: 16rpx;background-color: #fff;border-radius: 20rpx;padding:16rpx 10rpx;font-size: 28rpx;">
              <view catchtap="jumpProductDetail" style="display: flex;justify-content: space-between;" data-info="{{item}}">
                <text style="font-weight: bold;">
                  <text>{{item.product_title}}</text>
                  <text>-</text>
                  <text style="color: #da571b;">[{{item.sku_name}}]</text>
                </text>
                <text wx:if="{{item.stock_up_no >1}}" style="color: red;margin-left: 10rpx;white-space: nowrap;margin-right: 20rpx;">P{{item.stock_up_no}}</text>
              </view>
              <view style="font-size: 24rpx;color: #585656;display: flex;align-items: center;gap: 10rpx;margin-top: 10rpx;">
                <text>{{item.supplier_name}}</text>
                <text class="link" style="margin: 0 10rpx;" bind:tap="viewOrderList" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}">订单 ></text>
                <image src="{{imageUrl+'icon/note.png'}}" wx:if="{{item.note_list&&item.note_list.length>0}}" bind:tap="viewOrderList" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}" mode="widthFix" style="height: 40rpx;width: 40rpx;"></image>

                <image src="/static/point/note.png" mode="widthFix" style="height: auto;width: 35rpx;" bind:tap="viewOrderList" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}" wx:if="{{item.service_point_note_list&&item.service_point_note_list.length>0}}"></image>
                <text wx:if="{{item.purchase_note!=''}}" style="color: #ff5e00;font-size: 24rpx !important;">采购：{{item.purchase_note}}</text>
              </view>
              <view class="stock_up_list">
                <view style="display: flex;align-items: center;">
                  <view style="white-space: nowrap;margin-right: 30rpx;">应品控：[{{item.quality_due_num}}]</view>
                  <view style="display: flex;align-items: center;white-space: nowrap;">
                    <view>已品控：</view>
                    <van-stepper show-plus="{{false}}" show-minus="{{false}}" integer min="{{0}}" max="{{item.quality_due_num}}" input-width="60" value="{{item.quality_num}}" data-index="{{index}}" bind:change="stockUpNum2" />
                  </view>
                </view>


                <view class="stock_up_button not-has" data-info="{{item}}" data-index="{{index}}" bindtap="qualityUpdata">
                  品控
                </view>

              </view>
            </view>
          </view>
        </van-tab>
        <van-tab title="已品控列表">
          <van-search value="{{ searchQualityValue }}" data-status="{{2}}" placeholder="请输入商品标题" show-action bind:search="onSearchForQuality" bind:cancel="onCancelForQuality" />
          <view style="margin: 10rpx 20rpx 0rpx 20rpx;background-color: #fff;padding: 4rpx 10rpx;border-radius: 20rpx;font-size: 28rpx;" wx:for="{{qualityListHas}}" wx:key="key">
            <view style="margin-bottom: 30rpx;">
              <view catchtap="jumpProductDetail" style="display: flex;justify-content: space-between;" data-info="{{item}}">
                <text style="font-weight: bold;">
                  <text>{{item.product_title}}</text>
                  <text>-</text>
                  <text style="color: #da571b;">[{{item.sku_name}}]</text>
                </text>
                <text wx:if="{{item.stock_up_no>1}}" style="color: red;margin-left: 10rpx;white-space: nowrap;margin-right: 20rpx;">P{{item.stock_up_no}}</text>
              </view>
              <view style="font-size: 24rpx;color: #585656;display: flex;align-items: center;gap: 10rpx;margin-top: 10rpx;">
                <text>{{item.supplier_name}}</text>
                <text class="link" style="margin: 0 10rpx;" bind:tap="viewOrderList" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}">订单 ></text>
                <image src="{{imageUrl+'icon/note.png'}}" wx:if="{{item.note_list&&item.note_list.length>0}}" bind:tap="viewOrderList" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}" mode="widthFix" style="height: 40rpx;width: 40rpx;"></image>
                <image src="/static/point/note.png" mode="widthFix" style="height: auto;width: 40rpx;" bind:tap="viewOrderList" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}" wx:if="{{item.service_point_note_list&&item.service_point_note_list.length>0}}"></image>
                <text wx:if="{{item.purchase_note!=''}}" style="color: #ff5e00;font-size: 24rpx !important;">采购：{{item.purchase_note}}</text>
              </view>
              <view class="stock_up_list">
                <view style="display: flex;align-items: center;">
                  <view bind:tap="viewOrderList" style="margin-right: 30rpx;">应品控：{{item.quality_due_num}}</view>
                  <view style="display: flex;align-items: center;">
                    <view class="{{item.quality_due_num==item.quality_num?'':'danger'}}">已品控：</view>
                    <van-stepper show-plus="{{false}}" show-minus="{{false}}" integer min="{{0}}" max="{{item.quality_due_num}}" input-width="60" value="{{item.quality_num}}" data-index="{{index}}" data-indexs="{{childindex}}" bind:change="stockUpNumHas" />
                  </view>
                </view>
                <view class="stock_up_button has" data-info="{{item}}" data-index="{{index}}" data-indexs="{{childindex}}" bindtap="qualityUpdataHas">
                  更新品控
                </view>
              </view>
            </view>
          </view>
          <view>
          </view>
        </van-tab>
      </van-tabs>
    </view>


    <van-calendar class="calendar" bind:close="closeCarendar" first-day-of-week="1" show-title="{{false}}" show="{{ timesPop }}" show-confirm="{{ false }}" default-date="{{times}}" min-date="{{newTimes}}" max-date="{{maxTimes}}" bind:confirm="confirmTime" />

    <van-action-sheet show="{{ showQualityReasonAction }}" bind:close="closeQualityReasonAction" z-index="99999">
      <view style="text-align: center;padding: 20rpx;font-weight: bold;font-size: 40rpx;">
        商品品控
      </view>
      <view style="margin: 0 50rpx;">
        <view style="padding: 10rpx 0 ;">
          <!-- wx:if="{{qualityParam.due_num !==qualityParam.quality_num }}" -->
          应品控：{{qualityParam.due_num}} , 已品控：
          <text style="color: {{qualityParam.due_num == qualityParam.quality_num ?'#858585':'red'}};">{{qualityParam.quality_num}}</text>
        </view>
        <!-- TODO -->
        <view class="amount" wx:if="{{show_purchase_amount}}">
          <view style="font-size: 30rpx;">本次采购总成本：</view>
          <input class="inputBox" type="digit" placeholder="请输入入库成本总价" cursor-spacing="{{60}}" focus="{{false}}" placeholder-class="placeholder" value="{{amount_fmt}}" bindinput="inputContent" bindblur="blurBuyAmount" />
          <!-- <view style="color: red;font-size: 26rpx;" wx:if="{{qualityParam.change_num > 0}}">
            （新增商品，需更新成本）
          </view> -->
        </view>
        <view wx:if="{{show_purchase_amount}}" style="font-size: 28rpx;color: {{qualityParam.change_num > 0?'#858585':'red'}};margin-bottom: 40rpx;margin-top: 8rpx;">
          填写该商品采购成本总金额（非单件）
        </view>

        <van-divider wx:if="{{qualityParam.due_num !==qualityParam.quality_num  }}" contentPosition="left" customStyle="color: #ee0a24; border-color: #ee0a24; font-size: 28rpx;margin:0">
          缺货数：{{qualityParam.due_num - qualityParam.quality_num}}</van-divider>
        <van-radio-group value="{{ qualityReasonRadio }}" bind:change="onChangeQualityReason" wx:if="{{qualityParam.due_num !==qualityParam.quality_num }}">
          <view style="display: inline-flex;">
            <view>
              <van-radio name="2">质量不合格</van-radio>
            </view>
            <view style="margin-left: 30rpx;">
              <van-radio name="5">规格不符</van-radio>
            </view>
            <view style="margin-left: 30rpx;">
              <van-radio name="4">缺货</van-radio>
            </view>
          </view>
        </van-radio-group>
      </view>
      <view style="margin: 20rpx 50rpx;" wx:if="{{qualityParam.due_num !==qualityParam.quality_num }}">
        <van-uploader wx:if="{{qualityReasonRadio=='2'|| qualityReasonRadio=='5'}}" bind:delete="deletes" file-list="{{ qualityReasonImgPreviewList }}" max-count="1" name="qulityReason" accept="image" bind:after-read="uploadQualityReason" />
      </view>
      <view wx:if="{{showQualityReasonImgWarn && qualityParam.due_num !==qualityParam.quality_num}}" style="display: flex;align-items: center;justify-content: center;margin: 10rpx 0 ;">
        <view style="color: red;">请上传图片</view>
      </view>
      <view style="display: flex;align-items: center;justify-content: space-around;margin: 10rpx 20rpx;gap: 20rpx;">
        <view bind:tap="closeQualityReasonAction" class="cancel">取消</view>
        <view bind:tap="submitQualityReason" class="save">保存</view>
      </view>

      <view style="padding: 40rpx 0;">
      </view>
    </van-action-sheet>
  </view>
  <view style="width:100%;height: 300rpx;"></view>
</view>


<van-toast id="van-toast" />
<tabBar active="{{1}}"></tabBar>