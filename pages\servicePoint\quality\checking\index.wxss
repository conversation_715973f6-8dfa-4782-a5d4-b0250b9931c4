.container {
  margin-top: 40rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.stock_up_list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
}

.stock_up_button {
  text-align: center;
  background-color: #FC4840;
  border-radius: 66rpx;
  color: #ffffff;
  padding: 10rpx 30rpx;
  font-size: 26rpx;
}

.not-has {
  background-color: #12aaab;
}

.has {
  background-color: #12aaab;
}

.stock_up_buttons {
  width: 200rpx;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #999999;
  border-radius: 8rpx;
  color: #ffffff;
}

.form_table_wrap,
.detail_form_table_wrap {
  padding: 10rpx;
  box-sizing: border-box;
}

.form_table,
.detail_form_table {
  width: 100%;
  box-sizing: border-box;
}

.form_table_content {
  display: flex;
  align-items: center;
  border-left: 3rpx solid #e5e5e5;
}

.form_table_content_ {
  width: calc(100% / 4);
}

.form_table_content_>view {
  height: 100rpx;
  text-align: center;
  line-height: 100rpx;
  box-sizing: border-box;
  border-top: 3rpx solid #e5e5e5;
  border-bottom: 3rpx solid #e5e5e5;
  border-right: 3rpx solid #e5e5e5;
}

.form_table_content_ view:nth-child(1) {
  font-size: 28rpx;
}

.form_table_content_ view:nth-child(2) {
  font-size: 28rpx;
  margin-top: -3rpx;
}

.detail_form_title_wrap {
  display: flex;
  height: 100rpx;
}

.detail_form_title_wrap>view {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top: 3rpx solid #e5e5e5;
  border-bottom: 3rpx solid #e5e5e5;
  border-right: 3rpx solid #e5e5e5;
  font-size: 24rpx;
  /* font-weight: bold; */
}

.detail_form_table_content {
  display: flex;
  font-size: 24rpx;
}

.from_product_class {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 150rpx;
  box-sizing: border-box;
  border-bottom: 3rpx solid #e5e5e5;
  border-left: 3rpx solid #e5e5e5;
  border-right: 3rpx solid #e5e5e5;
}

.from_product_list_wrap {
  box-sizing: border-box;
}

.from_product_list {
  display: flex;
}

.from_product_list>view {
  border-bottom: 3rpx solid #e5e5e5;
  border-right: 3rpx solid #e5e5e5;
  height: auto;
  box-sizing: border-box;
}

.from_product_title {
  display: flex;
  align-items: center;
  width: 330rpx;
  padding: 15rpx 5rpx;
}

.from_product_quality_due_num {
  width: 100rpx;
  border-left: #e5e5e5 1rpx solid;
  border-bottom: #e5e5e5 1rpx solid;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.link {
  color: #1989fa;
  text-decoration: underline;
  white-space: nowrap;
}

.from_supplier {
  width: 80rpx;
}

.from_product_list .from_product_num,
.from_product_Prepared,
.from_supplier {
  display: flex;
  align-items: center;
  justify-content: center;
}

.danger {
  color: red;
}

.nav {
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding: 0 20rpx;
}

.calendar .van-popup {
  height: 500px !important;
}

.amount {
  display: flex;
  align-items: flex-end;
  margin-top: 40rpx;
}

.placeHolder {
  font-size: 24rpx;
  color: #808080;
}

.inputBox {
  border-bottom: 1rpx solid #b4b3b3;
  width: 150rpx;
}

/* 保存 */
.save {
  background-color: #12aaab;
  width: 400rpx;
  border-radius: 30rpx;
  text-align: center;
  padding: 20rpx 0;
  color: #fff;
  font-size: 36rpx;
  margin-top: 100rpx;
}

.cancel {
  background-color: #6b6b6b;
  width: 200rpx;
  border-radius: 30rpx;
  text-align: center;
  padding: 20rpx 0;
  color: #fff;
  font-size: 36rpx;
  margin-top: 100rpx;
}

.weight {
  display: flex;
  align-items: center;
  border: 1px solid #d4d4d4;
  border-right: none;
  background-color: #e0dfdf;
  font-weight: bold;
}

.weight view {
  border-right: 1rpx solid #d4d4d4;
  text-align: center;
  font-size: 24rpx;
  box-sizing: border-box;
}

.weight-list {
  display: flex;
  font-size: 24rpx;
  border: 1rpx solid #d4d4d4;
  border-top: none;
  border-right: none;
}

.children {
  border-right: 1rpx solid #d4d4d4;
  text-align: center;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
}

.childrens {
  border-right: 1rpx solid #d4d4d4;
  text-align: center;
  font-size: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
 .van-collapse .van-cell{
  background-color: #f8bf54 !important;
  padding: 10rpx !important;
  margin: 10rpx;
  box-sizing: border-box;
  width: 97% !important;
}
.van-cell__title, .van-cell__value{
  color: #fff;
}
.van-icon-arrow:before{
  color: #fff !important;
}
.van-collapse-item__content{
  padding: 0 !important;
}