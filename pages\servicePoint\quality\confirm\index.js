import {
  pre_stock_up,
} from '../../../../apis/warehouse/qualityControl';
import dayjs from '../../../../libs/dayjs';
import {
  pre_stock_up_list,
  stock_up
} from '../../../../apis/warehouse/stockUp';

import {
  confirm_stats,
  confirm_not,
  point_note_update,
  addr_detail
} from '../../../../apis/servicePoint/quality';

import {
  servicePointIDKey,
  stationInfoKey
} from '../../../../utils/dict.js';
const app = getApp()
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
import {
  dealTimeDay
} from '../../../../utils/check';
const util = require('../../../../utils/util');

Page({

  data: {
    notConfirmStatistics: {},
    now_timestamp: 0,
    preStockUpList: [],
    infoShow: false,
    orderNoteList: [],
    orderIdList: [], //订单id列表
    calendar: false, //日历
    imageUrl: app.globalData.imageUrl,
    newTimes: new Date().getTime() - 864000000, //筛选时间
    maxTimes: new Date().getTime() + 864000000,
    newDatas: new Date().getTime(),
    loading: true,
    phoneParam: app.globalData.phoneParam,
    show_note: false,
    service_point_note: '',
    is_point_note: true,
    order_status_edit: 0,
    service_fee: 0, // 地址服务费
    env: 0
  },

  onLoad(options) {
    let now = dayjs()
    this.setData({
      now_timestamp: now.valueOf(),
      env: app.globalData.env
    });
    this.preStockUp()
    this.preStockUpList()
  },
  // 服务仓备注
  handlePointNote(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      infoShow: false,
      show_note: true,
      is_point_note: true,
      service_point_note: info.service_point_note,
      order_status_edit: info.order_status == 3 ? false : true
    })
  },
  onCloseNote() {
    this.setData({
      show_note: false,
      service_point_note: ''
    })
  },
  onInputNote(e) {
    let v = e.detail.replace(/\s+/g, '')
    this.setData({
      service_point_note: v
    })
  },

  confirmNote() {
    this.setData({
      is_point_note: false
    })
    let data = {
      order_id: this.data.info.id, // 当且进度订单状态order_status=3时可以编辑
      service_point_note: this.data.service_point_note
    }

    point_note_update(data).then(res => {
      if (res.data.code == 0) {
        Toast('留言成功')
        this.setData({
          show_note: false,
          service_point_note: ''
        })
        this.preStockUpList()
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_point_note: true
      })
    })
    this.setData({
      show_note: false
    })

  },
  preStockUp() {
    //  待确认订单统计
    let data = {
      timestamp: this.data.now_timestamp,
    }

    if (app.globalData.env == 3) {
      data.service_point_id = wx.getStorageSync(servicePointIDKey)
    }

    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      data.service_point_id = station_info.service_point_id
      data.station_id = station_info.id
    }

    confirm_stats(data).then(res => {
      if (res.data.code == 0) {
        let d = res.data.data
        d.total_weight_fmt = (d.total_weight / 1000).toFixed(1)
        this.setData({
          notConfirmStatistics: res.data.data,
        })
      }
    })
  },

  //待备货列表
  preStockUpList() {
    let data = {}

    if (app.globalData.env == 3) {
      data.service_point_id = wx.getStorageSync(servicePointIDKey)
    }

    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      data.service_point_id = station_info.service_point_id
      data.station_id = station_info.id
    }


    confirm_not(data).then(res => {
      if (res.data.code == 0) {
        if (res.data.data == null) {
          this.setData({
            preStockUpList: [],
            loading: false
          })
          return
        }
        res.data.data.forEach(item => {
          item.deliver_fee_res.final_deliver_fee_fmt = (item.deliver_fee_res.final_deliver_fee / 100)
          item.created_at_fmt = dealTimeDay(item.created_at)
          item['select'] = false
        })
        this.setData({
          preStockUpList: res.data.data,
          loading: false
        })
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        loading: false
      })
    })
  },

  //备货
  stockUp(time) {
    let that = this
    this.setData({
      calendar: false,
    })
    Dialog.confirm({
        title: '提示',
        message: '确认备货到' + util.formatTimeTwo(time, 'Y-M-D'),
      })
      .then(() => {
        let data = {
          service_point_id: wx.getStorageSync(servicePointIDKey),
          order_id_list: that.data.orderIdList,
          target_timestamp: time
        }

        stock_up(data).then(res => {
          if (res.data.code == 0) {
            Toast('备货成功');
            this.preStockUpList()
            this.setData({
              calendar: false,
              orderIdList: [],
            })
          }
        }).catch(err => {
          Toast.fail(err.data.message)
        })

      })
      .catch(() => {
        this.setData({
          calendar: false,
        })
      })
  },

  //打开采购商信息弹窗
  buyerNameInfo(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      infoShow: true,
      info: info
    })
    this.addrDetail(info.address.address_id)
  },
  //关闭信息弹窗
  closeInfo() {
    this.setData({
      infoShow: false,
    })
  },

  // 地址信息
  addrDetail(id) {
    let data = {
      id: id
    }
    addr_detail(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          service_fee: res.data.data.service_fee
        })
      }
    })
  },

  makePhoneCall(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone //仅为示例，并非真实的电话号码
    })
  },

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    let from = 'confirm'
    wx.navigateTo({
      url: `/pages/supplier/product/info/index?id=${id}&from=${from}`,
    })
  },

  checked(e) {
    let index = e.currentTarget.dataset.index
    let info = e.currentTarget.dataset.info
    if (info.supplier_level == 'station' && this.data.env==3) {
      return
    }
    this.data.preStockUpList[index].select = !this.data.preStockUpList[index].select
    this.setData({
      preStockUpList: this.data.preStockUpList,
    })
  },

  onClose() {
    this.setData({
      calendar: false
    })
  },

  onConfirm(e) {
    this.stockUp(e.detail.getTime())
  },

  stockUps() {
    let list = this.data.preStockUpList
    let orderIdList = []
    list.forEach(ele => {
      if (ele.select) {
        orderIdList.push(ele.id)
      }
    })
    // 去重
    orderIdList = orderIdList.filter((item, index) => orderIdList.indexOf(item) === index)
    if (orderIdList.length == 0) {
      Toast('请选择');
      return
    }
    this.setData({
      calendar: true,
      newDatas: new Date().getTime(),
      orderIdList,
    })
  },

  onShow() {

  },

})