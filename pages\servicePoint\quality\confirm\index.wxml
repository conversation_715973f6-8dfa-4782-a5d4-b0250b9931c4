<view class="container">
  <view>
    <view class="container">
      <view class="order_list_wrap">
        <view class="stock_up_table_wrap">
          <view class="stock_up_table_title">
            <view>单品数</view>
            <view>采购商</view>
            <view>供应商</view>
            <view>总重量/kg</view>
            <view>商品数</view>
            <view>订单数</view>
          </view>
          <view class="stock_up_table stock_up_table_title" wx:key="key">
            <view class="stock_up_order_num">{{notConfirmStatistics.total_single}}</view>
            <view class="stock_up_goods_num">{{notConfirmStatistics.total_buyer}}</view>
            <view class="stock_up_goods_num">{{notConfirmStatistics.total_supplier}}</view>
            <view class="stock_up_goods_nums">{{notConfirmStatistics.total_weight_fmt}}</view>
            <view class="stock_up_goods_nums">{{notConfirmStatistics.total_product}}</view>
            <view class="stock_up_goods_nums">{{notConfirmStatistics.total_order}}</view>
          </view>
        </view>
        <view class="order_list" wx:for="{{preStockUpList}}" wx:key="key">
          <van-checkbox value="{{item.select}}" shape="square" data-index="{{index}}" data-info="{{item}}" disabled="{{item.supplier_level=='station' && env==3}}" label-disabled="{{true}}" bind:change="checked">
            <view class="order_list_content">
              <view style="width: 100%;font-weight: 600;font-size: 26rpx;display: flex;justify-content: space-between;" catchtap="buyerNameInfo" data-info="{{item}}">
                <view>
                  {{ item.buyer_name}}
                  <van-icon name="phone-o" />
                </view>
                <image src="{{imageUrl+'icon/note.png'}}" wx:if="{{item.order_note !== ''}}" mode="widthFix" style="height: auto;width: 40rpx;"></image>
              </view>

              <view class="point-note" wx:if="{{item.service_point_note !==''}}">
                <view style="margin-top: 4rpx;">
                  <image src="/static/point/note.png" mode="widthFix" style="height: auto;width: 35rpx;"></image>
                </view>
                <text style="margin-left: 6rpx;color: #666666;font-size: 24rpx;">服务仓：{{item.service_point_note}}</text>
              </view>
              <view style="color: #666666;font-size: 24rpx;">定位:{{item.address.location.address}}</view>
              <view style="color: #666666;font-size: 24rpx;display: flex;align-items: center;justify-content: space-between;">
                <text>供应商：{{item.supplier_name}}—{{item.created_at_fmt}}</text>
                <view style="display: flex;align-items: center;justify-content: space-between;">
                  <view style="font-size: 22rpx;text-align: right;">
                    <text style="margin-left: 40rpx;color: #409EFF;" wx:if="{{item.deliver_type==2}}">【自提】</text>
                    <text style="margin-left: 40rpx;color: #409EFF;" wx:if="{{item.deliver_type==3}}"> 【物流】</text>
                    <text style="margin-left: 40rpx;color: #409EFF;" wx:if="{{item.deliver_type==4}}"> 【即时配送】<text>{{item.instant_deliver_name}}</text></text>
                    <text class="logistics_name" wx:if="{{item.deliver_type==3}}">{{item.logistics_name}}</text>
                  </view>
                </view>
              </view>
              <view wx:for="{{item.product_list}}" class="per-product" wx:for-item="items" wx:for-index="indexs" wx:key="key" bindtap="jumpProductDetail" data-info="{{items}}">
                <text>{{indexs + 1}}. </text>
                <text>{{items.product_title}}</text>
                <text class="sku-name">[{{items.sku_name}}]</text>
                <text class="sku-num">x{{items.num}}</text>
              </view>
              <view wx:if="{{items.purchase_note}}" style="color: #fcc560;">采购备注：{{items.purchase_note}}</view>
            </view>
          </van-checkbox>
        </view>
      </view>

      <view class="stock_up" wx:if="{{preStockUpList !=''}}">
        <view bindtap="stockUps">
          加入发货
        </view>
      </view>
      <view style="width: 100%;height: 240rpx;"> </view>
    </view>

  </view>
</view>


<van-calendar class="calendar" show="{{ calendar }}" first-day-of-week="1" show-title="{{false}}" bind:close="onClose" default-date="{{newDatas}}" min-date="{{newTimes}}" max-date="{{maxTimes}}" show-confirm="{{false}}" bind:select="onConfirm" />
<van-action-sheet show="{{ infoShow }}" class="user-info" bind:close="closeInfo" z-index="999999" title="{{info.buyer_name}}">
  <view style="padding:30rpx;font-size: 26rpx;">
    <view style="color: #969696;">
      <view style="margin-bottom: 10rpx;">联系人：{{info.address.contact.name}}</view>
      <view bindtap="makePhoneCall" data-phone="{{info.address.contact.mobile}}" style="margin-bottom: 10rpx;">电话号码：{{info.address.contact.mobile}}
        <van-icon name="phone-o" />
      </view>
      <view style="margin-bottom: 10rpx;">定位地址：{{info.address.location.address}}</view>
      <view style="margin-bottom: 10rpx;display: flex;"><text style="white-space: nowrap;">详细地址：</text> <text>{{info.address.address}}</text></view>
    </view>

    <view style="display: flex;gap: 40rpx;margin-top: 20rpx;">
      <view wx:if="{{info.deliver_fee_res.final_deliver_fee_fmt > 0}}">配送费：{{info.deliver_fee_res.final_deliver_fee_fmt}}元</view>
    </view>

    <view wx:if="{{info.order_note !== ''}}" style="margin-top: 20rpx;">
      <view style="font-weight: bold;white-space: nowrap;font-size: 30rpx;">备注：</view>
      {{info.order_note}}
    </view>

    <view style="margin-top: 10rpx;">
      <view style="display: flex;">
        <text style="font-weight: bold;font-size: 30rpx;white-space: nowrap;">中心仓备注：</text>
        <view style="font-size: 26rpx;display: flex;align-items: center;">
          <text> {{info.service_point_note}}</text>
          <view style="width: 40rpx;margin-left: 10rpx;">
            <image src="/static/point/edit.png" mode="widthFix" style="height: 40rpx;width: 40rpx;white-space: nowrap;" data-info="{{info}}" bind:tap="handlePointNote"></image>
          </view>
        </view>

      </view>
    </view>
  </view>
</van-action-sheet>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" z-index="999" />

<van-overlay show="{{ loading }}" z-index="99999" custom-style="background-color:rgba(0, 0, 0, 0.8);">
  <view style="display: flex;justify-content: center;margin-top: calc({{phoneParam.safeArea.height  }}rpx + 100rpx);">
    <van-loading size="24px" color="#ffffff" vertical><text style="color: #ffffff;">加载中...</text></van-loading>
  </view>
</van-overlay>


<tabBar active="{{0}}" level="{{supplier_level}}"></tabBar>

<van-dialog use-slot title="" show="{{ show_note }}" show-confirm-button="{{false}}" bind:close="onCloseNote">
  <view class="modalDlg">
    <view>
      <view style="margin-bottom: 20rpx;text-align: center;font-weight: bold;">中心仓留言</view>
      <view>
        <van-field custom-class="input1" value="{{ service_point_note }}" disabled="{{order_status_edit}}" bind:change="onInputNote" type="textarea" maxlength="50" show-word-limit autosize />
      </view>
    </view>

    <view style="width: 100%;display: inline-flex;margin-top: 20rpx;">
      <view style="width:100rpx" class='cancelbnt' bindtap='onCloseNote'>取消</view>
      <view style="width:60%" class='wishbnt' bindtap='confirmNote' wx:if="{{is_point_note}}">确定</view>
      <view style="width:60%" class='wishbnt' wx:else>确定...</view>

    </view>
  </view>
</van-dialog>