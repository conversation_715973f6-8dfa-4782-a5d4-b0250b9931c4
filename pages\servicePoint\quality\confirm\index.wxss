.nav {
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.order_list {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  border-bottom: 2rpx solid #e5e5e5;
}

.logistics_name {
  color: #fff;
  background-color: #245e56;
  font-size: 20rpx;
  padding: 0 6rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.order_list_content {
  font-size: 28rpx;
  color: #000000;
  width: calc(100vw - 125rpx);
}

.per-product{
}

.sku-name{
  color: #da571b;
  padding: 0 8rpx;
}

.sku-num{
  color: #da571b;
}

.stock_up {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  box-sizing: border-box;
}


.stock_up view {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 8rpx;
  color: #ffffff;
  background-color: #FC4840;
}


.calendar .van-popup {
  height: 400px !important;
}


.stock_up_table_wrap {
  width: 100%;
  overflow-x: scroll;
}

.stock_up_table_title {
  width: 120%;
  display: flex;
  align-items: center;
  font-size: 28rpx;
}


.stock_up_table_title view:nth-child(1) {
  width: 20%;
}

.stock_up_table_title view:nth-child(2) {
  width: 20%;
}

.stock_up_table_title view:nth-child(3) {
  width: 20%;
}

.stock_up_table_title view:nth-child(4) {
  width: 20%;
}

.stock_up_table_title view:nth-child(5) {
  width: 20%;
}

.stock_up_table_title view:nth-child(6) {
  width: 20%;
}

.stock_up_table_title view {
  border: 2rpx solid #e5e5e5;
  height: 100rpx;
  border: 2rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}


.stock_up_select {
  display: flex;
  align-items: center;
  justify-content: center;
}

.select_icon {
  width: 40rpx;
}

.point-note {
  margin-right: 10rpx;
  font-weight: normal;
  font-size: 22rpx;
  display: flex;
}

.input1 {
  width: 100%;
  background-color: red;
  border: 3rpx solid #faca82;
  padding-top: 6rpx;
}

.van-cell {
  padding: 10rpx 16rpx !important;
}

.modalDlg {
  margin: 0 auto;
  padding: 55rpx;
  padding-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
}

.cancelbnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: #07c060;
  margin-right: 20rpx;
}

.wishbnt {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
}

.level {
  border: 1rpx solid #07b7f3;
  color: #07b7f3;
  font-size: 20rpx;
  padding: 0 6rpx;
  border-radius: 6rpx;
  margin-left: 6rpx;
}