import {
  dealTimeFormat2,
  servicePointIDKey,
  stationInfoKey
} from '../../../../utils/dict';
import dayjs from '../../../../libs/dayjs';
import {
  delivery,
  delivery_order_list,
  delivery_temp
} from '../../../../apis/warehouse/delivery';
import {
  warning_ship,
} from '../../../../apis/warehouse/qualityControl';
import {
  addr_detail
} from '../../../../apis/servicePoint/quality';
import {
  dealFenToYuan,
  dealTimeFormat1,
  dealTimeFormatMMDD,
  userIDKey
} from '../../../../utils/dict';
const uploadFile = require('../../../../utils/uploadFile');
const util = require('../../../../utils/util');


const app = getApp()
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';

Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    times: 0,
    nowTimes: 0,
    maxTimes: 0,
    newTimes: 0,
    shipActive: 0, // 发货页
    timesPop: false,
    delivery: {
      activeNames: [1],
      servicePonintList: [], //服务点列表
      servicePonint: "", //服务点标题
      servicePonintId: "", //服务点id
      timesPop: false, //时间弹窗
      servicePop: false, //服务点弹窗
      selectOrderIds: [], //选中的id列表

    },
    warningShipList: [],
    orderNoteList: [],
    orderPointNoteList: [],
    info: {},
    infoShow: false,
    service_fee: 0,
    env: 0
  },

  onLoad(options) {
    let now = dayjs()
    let newTimes = now.subtract(30, 'day').valueOf()
    let maxTimes = now.add(8, 'day').valueOf()
    let time = app.globalData.quality_time
    this.setData({
      times: time,
      nowTimes: dealTimeFormat2(time),
      maxTimes: maxTimes,
      newTimes: newTimes,
      env: app.globalData.env
    });
    this.deliveryOrderList()
  },

  copyInfo() {
    let info = this.data.info
    let v = info.address.contact.name + "，" + info.address.contact.mobile + "，" + info.address.location.address + " " + info.address.address
    wx.setClipboardData({
      data: v,
      success: function () {

      }
    })
  },

  // 发货数据列表
  deliveryOrderList() {
    let status = this.data.shipActive + 1
    let data = {
      timestamp: app.globalData.quality_time,
      status: status,
    }
    if (app.globalData.env == 3) {
      data.service_point_id = wx.getStorageSync(servicePointIDKey)
    }

    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      data.service_point_id = station_info.service_point_id
      data.station_id = station_info.id
    }


    delivery_order_list(data).then(res => {
      if (res.data.code == 0 && res.data.data != null) {
        res.data.data.forEach(item => {
          let arr = new Set()
          let note_arr = new Set()
          item.order_list.forEach(items => {
            items['select'] = false

            let time1 = items.order_id_num.substring(4, 12)
            let one = time1.slice(0, 4)
            let two = time1.slice(4)
            const formattedStr = this.formatDateString(one);
            const times = this.formatTimeString(two)
            items.order_time = formattedStr + ' ' + times
            items.order_id_num_fmt = items.order_id_num.slice(14)

            if (items.order_note != "") {
              arr.add(items.order_note)
            }

            if (items.service_point_note != "") {
              note_arr.add(items.service_point_note)
            }
          })
          item.note = Array.from(arr)
          item.note_arr = Array.from(note_arr)
          item.total_sort_weight_fmt = (item.total_sort_weight / 1000).toFixed(1)
          item.total_standard_weight_fmt = (item.total_standard_weight / 1000).toFixed(1)
        });

        this.setData({
          "delivery.deliveryList": res.data.data
        })
      } else {
        this.setData({
          "delivery.deliveryList": null
        })
      }
      wx.hideLoading()
    })
  },

  formatDateString(str) {
    // 使用 substring() 方法获取月份和日期部分
    const month = str.substring(0, 2);
    const day = str.substring(2);

    // 使用连接符连接月份和日期部分
    const formattedStr = month + '-' + day;

    return formattedStr;
  },
  formatTimeString(str) {
    // 使用 substring() 方法获取每个部分
    const part1 = str.substring(0, 2);
    const part2 = str.substring(2);
    // 使用连接符连接每个部分
    const formatTimes = part1 + ':' + part2;
    return formatTimes;
  },

  queryWarningShip() {
    let timestamp = app.globalData.quality_time
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
      timestamp
    }

    warning_ship(data).then(res => {
      if (res.data.code === 0) {
        let warningShipList = []
        if (res.data.data) {
          res.data.data.forEach(item => {
            warningShipList.push(dealTimeFormatMMDD(item))
          })
        }

        this.setData({
          warningShipList,
        })
      }
    })
  },

  //  发货页面
  shipTabChange(e) {
    let name = e.detail.name
    let status = 0
    if (name == "done") {
      status = 1
    }
    this.setData({
      shipActive: status
    })

    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    this.deliveryOrderList()
  },
  //打开采购商信息弹窗
  buyerNameInfo(e) {
    let info = e.currentTarget.dataset.info
    let list = info.note
    let pointNoteList = info.note_arr
    info.deliver_fee_fmt = this.dealMoney(info.deliver_fee)
    this.setData({
      infoShow: true,
      info: info,
      orderNoteList: list,
      orderPointNoteList: pointNoteList
    })
    this.addrDetail(info.address.address_id)
  },

  // 地址信息
  addrDetail(id) {
    let data = {
      id: id
    }
    addr_detail(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          service_fee: res.data.data.service_fee
        })
      }
    })
  },

  dealMoney(fen) {
    return Math.round(fen) / 100
  },

  closeDeliveryServicePoint() {
    this.setData({
      'delivery.servicePop': false,
    })
  },

  //选择服务点
  selectDeliveryServicePoint(e) {
    this.setData({
      'delivery.servicePop': false,
      'delivery.servicePonint': e.detail.name,
      'delivery.servicePonintId': e.detail.to_service_point_id
    })
    this.deliveryOrderList()
  },

  //复制
  copy(e) {
    let content = e.currentTarget.dataset.content;
    wx.setClipboardData({
      data: content,
      success: function (res) {}
    });
  },

  // 订单列表选择
  checked(event) {
    let id = event.currentTarget.dataset.info.order_id
    let index = event.currentTarget.dataset.index
    let indexs = event.currentTarget.dataset.indexs
    this.data.delivery.deliveryList[index].order_list[indexs].select = !this.data.delivery.deliveryList[index].order_list[indexs].select
    if (this.data.delivery.deliveryList[index].order_list[indexs].select) {
      this.data.delivery.selectOrderIds.push(id)
    } else {
      this.data.delivery.selectOrderIds = this.data.delivery.selectOrderIds.filter(items => {
        return items != id;
      }); //反选移除已经选中的
    }
    this.setData({
      'delivery.deliveryList': this.data.delivery.deliveryList
    });
  },

  delivery(e) {
    if (this.data.delivery.selectOrderIds.length == 0) {
      Toast('请选择订单');
      return
    }
    let data = {
      service_point_id: wx.getStorageSync(servicePointIDKey),
      order_id_list: this.data.delivery.selectOrderIds,
    }
    Dialog.confirm({
        title: '发货',
        message: '确定发货',
      })
      .then(() => {
        delivery_temp(data).then(res => {
          if (res.data.code == 0) {
            Toast('发货成功');
            this.deliveryOrderList()
            this.setData({
              'delivery.selectOrderIds': [],
            })
          }
        }).catch(err => {
          Toast(err.data.message);
        })
      })
      .catch(() => {});

  },
  closeCarendar(e) {
    this.setData({
      timesPop: false,
    })
  },
  //关闭信息弹窗
  closeInfo() {
    this.setData({
      infoShow: false,
    })
  },
  makePhoneCall(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.phone //仅为示例，并非真实的电话号码
    })
  },
  //打开时间弹窗
  openTimePop() {
    this.setData({
      timesPop: true,
    })
  },
  // 确认时间
  confirmTime(e) {
    app.globalData.quality_time = e.detail.getTime()

    this.setData({
      timesPop: false,
      times: e.detail.getTime(),
      nowTimes: dealTimeFormat2(e.detail.getTime())
    })
    this.deliveryOrderList()
  },

  onShow() {
    this.queryWarningShip()
  },

  seeProductDetail(e){
    console.log(e,111);
    let info = e.currentTarget.dataset.info
    let id = info.product_id
    wx.navigateTo({
      url: `/pages/supplier/product/info/index?id=${id}&from=sort`,
    })
  },
})
