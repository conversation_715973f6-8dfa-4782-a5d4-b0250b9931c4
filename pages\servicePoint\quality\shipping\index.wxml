<view class="nav" style="position: fixed;width: 100%;z-index: 99999;top: 0;height: 50rpx;">
  <view class="capsule-box">
    <view bindtap="openTimePop">时间：{{nowTimes}}</view>
  </view>
</view>

<view class="container">
  <view>
    <van-tabs active="{{ shipActive }}" animated sticky bind:change="shipTabChange" offset-top="{{20}}">
      <van-tab title="待发货" name="todo">
        <view wx:if="{{warningShipList&&warningShipList.length>0}}" class="warning">
          <view style="font-size: 28rpx;font-weight: bolder;">
            <van-tag type="danger">提示</van-tag>
            以下日期存在未发货订单
          </view>
          <view style="display: flex;margin: 10rpx 0;">
            <block wx:for="{{warningShipList}}" wx:key="index">
              <view style="font-size: 26rpx;margin-right:16rpx;">
                <van-tag plain type="warning">{{item}}</van-tag>
              </view>
            </block>
          </view>
        </view>

        <view wx:if="{{delivery.deliveryList}}" style="padding: 20rpx;box-sizing: border-box;">
          <view wx:for="{{delivery.deliveryList}}" wx:key="key" style="margin-bottom: 10rpx;">
            <view class="title" style="width: calc(100% - 45rpx);" catchtap="buyerNameInfo" data-info="{{item}}">
              <view style="display: flex;align-items: flex-start;gap: 10rpx;flex: 1;">
                <view>{{item.buyer_name}}
                  <van-icon name="phone-o" />
                </view>
                <view wx:if="{{item.note.length>0}}">
                  <image src="{{imageUrl+'icon/note.png'}}" wx:if="{{item.order_note !== ''}}" mode="widthFix" style="height: auto;width: 30rpx;"></image>
                </view>
                <view wx:if="{{item.note_arr.length>0}}" style="margin-left: 6rpx;">
                  <image src="/static/point/note.png" mode="widthFix" style="height: auto;width: 30rpx;"></image>
                </view>
              </view>

              <view wx:for="{{item.deliver_type_list}}" wx:for-item="ele" wx:key="ele" style="white-space:nowrap;text-align: right;">
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 1}}">【送货到店】</view>
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 2}}">【自提】</view>
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 3}}">【物流】
                  <text class="tag-text" style="background-color: #245e56;">{{item.logistics_name}}</text>
                </view>
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 4}}">【即时配送】
                  <text class="tag-text" style="background-color: #fc4840;" wx:if="{{item.deliver_type_list && item.deliver_type_list.length == 1}}">{{item.instant_deliver_name}}</text>
                </view>
              </view>

            </view>
            <view class="delivery_title weight-delivery">
              <view style="font-size: 24rpx;color:{{item.total_sort_num==item.total_num?'#303133':'red'}};">
                (分拣/总数)：{{item.total_sort_num }}/{{item.total_num}}
              </view>
              <view>(分拣/标重)：{{item.total_sort_weight_fmt}}kg/{{item.total_standard_weight_fmt}}kg </view>
            </view>
            <view class="delivery_title " style="font-size: 24rpx;color: #606266;"><text>地址:{{item.address.address}}</text>
              <van-icon data-content="{{item.address.address}}" bindtap="copy" name="description" />
            </view>
            <view class="delivery_title" style="font-size: 24rpx;color: #606266;"><text>定位:{{item.address.location.address}}</text>
              <van-icon data-content="{{item.address.location.address}}" bindtap="copy" name="description" />
            </view>
            <view class="order_list_wrap">
              <view class="order_list" wx:for="{{item.order_list}}" wx:for-item="items" wx:for-index="childindex" wx:key="key">
                <van-checkbox value="{{items.select}}" disabled="{{ (items.has_ship||!items.can_ship) || items.supplier_level=='station' && env==3}}" shape="square" data-indexs="{{childindex}}" data-info="{{items}}" data-index="{{index}}" label-disabled bind:change="checked">
                  <view class="order_list_content">
                    <view style="font-size: 22rpx;display: flex;align-items: center;justify-content: space-between;">
                      <view>
                        <text>【{{items.supplier_name}}】</text>
                        <text wx:if="{{items.supplier_level == 'station'}}" class="level">城市仓订单</text>
                        <text wx:if="{{items.has_ship}}" class="has_ship">已发货</text>
                      </view>

                      <view style="display: flex;">
                        <view>{{items.order_time}}</view>
                        <view wx:if="{{item.deliver_type_list &&  item.deliver_type_list.length > 1}}">
                          <view wx:if="{{items.deliver_type==1}}" style="font-size: 20rpx;color: #409EFF;">【送货到店】</view>
                          <view wx:if="{{items.deliver_type==2}}" style="font-size: 20rpx;color: #409EFF;">【自提】</view>
                          <view wx:if="{{items.deliver_type==3}}" style="font-size: 20rpx;color: #409EFF;">
                            【物流】
                            <text style="margin-left: 4rpx;">{{items.logistics_name}}</text>
                          </view>
                          <view wx:if="{{items.deliver_type==4}}" style="font-size: 20rpx;color: #409EFF;display: flex;">
                            <view>【即时配送】</view>
                            <view style="margin-left: 4rpx;" wx:if="{{items.instant_deliver_type == 1}}">跑腿</view>
                            <view style="margin-left: 10rpx;" wx:if="{{items.instant_deliver_type == 2}}">货拉拉</view>
                          </view>
                        </view>
                      </view>

                    </view>
                    <view wx:for="{{items.product_list}}" wx:for-item="itemss" wx:for-index="childindexs" wx:key="key" style="display: flex;align-items: flex-start; justify-content: space-between;">
                      <view style="display: flex;align-items: flex-start;flex: 1;justify-content: space-between;" data-info="{{itemss}}" catch:tap="seeProductDetail">
                        <view style="flex: 1;">
                            <text>{{childindexs + 1}}. </text>
                            <text>{{itemss.product_title}}</text>
                            <text style="color: #da571b;padding: 0 10rpx;">[{{itemss.sku_name}}]</text>
                            <text wx:if="{{itemss.stock_up_no>1}}" style="color: red;margin: 0 10rpx;">P{{itemss.stock_up_no}}</text>
                        </view>
                      </view>

                      <view style="text-align: right;width: 120rpx;">
                        <text style="color: {{itemss.due_num == itemss.sort_num?'':'red'}};">({{itemss.sort_num}}/{{itemss.due_num}})</text>
                        <view style="display: flex;justify-content: flex-end;">
                          <text class="sort_has" wx:if="{{!itemss.quality_has&&!items.has_ship}}">未品控</text>
                          <text class="sort_has colors" wx:if="{{itemss.quality_has&&!itemss.sort_has&&!items.has_ship}}">未分拣</text>
                          <!-- 品控状态 -->
                          <text wx:if="{{itemss.reason_type == 2 && itemss.sort_has && itemss.sort_num !==itemss.due_num}}" class="reason-num">品控不合格</text>
                          <text wx:if="{{itemss.reason_type == 5 && itemss.sort_has && itemss.sort_num !==itemss.due_num}}" class="reason-num">规格不符</text>
                          <text wx:if="{{itemss.reason_type == 4 && itemss.sort_has && itemss.sort_num !==itemss.due_num}}" class="reason-num">缺货</text>
                        </view>
                      </view>

                    </view>

                  </view>
                </van-checkbox>

              </view>
            </view>

          </view>
          <view class="delivery_button_wrap" wx:if="{{delivery.deliveryList}}">
            <view class="delivery_button" data-index="{{index}}" bindtap="delivery">发货</view>
          </view>
        </view>
        <van-empty wx:else description="暂无内容" />
      </van-tab>
      <van-tab title="已发货" name="done">
        <view wx:if="{{delivery.deliveryList}}" style="padding: 20rpx;box-sizing: border-box;">
          <view wx:for="{{delivery.deliveryList}}" wx:key="key">
            <view class="delivery_title" style="display: inline-flex;align-items: center;justify-content: space-between;width: calc(100% - 40rpx);" catchtap="buyerNameInfo" data-info="{{item}}">
              <view style="flex: 1;display: flex;align-items: flex-start;">
                <view>{{item.buyer_name}}
                  <van-icon name="phone-o" />
                </view>
                <view style="padding: 0 10rpx;" wx:if="{{item.note.length>0}}">
                  <image src="{{imageUrl+'icon/note.png'}}" wx:if="{{item.order_note !== ''}}" mode="widthFix" style="height: auto;width: 30rpx;"></image>
                </view>
                <view style="margin-left: 6rpx;" wx:if="{{item.note_arr.length>0}}">
                  <image src="/static/point/note.png" mode="widthFix" style="height: auto;width: 30rpx;"></image>
                </view>
              </view>


              <view wx:for="{{item.deliver_type_list}}" wx:for-item="ele" wx:key="ele">
                <view style="font-size: 20rpx;color: #409EFF;white-space: nowrap;" wx:if="{{ele == 1}}">【送货到店】</view>
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 2}}">【自提】</view>
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 3}}">【物流】
                  <text style="color: #fff;background-color: #245e56;font-size: 20rpx;padding: 0 6rpx;border-radius: 6rpx;">{{item.logistics_name}}</text>
                </view>
                <view style="font-size: 20rpx;color: #409EFF;" wx:if="{{ele == 4}}">【即时配送】
                  <text style="color: #fff;background-color: #fc4840;font-size: 20rpx;padding: 0 6rpx;border-radius: 6rpx;" wx:if="{{item.deliver_type_list && item.deliver_type_list.length == 1}}">{{item.instant_deliver_name}}</text>
                </view>
              </view>
            </view>
            <view class="delivery_title weight-delivery">
              <view style="font-size: 24rpx;color:{{item.total_sort_num==item.total_num?'#303133':'red'}};">
                (分拣/总数)：{{item.total_sort_num }}/{{item.total_num}}
              </view>
              <view>(分拣/标重)：{{item.total_sort_weight_fmt }}kg/{{item.total_standard_weight_fmt}}kg </view>
            </view>
            <view class="delivery_title" style="font-size: 24rpx;color: #606266;"><text>地址:{{item.address.address}}</text>
              <van-icon data-content="{{item.address.address}}" bindtap="copy" name="description" />
            </view>
            <view class="delivery_title" style="font-size: 24rpx;color: #606266;padding-right: 10rpx;"><text>定位:{{item.address.location.address}}</text>
              <van-icon data-content="{{item.address.location.address}}" bindtap="copy" name="description" />
            </view>
            <view class="order_list_wrap">
              <view class="order_list" wx:for="{{item.order_list}}" wx:for-item="items" wx:for-index="childindex" wx:key="key">
                <view class="order_list_content">
                  <view style="font-size: 22rpx;display: flex;align-items: center;justify-content: space-between;">
                    <view>
                      <text>【{{items.supplier_name}}】</text>
                      <text wx:if="{{items.supplier_level == 'station'}}" class="level">城市仓订单</text>
                    </view>
                    <view style="display: flex;">
                      <view>{{items.order_time}}</view>
                      <view wx:if="{{item.deliver_type_list &&  item.deliver_type_list.length > 1}}">
                        <view wx:if="{{items.deliver_type==1}}" style="font-size: 20rpx;color: #409EFF;">【送货到店】</view>
                        <view wx:if="{{items.deliver_type==2}}" style="font-size: 20rpx;color: #409EFF;">【自提】</view>
                        <view wx:if="{{items.deliver_type==3}}" style="font-size: 20rpx;color: #409EFF;">
                          【物流】
                          <text style="margin-left: 4rpx;">{{items.logistics_name}}</text>
                        </view>
                        <view wx:if="{{items.deliver_type==4}}" style="font-size: 20rpx;color: #409EFF;display: flex;">
                          <view>【即时配送】</view>
                          <view style="margin-left: 10rpx;" wx:if="{{items.instant_deliver_type == 1}}">跑腿</view>
                          <view style="margin-left: 10rpx;" wx:if="{{items.instant_deliver_type == 2}}">货拉拉</view>
                        </view>
                      </view>
                    </view>
                  </view>

                  <view wx:for="{{items.product_list}}" wx:for-item="itemss" wx:for-index="childindexs" wx:key="key" style="display: flex;align-items: flex-start; justify-content: space-between;">
                    <view style="display: flex;align-items: flex-start;flex: 1;justify-content: space-between;" data-info="{{itemss}}" catch:tap="seeProductDetail">
                      <view style="flex: 1;">
                        <text>{{childindexs + 1}}. </text>
                        <text>{{itemss.product_title}}</text>
                        <text style="color: #da571b;padding: 0 10rpx;">[{{itemss.sku_name}}]</text>
                        <text wx:if="{{itemss.stock_up_no>1}}" style="color: red;margin: 0 10rpx;">P{{itemss.stock_up_no}}</text>
                      </view>
                    </view>

                    <view style="text-align: right;width: 120rpx;">
                      <text style="color: {{itemss.due_num == itemss.sort_num?'':'red'}};">({{itemss.sort_num}}/{{itemss.due_num}})</text>
                      <view>
                        <text class="sort_has" wx:if="{{!itemss.sort_has&&!items.has_ship}}">未分拣</text>
                        <!-- 品控状态 -->
                        <text wx:if="{{itemss.reason_type == 2 && itemss.sort_has && itemss.sort_num !==itemss.due_num}}" class="reason-num">品控不合格</text>
                        <text wx:if="{{itemss.reason_type == 5 && itemss.sort_has && itemss.sort_num !==itemss.due_num}}" class="reason-num">规格不符</text>
                        <text wx:if="{{itemss.reason_type == 4 && itemss.sort_has && itemss.sort_num !==itemss.due_num}}" class="reason-num">缺货</text>
                      </view>
                    </view>

                  </view>
                </view>

              </view>
            </view>
          </view>
        </view>

        <van-empty wx:else description="暂无内容" />
      </van-tab>
    </van-tabs>

  </view>
  <view style="width: 100%;height: 300rpx;">
  </view>
</view>

<van-action-sheet show="{{ delivery.servicePop }}" actions="{{ delivery.servicePonintList }}" bind:close="closeDeliveryServicePoint" bind:select="selectDeliveryServicePoint" />
<van-calendar class="calendar" show="{{ timesPop }}" first-day-of-week="1" show-title="{{false}}" bind:close="closeCarendar" default-date="{{times}}" show-confirm="{{ false }}" min-date="{{newTimes}}" max-date="{{maxTimes}}" bind:confirm="confirmTime" />

<van-action-sheet show="{{ infoShow }}" bind:cancel="closeInfo" bind:close="closeInfo" z-index="999999" close-on-click-overlay cancel-text="取消">
  <view style="font-size: 28rpx;background-color: #f7f8fa;display: flex;flex-direction: column;gap: 20rpx;padding: 20rpx;">
    <view class="per">
      <view style="color: #979797;">
        <view>会员：{{info.buyer_name}}</view>
        <view style="display: flex;align-items: center;margin-bottom: 10rpx;">
          <view>联系人：{{info.address.contact.name}} </view>
          <view class="copy-info-class" bind:tap="copyInfo">复制地址信息 </view>
        </view>
        <view bindtap="makePhoneCall" data-phone="{{info.address.contact.mobile}}">电话号码：{{info.address.contact.mobile}}
          <van-icon name="phone-o" />
        </view>
      </view>
      <view style="color: #979797;margin-top: 10rpx;display: flex;align-content: center;gap: 40rpx">
        <view>配送费：{{info.deliver_fee_fmt}}元</view>
      </view>
      <view style="display: flex;margin-top: 40rpx; width: 100%;font-size: 28rpx;" wx:if="{{orderNoteList.length>0}}">
        <view style="width: 110rpx;">留言：</view>
        <view style="width: calc(100vw - 130rpx);white-space: inherit;">
          <view style="margin-bottom: 10rpx;" wx:for="{{orderNoteList}}" wx:key="key">{{item}}</view>
        </view>
      </view>

      <view style="display: flex;margin-top: 10rpx; width: 100%;font-size: 28rpx;" wx:if="{{orderPointNoteList.length>0}}">
        <view style="width: 180rpx;white-space: nowrap;">服务仓留言：</view>
        <view style="width: calc(100vw - 180rpx);white-space: inherit;">
          <view style="margin-bottom: 10rpx;" wx:for="{{orderPointNoteList}}" wx:key="key">{{item}}</view>
        </view>
      </view>
    </view>

    <view style="height: 60rpx;"></view>
  </view>
</van-action-sheet>


<van-dialog id="van-dialog" z-index="999" />
<van-toast id="van-toast" />
<tabBar active="{{3}}"></tabBar>