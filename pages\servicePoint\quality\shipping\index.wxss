.container {
  margin-top: 40rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.nav {
  position: fixed;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.capsule-box {
  margin-left: 20rpx;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  align-items: center;
}

.delivery_title {
  margin-left: 30rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.title {
  display: inline-flex;
  align-items: flex-start;
  justify-content: space-between;
  box-sizing: border-box;
  margin-left: 30rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.order_list {
  padding: 20rpx;
  box-sizing: border-box;
  border-bottom: 2rpx solid #e5e5e5;
}

.order_list_content {
  font-size: 28rpx;
  color: #666666;
  box-sizing: border-box;
}

.van-checkbox__label {
  flex: 1 !important;
}

.delivery_button_wrap {
  display: flex;
  justify-content: center;
  padding: 30rpx;
  box-sizing: border-box;
}

.delivery_button {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 8rpx;
  color: #ffffff;
  background-color: #FC4840;
}

.copy_button {
  background-color: #999999;
  color: #ffffff;
  padding: 0 10rpx;
  box-sizing: border-box;
  margin-left: 10rpx;
}

.calendar .van-popup {
  height: 500px !important;
}

.warning {
  margin: 10rpx 40rpx;
  padding: 6rpx;
  border-radius: 14rpx;
  background-color: #ffffff;
}

.copy-info-class {
  border: 1px solid #999898;
  border-radius: 10rpx;
  margin-left: 10rpx;
  font-size: 24rpx;
  padding: 0 10rpx;
}

.tag-text {
  font-size: 20rpx;
  padding: 0 8rpx;
  border-radius: 6rpx;
  color: #ffffff;
}

.reason-num {
  margin-left: 4rpx;
  background-color: #f76565;
  color: #fff;
  padding: 0 6rpx;
  font-size: 20rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.sort_has {
  background-color: #ee8c8c;
  border-radius: 6rpx;
  font-size: 18rpx;
  color: #fff;
  width: 60rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx 6rpx 6rpx;
}

.colors {
  background-color: #eec16e;
}

.has_ship {
  border: 1rpx solid #07c160;
  color: #07c160;
  font-size: 20rpx;
  padding: 0 6rpx;
  border-radius: 6rpx;
  background-color: #fff;
}

.weight-delivery {
  font-size: 24rpx;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 22rpx;
}

.level {
  border: 1rpx solid #07b7f3;
  color: #07b7f3;
  font-size: 20rpx;
  padding: 0 6rpx;
  border-radius: 6rpx;
  margin-left: 6rpx;
}

.transfer-price {
  border-radius: 10rpx;
  text-align: center;
  display: flex;
  justify-content: space-between;
}


.per {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
}