import dayjs from '../../../../libs/dayjs';
import {
  dealTimeFormat1,
  dealTimeFormat2,
  servicePointIDKey,
  stationInfoKey
} from '../../../../utils/dict';

import {
  sorting_list_temp,
  sort_search
} from '../../../../apis/warehouse/sorting';
const app = getApp()

Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    times: 0,
    nowTimes: 0,
    maxTimes: 0,
    newTimes: 0,
    sortingActive: 0,
    searchSortValue: "", // 分拣搜索
    sorting: {
      activeNames: [1],
      servicePonintList: [], //服务点列表
      servicePonint: "", //服务点标题
      servicePonintId: "", //服务点id
      timesPop: false,
      servicePop: false,
    },
    timesPop: false,
  },

  onLoad(options) {
    let now = dayjs()
    let newTimes = now.subtract(30, 'day').valueOf()
    let maxTimes = now.add(8, 'day').valueOf()
    let time = app.globalData.quality_time
    this.setData({
      times: time,
      nowTimes: dealTimeFormat2(time),
      maxTimes: maxTimes,
      newTimes: newTimes,
    });
    // this.sortingList()
  },
  onShow() {
    if (this.data.searchSortValue !== '') {
      this.onSearchForSort()
    } else {
      let status = ''
      if (this.data.sortingActive == 0) {
        status = 'no'
      }
      if (this.data.sortingActive == 1) {
        status = 'yes'
      }
      this.sortingListHas(status)
    }

  },

  //打开时间弹窗
  openTimePop() {
    this.setData({
      timesPop: true,
    })
  },
  //蓝牙
  bluetooth() {
    wx.navigateTo({
      url: '/packageSort/bluetooth/index',
    })
  },

  // /分拣页面
  switchSortingList(e) {
    this.setData({
      sortingActive: e.detail.name
    })

    if (this.data.searchSortValue !== '') {
      this.onSearchForSort()
      return
    }
    let status = ''
    if (e.detail.name == 0) {
      status = 'no'
    } else {
      status = 'yes'
    }

    this.sortingListHas(status)
  },

  sortingListHas(e) {
    let data = {
      timestamp: app.globalData.quality_time,
      sort_status: e
    }
    if (app.globalData.env == 3) {
      data.service_point_id = wx.getStorageSync(servicePointIDKey)
    }
    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      data.service_point_id = station_info.service_point_id
      data.station_id = station_info.id
    }
    sorting_list_temp(data).then(res => {
      if (res.data.code == 0) {
        if (e == 'no') {
          this.setData({
            "sorting.sortingList": res.data.data,
          })
        }
        if (e == 'yes') {
          this.setData({
            "sorting.sortingListHas": res.data.data,
          })
        }
      }
    })
  },

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    wx.navigateTo({
      url: `/pages/supplier/product/info/index?id=${id}&from=sort`,
    })
  },

  jumpSorting2(e) {
    let servicepointid = e.currentTarget.dataset.servicepointid
    let stockupid = e.currentTarget.dataset.stockupid
    wx.navigateTo({
      url: '/packageSort/sortingDetail/index?stockupid=' + stockupid + '&servicepointid=' + servicepointid,
    })
  },
  //跳转分拣
  jumpSorting(e) {
    let servicepointid = e.currentTarget.dataset.servicepointid
    let stockupid = e.currentTarget.dataset.stockupid
    wx.navigateTo({
      url: '/packageSort/sortingDetail/index?stockupid=' + stockupid + '&servicepointid=' + servicepointid,
    })
  },
  // 分拣搜索
  onSearchForSort(e) {
    let status = parseInt(this.data.sortingActive + 1)
    let content = ''
    if (e) {
      content = e.detail
    } else {
      content = this.data.searchSortValue
    }
    let param = {
      timestamp: this.data.times,
      content: content,
      sort_status: status
    }
    if (app.globalData.env == 3) {
      param.service_point_id = wx.getStorageSync(servicePointIDKey)
    }
    if (app.globalData.env == 7) {
      let station_info = wx.getStorageSync(stationInfoKey)
      param.service_point_id = station_info.service_point_id
      param.station_id = station_info.id
    }

    sort_search(param).then(res => {
      if (status === 1) {
        this.setData({
          "sorting.sortingList": res.data.data,
          searchSortValue: content
        })


      }
      if (status === 2) {
        this.setData({
          "sorting.sortingListHas": res.data.data,
          searchSortValue: content
        })
      }


    }).catch(err => {
      Toast(err.data.message)
    })
  },

  onCancelForSort(e) {
    let status = parseInt(this.data.sortingActive + 1)
    this.setData({
      searchSortValue: ""
    })
    let state = ''
    if (status === 1) {
      state = 'no'
    } else {
      state = 'yes'
    }
    this.sortingListHas(state)
  },

  // 确认时间
  confirmTime(e) {
    app.globalData.quality_time = e.detail.getTime()
    this.setData({
      timesPop: false,
      times: e.detail.getTime(),
      nowTimes: dealTimeFormat2(e.detail.getTime())
    })
    let status = ''
    if (this.data.sortingActive == 0) {
      status = 'no'
    } else {
      status = 'yes'
    }
    this.sortingListHas(status)
  },

  closeCarendar(e) {
    this.setData({
      timesPop: false,
    })
  },

})