<view class="nav" style="position: fixed;width: 100%;z-index: 99999;top: 0;height: 50rpx;">
  <view class="capsule-box" style="display:flex;">
    <view bindtap="openTimePop">时间：{{nowTimes}}</view>
    <view bindtap="bluetooth">蓝牙</view>
  </view>
</view>

<view class="container">
  <view>
    <view>
      <van-tabs active="{{ sortingActive }}" z-index="999" bind:change="switchSortingList" sticky offset-top="20">
        <van-tab title="未分拣列表">
          <van-search value="{{ searchSortValue }}" data-status="{{1}}" placeholder="请输入商品标题" show-action bind:search="onSearchForSort" bind:cancel="onCancelForSort" />
          <view style="margin: 20rpx;" wx:for="{{sorting.sortingList}}" wx:key="key">
            <view class="sorting_goods_list">
              <view catchtap="jumpProductDetail" style="display: flex;justify-content: space-between;flex: 1;" data-info="{{item}}">
                <view style="font-weight: bold;flex: 1;">
                  <text>{{index + 1}}. {{item.product_title}}</text>
                  <text style="color: #da571b;">[{{item.sku_name}}]</text>
                  <text wx:if="{{item.stock_up_no>1}}" style="color: red;margin-left: 10rpx;white-space: nowrap;">P{{item.stock_up_no}}</text>
                </view>
              </view>
              <view class="not-has" bindtap="jumpSorting2" style="{{item.quality_has?'':'background:#e5e5e5'}}" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}">
                去分拣({{item.quality_num}})
              </view>
            </view>
            <view>
            </view>
          </view>
        </van-tab>
        <van-tab title="已分拣列表">
          <van-search value="{{ searchSortValue }}" data-status="{{2}}" placeholder="请输入商品标题" show-action bind:search="onSearchForSort" bind:cancel="onCancelForSort" />
          <view style="margin: 20rpx;" wx:for="{{sorting.sortingListHas}}" wx:key="key">
            <view class="sorting_goods_list">
              <view catchtap="jumpProductDetail" style="display: flex;justify-content: space-between;flex: 1;" data-info="{{item}}">
                <view style="font-weight: bold;flex: 1;">
                  <text>{{index + 1}}. {{item.product_title}}</text>
                  <text style="color: #da571b;">[{{item.sku_name}}]</text>
                  <text wx:if="{{item.stock_up_no>1}}" style="color: red;margin-left: 10rpx;white-space: nowrap;">P{{item.stock_up_no}}</text>
                </view>
              </view>
              <view class="has" bindtap="jumpSorting" data-servicePointId="{{item.service_point_id}}" data-stockUpId="{{item.id}}">
                详情({{item.quality_num}})
              </view>
            </view>
          </view>
          <view>
          </view>
        </van-tab>
      </van-tabs>
    </view>
    <van-calendar class="calendar" show="{{ timesPop }}" first-day-of-week="1" show-title="{{false}}" bind:close="closeCarendar" show-confirm="{{ false }}" default-date="{{times}}" min-date="{{newTimes}}" max-date="{{maxTimes}}" bind:confirm="confirmTime" />
    <van-action-sheet show="{{ sorting.servicePop }}" actions="{{ sorting.servicePonintList }}" bind:close="closeSortingServicePoint" bind:select="selectSortingServicePoint" />

  </view>

  <view style="width:100%;height: 300rpx;"></view>
</view>


<tabBar active="{{2}}"></tabBar>