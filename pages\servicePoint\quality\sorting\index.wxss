.container {
  margin-top: 40rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.filterCondition {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
}

.filterCondition view:nth-child(1),
.filterCondition view:nth-child(2) {
  width: calc(100% - 100rpx);
}

.filterCondition view:nth-child(2) {
  width: 80rpx;
  text-align: center;
  padding: 6rpx 0;
  border-radius: 8rpx;
  font-size: 24rpx;
  background-color: #1E90FF;
  color: #ffffff;
}

.product_title {
  display: flex;
  gap: 10rpx;
  font-weight: bold;
  flex: 1;
  justify-content: space-between;
}

.sorting_list {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.sorting_list view {
  margin-right: 10rpx;
}

.sorting_goods_list {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  padding: 6rpx;
  border-radius: 6rpx;
}

.sorting_goods_list view:nth-child(2) {
  height: 50rpx;
  padding: 0 20rpx;
  margin-left: 10rpx;
  white-space: nowrap;
  line-height: 50rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.sorting_goods_list .not-has {
  background-color: #1E90FF;
  color: #fff;
}

.sorting_goods_list .has {
  color: #fff;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  background-color: #12aaab;
}

.sorting_button_wrap {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.sorting_button {
  width: 200rpx;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #FC4840;
  border-radius: 8rpx;
  color: #ffffff;
}

.print {
  width: 200rpx;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #1E90FF;
  border-radius: 8rpx;
  color: #ffffff;
}

.nav {
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  padding: 0 20rpx;
}

.nav view:nth-child(2) {
  width: 80rpx;
  text-align: center;
  padding: 6rpx 0;
  border-radius: 8rpx;
  font-size: 24rpx;
  background-color: #1E90FF;
  color: #ffffff;
  margin-left: 20rpx;
}


.calendar .van-popup {
  height: 500px !important;
}