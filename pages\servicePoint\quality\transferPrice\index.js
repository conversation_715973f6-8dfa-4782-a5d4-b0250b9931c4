import {
  order_ship,
  order_ship_adjust
} from '../../../../apis/servicePoint/quality';
import {
  servicePointIDKey,
} from '../../../../utils/dict';
import Toast from '@vant/weapp/toast/toast';
Page({

  data: {
    buyer_id: '',
    timestamp: 0,
    order_info: {},
    show: false,
    settle_unit_price: 0,
    settle_unit_price_fmt: '',
    product_id: '',
    order_id: '',
    is_check_weight: false
  },

  onLoad(options) {
    let info = JSON.parse(options.param)
    this.setData({
      buyer_id: info.buyer_id,
      timestamp: info.timestamp
    })
    this.getOrderShip()
  },

  getOrderShip() {
    let data = {
      buyer_id: this.data.buyer_id,
      timestamp: this.data.timestamp,
      service_point_id: wx.getStorageSync(servicePointIDKey)
    }
    order_ship(data).then(res => {
      if (res.data.code == 0) {
        let stats = res.data.data
        stats.total_sort_weight_fmt = (stats.total_sort_weight / 1000).toFixed(1)
        stats.total_standard_weight_fmt = (stats.total_standard_weight / 1000).toFixed(1)

        stats.order_list.forEach(item => {
          let time1 = item.order_id_num.substring(4, 12)
          let one = time1.slice(0, 4)
          let two = time1.slice(4)
          const formattedStr = this.formatDateString(one);
          const times = this.formatTimeString(two)
          item.order_time = formattedStr + ' ' + times
          item.order_id_num_fmt = item.order_id_num.slice(14)
          item.product_list.forEach(ele => {
            ele.price_fmt = (ele.price / 100).toFixed(2)
            ele.settle_unit_price_fmt = (ele.settle_unit_price / 100).toFixed(2)
            ele.product_rough_weight_unit_price_kg_fmt = (ele.product_rough_weight_unit_price_kg / 100).toFixed(2)
            ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(2)
            ele.sort_weight_fmt = (ele.sort_weight / 1000).toFixed(2)

            if (ele.is_check_weight) {
              let num = (ele.product_rough_weight_unit_price_kg - ele.settle_unit_price) / ele.product_rough_weight_unit_price_kg
              ele.percentage = Math.abs(num)
            }
            if (!ele.is_check_weight) {
              let num = (ele.price - ele.settle_unit_price) / ele.price
              ele.percentage = Math.abs(num)
            }

          })
        })
        this.setData({
          order_info: stats
        })
      }
    })
  },
  formatDateString(str) {
    // 使用 substring() 方法获取月份和日期部分
    const month = str.substring(0, 2);
    const day = str.substring(2);

    // 使用连接符连接月份和日期部分
    const formattedStr = month + '-' + day;

    return formattedStr;
  },
  formatTimeString(str) {
    // 使用 substring() 方法获取每个部分
    const part1 = str.substring(0, 2);
    const part2 = str.substring(2);
    // 使用连接符连接每个部分
    const formatTimes = part1 + ':' + part2;
    return formatTimes;
  },



  handlePrice(e) {
    let ele = e.currentTarget.dataset.ele
    let item = e.currentTarget.dataset.item
    if (item.has_ship) {
      Toast('订单已发货')
      return
    }
    if (!ele.sort_has) {
      Toast('商品未分拣')
      return
    }

    if (ele.sort_num == 0) {
      Toast('分拣数为0')
      return
    }

    this.setData({
      show: true,
      settle_unit_price_fmt: ele.settle_unit_price_fmt,
      product_id: ele.product_id,
      order_id: item.order_id,
      is_check_weight: ele.is_check_weight
    })
  },

  closeEditPrice() {
    this.setData({
      show: false
    })
  },
  // 价格输入
  inputPrice(e) {
    let price = parseInt((e.detail.value) * 100)
    let price_fmt = this.splitDotTwo(e.detail.value)
    this.setData({
      settle_unit_price: price,
      settle_unit_price_fmt: price_fmt
    })
  },

  submitEditPrice() {
    let data = {
      order_id: this.data.order_id,
      product_id: this.data.product_id,
      settle_unit_price: this.data.settle_unit_price
    }

    order_ship_adjust(data).then(res => {
      if (res.data.code == 0) {
        Toast('调整成功');
        this.getOrderShip()
      }

    }).catch(err => {
      wx.showToast({
        title: err.data.message,
        icon: "none",
        duration: 2000
      })
    })
  },

  splitDotTwo(e) {
    let price = e.toString().replace(/\s+/g, '')
    if (price == '') {
      return 0
    }
    price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
    price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    price = price.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      price = parseFloat(price);
    }
    return price
  },

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    wx.navigateTo({
      url: `/pages/supplier/product/info/index?id=${id}&from=sort`,
    })
  },
  onShow() {

  },

})