<view class="content">
  <view class="buyer-info">
    <view class="deliver">
      <view style="font-size: 28rpx;font-weight: bold;">会员： {{order_info.buyer_name}}</view>
      <view wx:for="{{order_info.deliver_type_list}}" wx:key="index" style="white-space:nowrap;text-align: right;">
        <view class="color-text" wx:if="{{item == 1}}">【送货到店】</view>
        <view class="color-text" wx:if="{{item == 2}}">【自提】</view>
        <view class="color-text" wx:if="{{item == 3}}">【物流】
          <text class="tag-text" style="background-color: #245e56;">{{order_info.logistics_name}}</text>
        </view>
        <view class="color-text" wx:if="{{item == 4}}">【即时配送】
          <text class="tag-text" style="background-color: #fc4840;" wx:if="{{order_info.deliver_type_list && order_info.deliver_type_list.length == 1}}">{{order_info.instant_deliver_name}}</text>
        </view>
      </view>
    </view>
    <view class="delivery_title">
      <view style="font-size: 24rpx;color:{{order_info.total_sort_num==item.total_num?'#303133':'red'}};">
        分拣/总数：{{order_info.total_sort_num }}/{{order_info.total_num}}
      </view>
      <view>分拣/标重：{{order_info.total_sort_weight_fmt}}kg/{{order_info.total_standard_weight_fmt}}kg </view>
    </view>
    <view style="font-size: 24rpx;color: #606266;margin: 10rpx 0;">地址：{{order_info.address.address}}</view>
    <view style="font-size: 24rpx;color: #606266;">定位：{{order_info.address.location.address}}</view>
  </view>

  <!-- 订单 -->
  <view class="info">

    <view wx:for="{{order_info.order_list}}" wx:key="index" class="order-item">
      <view class="time">
        <view>{{item.supplier_name}}
          <van-tag type="success" wx:if="{{item.has_ship}}">已发货</van-tag>
        </view>
        <view>{{item.order_time}}</view>
      </view>

      <view wx:for="{{item.product_list}}" wx:key="product_id" wx:for-item="ele" class="product">

        <view class="product-item">
          <view style="margin-right: 6rpx;">{{index +1}}.</view>
          <view style="flex: 1;">
            <text catchtap="jumpProductDetail" data-info="{{ele}}">{{ele.product_title}} </text>
            <view class="num">
              <view>订单/分拣： {{ele.due_num}}/ {{ele.sort_num}}</view>
              <text wx:if="{{ele.is_check_weight}}" style="color: orange;font-size: 24rpx;">称重</text>
            </view>
            <view class="num" wx:if="{{ele.is_check_weight}}">
              <view style="flex: 1;">毛重： {{ele.rough_weight_fmt}}kg</view>
              <view style="flex: 1;">分拣总重： {{ele.sort_weight_fmt}}kg</view>
            </view>
            <view class="num">
              <view style="flex: 1;">
                <text>销售单价：{{ele.price_fmt}}</text>
                <text wx:if="{{ele.is_check_weight}}" style="font-size: 22rpx;">({{ele.product_rough_weight_unit_price_kg_fmt}}/kg)</text>
              </view>
              <view class="num" catch:tap="handlePrice" data-ele="{{ele}}" data-item="{{item}}">
                <view>结算单价：</view>
                <view class="edit-input {{ele.product_rough_weight_unit_price_kg == ele.settle_unit_price?'':'red'}}" wx:if="{{ele.is_check_weight}}">{{ele.settle_unit_price_fmt}}</view>
                <text wx:if="{{ele.is_check_weight}}" style="font-size: 22rpx;">/kg</text>
                <view class="edit-input {{ele.price == ele.settle_unit_price?'':'red'}}" wx:if="{{!ele.is_check_weight}}">{{ele.settle_unit_price_fmt}}</view>
              </view>
            </view>
            <view wx:if="{{ele.percentage >= 0.2 &&  ele.settle_unit_price>0}}" class="percentage">结算单价相差销售单价20%</view>
          </view>
        </view>

      </view>
    </view>

  </view>
  <view style="height: 30rpx;"></view>


</view>


<van-dialog use-slot title="结算单价" show="{{ show  }}" show-cancel-button bind:close="closeEditPrice" bind:confirm="submitEditPrice">
  <view style="margin: 20rpx 0 ;padding: 20rpx;">
    <view class="dig-price">
      <text wx:if="{{is_check_weight}}">计重:</text>
      <text wx:if="{{!is_check_weight}}">计件:</text>
      <input class="edit-price" type="digit" bindinput="inputPrice" bindblur="blurPrice" value="{{ settle_unit_price_fmt}}" />
      <text wx:if="{{is_check_weight}}">/kg</text>
    </view>
    <view style="color: orange;font-size: 24rpx;margin-top: 20rpx;text-align: center;">
      <text style="margin-right: 30rpx;">计件：一件的价格</text>
      <text>计重：多少元/kg</text>
    </view>
  </view>
</van-dialog>

<van-toast id="van-toast" />