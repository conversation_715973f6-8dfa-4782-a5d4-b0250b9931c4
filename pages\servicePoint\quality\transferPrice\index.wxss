.content {
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f7f8fa;
  min-height: 100vh;
}
.buyer-info{
  background-color: #fff;
  padding: 10rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
}

.info {
  margin-bottom: 20rpx;
}

.deliver {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.color-text {
  font-size: 20rpx;
  color: #409EFF;
}

.tag-text {
  font-size: 20rpx;
  padding: 0 8rpx;
  border-radius: 6rpx;
  color: #ffffff;
}

.delivery_title {
  font-weight: bold;
  font-size: 24rpx;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 22rpx;
  margin-top: 10rpx;
}

.order-item {
  margin-bottom: 26rpx;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 26rpx;
}

.time {
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #767777;
  padding: 0 20rpx;
}

.product {
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 10rpx;
  padding: 10rpx;
  border-radius: 10rpx;
}

.num {
  display: flex;
  align-items: flex-end;
  margin-top: 10rpx;
  gap: 10rpx;
  color: #858484;
  font-size: 26rpx;
}

.edit-input {
  border-bottom: 1rpx solid #d6d4d4;
  color: #5f5f5f;
  font-size: 28rpx;
  text-align: center;
  width: 100rpx;
}

.edit-price {
  border-bottom: 1rpx solid #d6d4d4;
  color: #5f5f5f;
  font-size: 28rpx;
  text-align: center;
}

.dig-price {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
.red{
  color: red;
}
.percentage{
  color: #fa8a8a;
  font-size: 22rpx;
  margin-top: 10rpx;
  text-align: right;
}