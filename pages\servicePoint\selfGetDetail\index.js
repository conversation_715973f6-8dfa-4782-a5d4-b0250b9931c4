const app = getApp()
import {
  buyer_order_detail,
  confirm_receive,
  deliver_note_by_buyer,
  logistics_create
} from '../../../apis/servicePoint/delivery';

import {
  take_delivery_Detail
} from '../../../apis/servicePoint/takeDelivery';
const uploadFile = require('../../../utils/uploadFile');
const util = require('../../../utils/util');
import {
  upload_sign,
} from '../../../utils/api';
import {
  categoryCoverProcess,
  dealTimeFormat1,
  deliverTypeSelfGet,
} from '../../../utils/dict';

import dayjs from "../../../libs/dayjs"

import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
Page({

  data: {
    fileList: [],
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    fileLists: [],
    fileListsLogistics: [],
    orderlistIds: [],
    imgList: [], //图片列表
    imgListLogistics: [], //图片列表
    deliver_note: {
      day_at: 0,
    }, // 配送单
    orderList: [],
    service_point_id: '',
    buyer_id: '',
    buyer_name: '',
    addr: {},
    timestamp: 0,
    logisticsImg: [],
    logisticsTime: 0,
    logisticsTimeFmt: 0,
    radio: '1',
    logisticsAutoReceiveHour: 12,
    //  操作栏
    showOperate: false,
    selectOrder: {}, // 选中订单
    showUploadDeliver: false, // 展示上传订单交付
  },

  onLoad(options) {
    console.log(options, 890);

    let buyer_id = options.buyer_id
    let addr = JSON.parse(options.addr)
    console.log(addr, 890);

    let buyer_name = options.buyer_name
    let timestamp = parseInt(options.timestamp)
    let service_point_id = options.service_point_id
    if (!buyer_id || !timestamp) {
      wx.navigateBack()
    }
    this.setData({
      buyer_id,
      addr,
      buyer_name,
      timestamp,
      service_point_id
    })

    this.queryOrderList()

    this.queryDeliverNote()
  },

  returnBack() {
    wx.navigateBack()
  },

  //  查询-配送单
  queryDeliverNote() {
    let timestamp = this.data.timestamp
    let buyer_id = this.data.buyer_id
    let deliver_type = deliverTypeSelfGet
    let data = {
      buyer_id,
      timestamp,
      deliver_type,
    }
    deliver_note_by_buyer(data).then(res => {
      let r = res.data.data
      if (r.day_at != 0) {
        r.day_at_format = this.dealTimeToDay(r.day_at)
        r.begin_at_format = this.dealTime(r.begin_at)
        r.end_at_format = this.dealTime(r.end_at)
        r.created_at_format = this.dealTime(r.created_at)
      }
      this.setData({
        deliver_note: r
      })
    })
  },


  dealTimeToDay(at) {
    return dayjs(at).format('YYYY-MM-DD')
  },

  dealTime(at) {
    return dayjs(at).format('YYYY-MM-DD HH:mm:ss')
  },


  // 点击查看-配送单
  handleViewDeliverNote(e) {
    let info = e.currentTarget.dataset.info
    let dayAt = this.dealTimeToDay(info.day_at)
    let name = dayAt + "配送单" + ".xlsx"
    let url = e.currentTarget.dataset.path
    let path = this.data.imageUrl + url
    wx.downloadFile({
      url: path,
      filePath: wx.env.USER_DATA_PATH + '/' + name,
      success(res) {
        console.log(res, 111);
        if (res.statusCode === 200) {
          const filePath = res.filePath
          wx.openDocument({
            filePath: filePath,
            fileType: 'xlsx',
            showMenu: true,
            success: function (res) {
              console.log(res)
            }
          })
        }
      }
    })
  },

  refreshDeliverNote() {
    this.queryDeliverNote()
  },

  queryOrderList() {
    let buyer_id = this.data.buyer_id
    let timestamp = this.data.timestamp
    let service_point_id = this.data.service_point_id
    let data = {
      service_point_id: service_point_id,
      buyer_id: buyer_id,
      timestamp: timestamp,
    }
    let noteSet = new Set()

    take_delivery_Detail(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data

        let showUploadDeliver = false
        list.forEach(item => {
          item.product_list.forEach(items => {
            items.sort_weight_fmt = items.sort_weight / 1000
          })

          //  物流单上传判断
          if (item.order_status != 9) {
            showUploadDeliver = true
          }

        })

        this.setData({
          orderList: res.data.data,
          showUploadDeliver: showUploadDeliver,
        })
      }
    })
  },

  // 物流单
  previewRlogisticsImg(e) {
    console.log(4444, e)
    let src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src, // 当前显示图片的http链接
      urls: [src],
    })
  },

  // 上传
  afterRead(e) {
    console.log(e)
    let that = this
    let tempFilePaths = e.detail.file.url
    let fileType = e.detail.file.type
    let type = "order"
    // this.data.fileList = []
    upload_sign(type).then(res => {
      if (res.data.code == 0) {
        console.log(res)
        let uploadData = res.data.data;
        let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(tempFilePaths))
        uploadFile(uploadData.host, tempFilePaths, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
          if (data.statusCode == 200) {
            const {
              fileList = []
            } = that.data;
            fileList.push({
              url: that.data.imageUrl + newPath
            });
            let list = {
              type: fileType,
              origin_name: util.siding(tempFilePaths),
              name: newPath,
            }
            this.data.imgList.push(list)
            that.setData({
              fileLists: fileList,
              imgList: this.data.imgList
            });
            // this.data.fileLists.push(e.detail.list,
            // that.setData({
            //   "formData.video_file": list
            // })
          }
        })
      }
    })
  },

  delete(e) {
    let index = e.detail.index;
    this.data.fileLists.splice(index, 1);
    this.data.imgList.splice(index, 1);
    this.setData({
      fileLists: this.data.fileLists,
      imgList: this.data.imgList
    })
  },

  // 时间
  onChange(e) {
    let a = e.detail
    this.setData({
      radio: a,
    })
  },

  submit() {
    let that = this
    if (this.data.fileLists.length > 0) {
      Dialog.confirm({
          title: '提示',
          message: '确定交付订单',
        })
        .then(() => {
          // on confirm
          this.confirmReceive()
        })
        .catch(() => {
          // on cancel
        });
    } else {
      Toast('图片缺失');
    }
  },

  //配送完成
  confirmReceive() {
    let orderList = this.data.orderList
    let orderIDs = []

    orderList.forEach(item => {
      // if (item.order_status != 9) {
      //   orderIDs.push(item.id)
      // }

      if (item.order_status != 9) {
        if (app.globalData.env == 3 && item.supplier_level == 'point') {
          orderIDs.push(item.id)
        }

        if (app.globalData.env == 7 && item.supplier_level == 'station') {
          orderIDs.push(item.id)
        }

      }
    });
    let that = this
    if (orderIDs.length < 1) {
      Toast("刷新")
      that.refresh()
      return
    }

    let data = {
      order_id_list: orderIDs,
      delivery_img_list: this.data.imgList
    }

    console.log(data);
    confirm_receive(data).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '交付完成',
          icon: "none",
          duration: 1500
        })
        setTimeout(() => {
          that.refresh()
        }, 1500);
      }
    })
  },

  refresh() {
    this.setData({
      showUploadDeliver: false,
    })
    this.queryOrderList()
  },

  openShowOperate(e) {
    let info = e.currentTarget.dataset.info
    console.log(info);
    if (info.logistics_time != 0) {
      info.logistics_time_fmt = dealTimeFormat1(info.logistics_time)
    }
    if (info.order_status === 9) {
      info.order_status_record.receive_time_fmt = dealTimeFormat1(info.order_status_record.receive_time)
    }

    this.setData({
      showOperate: true,
      selectOrder: info,
    })
  },

  closeShowOperate() {
    this.setData({
      showOperate: false,
    })
  },

  previewImg(e) {
    let src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src,
      urls: [src],
    })
  },

})