<view class="container">
  <view class="nav">
    <view class="capsule-box">
      <view bindtap="onDisplay">时间：{{nowStampFmt}}</view>
      <van-calendar class="calendar" show="{{ show }}" default-date="{{ now}}" max-date="{{maxDate}}" min-date="{{ minDate }}" show-confirm="{{false}}" show-title="{{false}}" bind:close="onClose" bind:confirm="onConfirm" allow-same-day="true" />
    </view>
  </view>

  <view style="padding-top: 60rpx;">
    <view class="buyer_list_wrap" wx:if="{{list!=''}}" style="padding-top: 0 !important;box-sizing: border-box;">
      <view class="buyer_list" wx:for="{{list}}" wx:key="key">
        <view class="buyer_content" style="{{deliveryActive==0?'width:calc(100% - 80rpx)':'width:100%'}}">
          <view class="buyer_name">
            <view class="name">
              {{item.buyer_name}}
            </view>
            <text class="name-instant">{{item.instant_deliver_name}}</text>
            <text wx:if="{{!item.received}}" class="tag">未完成</text>
          </view>
          <view style="display: flex;justify-content: space-between;">
            <view class="left">
              <view class="buyer_phone" style="margin: 10rpx 0;">
                <view>电话: {{item.address.contact.mobile}}</view>
                <view style="margin-left: 20rpx;">联系人: {{item.address.contact.name}}</view>
              </view>
              <view class="buyer_address" style="margin: 10rpx 0;">定位: {{item.address.location.address}}</view>
              <view class="buyer_address" style="margin: 10rpx 0;">地址: {{item.address.address}}</view>

              <view style="display: flex;gap: 40rpx;">
                <view class="ship_quantily">发货: <text class="num_color">{{item.sort_num}}件</text></view>
                <view class="quality_control_num">重量: <text class="num_color">{{item.sort_weight_fmt}}kg</text>
                </view>
              </view>
            </view>
            <view class="right" style="display: flex;align-items: center;">
              <view data-info="{{item}}" bindtap="orderDetail">
                <!-- detail -->
                <image src="{{imageUrl+'icon/right-slim.png'}}" mode="widthFix" style="height: auto;width: 40rpx;"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <van-empty wx:else description="暂无内容" />
    <view style="width: 100%;height: 240rpx;">
    </view>
  </view>

</view>

<tabBar active="{{0}}"></tabBar>