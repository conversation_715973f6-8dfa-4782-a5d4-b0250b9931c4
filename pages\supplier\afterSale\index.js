import dayjs from "../../../libs/dayjs"
import {
  after_sale_list
} from '../../../apis/supplier/comment'

import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
  dealFenToYuan,
} from '../../../utils/dict';
const app = getApp()
Page({
  data: {
    minDate: 0,
    maxDate: 0,
    atMaxDate:0,
    date: '',
    show: false,
    allShow:false,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    time_range: [],
    time_begin: 0,
    time_end: 0,
    active: 'saleing',
    isEnd: false,
    auditStatus: 1,
    page: 1,
    limit: 6,
    saleList: [],
    saleTypeList: [{
        name: 'saleing',
        title: '待审核'
      },
      {
        name: 'passThrough',
        title: '通过'
      },
      {
        name: 'noPass',
        title: '不通过'
      }

    ]
  },

  onLoad(options) {
    let now = dayjs()
    let minDate = now.subtract(70, "day").startOf('day').valueOf()
    let atMaxDate = now.endOf('day').valueOf()
    let maxDate = 0

    // let time_begin = now.subtract(20, "day").startOf('day').valueOf()
    let time_begin = 0

    let time_range = [time_begin, atMaxDate]
    this.setData({
      // date: `${this.dealTimeToDay(time_begin) }-${ this.dealTimeToDay(maxDate)}`,
      date: '筛选',
      time_range: time_range,
      time_begin: time_begin,
      time_end: maxDate,
      minDate,
      maxDate,
      atMaxDate
    });
    this.salcList()
  },
  dealTimeToDay(at) {
    return dayjs(at).format('MM/DD')
  },
  onDisplay() {
    this.setData({
      show: true,
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },
  onConfirm(event) {
    const [start, end] = event.detail;
    let time_begin = start.getTime()
    let time_end = dayjs(end.getTime()).endOf('day').valueOf()
    let time_range = [time_begin, time_end]

    this.setData({
      show: false,
      date: `${this.dealTimeToDay(start)}-${this.dealTimeToDay(time_end)}`,
      time_begin,
      time_range,
      time_end,
      allShow: true,
      page: 1,
      saleList:[]
    });

    //  查询列表
    this.salcList()
  },

  handleAll(){
    this.setData({
      show: false,
      date: '筛选',
      time_begin: 0,
      time_range:0,
      time_end:0,
      allShow: false,
      page: 1,
      saleList:[]
    });

    this.salcList()
  },


  handleChangTab(e) {
    this.setData({
      //  初始化参数
      page: 1,
      active: e.detail.name,
      saleList: []
    })
    this.activeTab(e.detail.name)
    this.salcList()
    console.log(this.data.auditStatus)
  },
  activeTab(e) {
    let status = 0
    switch (e) {
      case 'saleing':
        status = 1
        break;
      case 'passThrough':
        status = 2
        break;
      case 'noPass':
        status = 3
        break;
    }
    this.setData({
      auditStatus: status
    })
  },
  salcList() {
    if (this.data.isEnd === true) {
      return
    }
    let supplier_id = wx.getStorageSync('supplierid')
    // supplier_id = '6481ef7527b29d1c3e17c056'
    let page = this.data.page++
    let data = {
      supplier_id: supplier_id,
      time_begin: this.data.time_begin, // 申请区间
      time_end: this.data.time_end,
      audit_status: this.data.auditStatus,
      page: page,
      limit: this.data.limit
    }
    after_sale_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data.list !== null) {
          list = res.data.data.list
          list.forEach(item => {
            item.created_at_fmt = dealTimeFormat1(item.created_at)
            item.amount_fmt = dealFenToYuan(item.amount)
            item.audit_amount_fmt = dealFenToYuan(item.audit_amount)
          })
        }
        let newList = [...this.data.saleList, ...list]
        this.setData({
          saleList: newList
        })
      }
    })

  },


  seeDetail(e) {
    let order_id = e.currentTarget.dataset.info.order_id
    let product_id = e.currentTarget.dataset.info.product_id
    wx.navigateTo({
      url: '/pages/supplier/afterSale/info/index?from=supplier&id=' + product_id + '&order_id=' + order_id,
    })
  },
  onReachBottom() {
    this.salcList()
  },

})