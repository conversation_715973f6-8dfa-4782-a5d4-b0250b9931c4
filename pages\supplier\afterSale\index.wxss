page {
  background-color: #f6f6f6;
}

.nav {
  position: fixed;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.capsule-box {
  padding: 0 20rpx;
}


.refund_detail {
  margin: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 16rpx;
}

.goods_cover {
  width: 160rpx !important;
  height: 160rpx !important;
  border-radius: 8rpx;
}

.goods_detail {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.goods_content {
  width: calc(100% - 180rpx);
}

.refund_order_content view {
  font-size: 28rpx;
  color: #666666;
  margin-top: 10rpx;
}

.goods_content view:nth-child(1) {
  font-size: 30rpx;
}

.goods_content view:nth-child(2) {
  font-size: 28rpx;
  color: #666666;
  margin-top: 10rpx;
}

.goods_content view:nth-child(3) {
  font-size: 28rpx;
  color: #666666;
  margin-top: 10rpx;
}

.goods_content view:nth-child(4) {
  font-size: 28rpx;
  color: #666666;
  margin-top: 10rpx;
}



.refund_order_content view:nth-child(1) {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.sale-list {
  display: inline-flex;
  font-size: 28rpx;

  color: #666666;
  margin-top: 10rpx;
}

.sale-list .title {
  white-space: nowrap;
}

.sale-list .con {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.calendar .van-popup {
  height: 400px !important;
}

.all {
  background-color: #1989fa;
  padding: 2rpx 10rpx;
  color: #fff;
  border-radius: 10rpx;
  font-size: 28rpx;
}