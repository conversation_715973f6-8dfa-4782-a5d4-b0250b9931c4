import {
  refund_detail_for_after_sale,
} from "../../../../apis/order"
import {
  deepClone
} from "../../../../utils/util"
import {
  refund_audit
} from '../../../../apis/vip'
import {
  categoryCoverProcess,
} from '../../../../utils/dict';

import {
  get_order,
} from '../../../../apis/supplier/order';

import dayjs from "../../../../libs/dayjs"
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
const app = getApp()
Page({

  data: {
    imageUrl: app.globalData.imageUrl,
    info: {},
    categoryCoverProcess: categoryCoverProcess,
    from: '',
    show: false,
    audit_note: '',
    radio: '2',
    audit_amount: 0,
    order_id: '',
    order_time: '',
    timer: null,
    user_info:{}

  },

  onLoad(options) {
    let from = ''
    if (options.from) {
      from = options.from
    }
    let id = options.id
    this.setData({
      id: id,
      order_id: options.order_id,
      from: from
    })
    this.refundDetail(id, options.order_id)
    this.getTime()

  },
  // 获取订单时间
  getTime() {
    let data = {
      id: this.data.order_id
    }
    get_order(data).then(res => {
      if (res.data.code == 0) {
        let time = res.data.data.created_at
        this.setData({
          user_info: res.data.data,
          order_time: dayjs(time).format('YYYY-MM-DD HH:mm:ss')
        })
      }
    })
  },

  handletoSales(e) {
    let info = this.data.info
    let weight =(info.refund_weight/1000).toFixed(1)
    let per_price=  (info.price /(info.rough_weight/1000)).toFixed(1);
    this.setData({
      show: true,
      audit_note: '感谢您的反馈，农产品生鲜种植、销售、运输、确实存在一定的不良率，供应商承担超出不良率描述范围的部分进行全部售后'+weight+'-('+ weight+'kgx3%)=0*'+per_price+'=0元，感谢您的支持，祝生意兴隆！',
      radio: '2',
    })
  },

  onChange(e) {
    let id = e.detail
    this.setData({
      radio: id
    })
  },
  inputReason(e) {
    let content = e.detail.value
    this.setData({
      audit_note: content
    })
  },

  inputAmount(e) {
    let amount = e.detail.value
    this.setData({
      audit_amount_fmt: amount,
      audit_amount: parseInt(amount * 100)
    })
  },

  onClose() {
    this.setData({
      show: false,
      audit_note: '',
      radio: '2',
      audit_amount_fmt: '',
      audit_amount: 0
    })
  },
  handleConfirm() {
    let data = {
      refund_order_id: this.data.info.id,
      audit_status: parseInt(this.data.radio),
      audit_amount: this.data.audit_amount, // 审核金额
      audit_note: this.data.audit_note, // 审核备注
      auditor_type: this.data.from
    }
    if (data.audit_status === 2 && data.audit_amount <= 0) {
      Toast("审核金额不能小于0");
      return;
    }
    if (data.audit_amount / 100 > this.data.info.amount) {
      Toast("审核金额不能大于申请金额");
      return;
    }
    if (data.audit_note === "") {
      Toast("请输入审核备注");
      return;
    }
    Dialog.confirm({
        title: '审核',
        message: '确认提交？',
      })
      .then(() => {
        refund_audit(data).then(res => {
          if (res.data.code == 0) {
            Toast('已审核')
            this.setData({
              page: 1,
              list: [],
              show: false
            })
            this.refundDetail(this.data.id, this.data.order_id)
          }
        }).catch(err => {})
      })
      .catch(() => {

      });
  },


  //  查看图片
  searchImg(e) {
    let img = this.data.imageUrl + e.currentTarget.dataset.imgurl
    wx.previewImage({
      current: img, // 当前显示图片的http链接
      urls: [img] // 需要预览的图片http链接列表
    })
  },
  //查看视频
  searchVideo(e) {
    let videoUrl = this.data.imageUrl + e.currentTarget.dataset.videourl
    let sources = [{
      url: videoUrl,
      type: 'video'
    }, ]
    let index = 0
    wx.previewMedia({
      sources: sources, // 需要预览的资源列表
      current: index, // 当前显示的资源序号
      url: videoUrl // 当前预览资源的url链接
    })
  },


  refundDetail(pid, order_id) {
    let data = {
      product_id: pid,
      order_id: order_id,
    }
    refund_detail_for_after_sale(data).then(res => {
      if (res.data.code == 0) {
        let d = deepClone(res.data.data)

        //  处理数据
        d.amount = this.dealMoney(d.amount)
        d.price = this.dealMoney(d.price)
        d.supplier_audit_amount_fmt = this.dealMoney(d.supplier_audit_amount)

        let created_at = dayjs(d.created_at)

        d.created_at_desc = created_at.format('YYYY-MM-DD HH:mm:ss')
        d.updated_at_desc = dayjs(d.updated_at).format('YYYY-MM-DD HH:mm:ss')
        d.supplier_audit_at_fmt = dayjs(d.supplier_audit_at).format('YYYY-MM-DD HH:mm:ss')

        let now = dayjs()

        let target_at = created_at.add(24, 'hour')

        let created_at_count = 0

        let diff = target_at.diff(now, 'millisecond')
        if (diff > 0) {
          created_at_count = diff
        }
        d.created_at_count = created_at_count
        this.setData({
          info: d
        })

      }
    })
  },



  dealMoney(num) {
    return this.divideByHundred(num)
  },
  divideByHundred(str) {
    let floatVal = parseFloat(str);
    if (isNaN(floatVal)) {
      return 0;
    }
    floatVal = Math.round(str * 100) / 10000;
    let strVal = floatVal.toString();
    let searchVal = strVal.indexOf('.');
    if (searchVal < 0) {
      searchVal = strVal.length;
      strVal += '.';
    }
    while (strVal.length <= searchVal + 2) {
      strVal += '0';
    }
    return parseFloat(strVal);
  },


  reApply() {
    let info = this.data.info
    let data = {
      order_id: info.order_id, // 必填 订单ID
      refund_type: info.refund_type, // 必填  1:售后
      refund_reason_type: info.refund_reason_type, // 必填 1 少/缺货 2商品质量问题 3实物与商品描述不符
      reason: info.reason, // 必填 原因
      amount: info.amount, // 金额，不退运费
      product_id: info.product_id, // 商品ID
      refund_num: info.num, // 必填 退款数量，>=1
      refund_weight: info.refund_weight, //  退款重量 
      image_list: info.image_list, // 图片列表
      video: info.video // 视频
    }

    let content = JSON.stringify(data)
    let infos = JSON.stringify(this.data.info)
    wx.navigateTo({
      url: '/pages/center/order/refundRequest/index?content=' + content + '&infos=' + infos,
    })
  },

  toProductDetail() {
    //  商品详情
    let id = this.data.info.product_id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  },

  toOrderDetail(e) {
    let id = this.data.info.order_id

    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id + "&from=afterSale",
    })
  },

  onReady() {},

  onShow() {},

  onHide() {},

  onUnload() {},

  onPullDownRefresh() {},

  onReachBottom() {},
})