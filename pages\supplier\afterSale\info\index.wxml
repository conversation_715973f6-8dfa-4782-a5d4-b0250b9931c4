<wxs src="../../../../utils/tool.wxs" module="tool" />
<view class="container">
  <view class="user">
    <view>名称：{{user_info.address.contact.name}}</view>
    <view>电话：{{user_info.address.contact.mobile}}</view>
    <view>地址：{{user_info.address.location.address}}{{user_info.address.location.name}}</view>
  </view>
  <view class="goods_detail">
    <view class="goods_cover">
      <image class="goods_cover" src="{{info.product_cover.name?imageUrl +categoryCoverProcess+ info.product_cover.name:''}}" mode="" />
    </view>
    <view class="goods_content" bind:tap="toProductDetail">
      <view class="goods_title">{{info.product_title}}</view>
      <view style="font-size: 26rpx; margin-top: 10rpx;">{{info.sku_name}}</view>
      <view class="goods_amount_price">
        <view class="goods_price">￥{{info.price}}</view>
        <view class="goods_amount">x{{info.num}}</view>
      </view>
    </view>
  </view>

  <view class="goods_param">
    <view style="display: flex;justify-content: space-between; padding: 0 30rpx;font-size: 26rpx;">
      <text>售后类型</text>
      <text style="color: #979696;" wx:if="{{info.refund_reason_type == 1}}">商品重量不足</text>
      <text style="color: #979696;" wx:if="{{info.refund_reason_type == 2}}">商品质量问题</text>
      <text style="color: #979696;" wx:if="{{info.refund_reason_type == 3}}">商品面描述不符</text>
      <text style="color: #979696;" wx:if="{{info.refund_reason_type == 4}}">商品缺货</text>
      <text style="color: #979696;" wx:if="{{info.refund_reason_type == 5}}">规格不符</text>
      <text style="color: #979696;" wx:if="{{info.refund_reason_type == 6}}">其他</text>
    </view>



    <van-cell title="申请重量" value="{{tool.toFixedOne(info.refund_weight / 1000)}}" border="{{ false }}" />
    <van-cell title="申请金额" value="{{info.amount}}" border="{{ false }}" />
    <view class="refund_filling_content">
      <view style="color: #000;">
        <van-field value="{{ info.reason }}" readonly autosize label="售后原因" input-align="right" type="textarea" bind:change="reason" border="{{ false }}" />
      </view>
    </view>
    <van-cell title="订单时间" value="{{order_time}}" border="{{ false }}" />

    <van-cell title="申请时间" value="{{info.created_at_desc}}" border="{{ false }}" />
    <van-cell title="订单查看" is-link border="{{ false }}" bind:tap="toOrderDetail" />
    <van-cell title="售后状态" border="{{ false }}">
      <view wx:if="{{info.is_withdraw}}">
        <van-tag plain type="primary">已撤销</van-tag>
      </view>
      <view wx:else>
        <van-tag plain wx:if="{{info.audit_status==1}}" type="warning" border="{{ false }}">售后中</van-tag>
        <van-tag plain wx:if="{{info.audit_status==2}}" type="success" border="{{ false }}">已通过</van-tag>
        <van-tag plain wx:if="{{info.audit_status==3}}" type="danger" border="{{ false }}">售后不通过</van-tag>
      </view>
    </van-cell>
  </view>
  <van-divider contentPosition="center" customStyle="color: #1989fa; border-color: #1989fa; font-size: 18px;">售后信息</van-divider>
  <!-- <view style="border-top: 1rpx solid #c5c4c4;margin: 20rpx 0;"></view> -->
  <van-cell title="损坏情况图" border="{{ false }}" />
  <view style="display: flex;flex-wrap: wrap;">
    <view wx:for="{{info.image_list}}" wx:key="key">
      <image class="image-class" src="{{item.name?imageUrl+item.name:''}}" data-imgUrl="{{item.name}}" bindtap="searchImg" mode="aspectFill" />
    </view>
  </view>

  <van-cell title="耗损重量图" border="{{ false }}" />
  <view wx:if="{{info.image_list_two.length > 0}}">
    <view wx:for="{{info.image_list_two}}" wx:key="key">
      <image class="image-class" src="{{item.name?imageUrl+item.name:''}}" data-imgUrl="{{item.name}}" bindtap="searchImg" mode="aspectFill" />
    </view>
  </view>
  <view wx:else style="margin-left: 20rpx;">无</view>

  <van-cell title="包装及面单" border="{{ false }}" />
  <view wx:if="{{info.image_list_one.length > 0}}">
    <view wx:for="{{info.image_list_one}}" wx:key="key">
      <image class="image-class" src="{{item.name?imageUrl+item.name:''}}" data-imgUrl="{{item.name}}" bindtap="searchImg" mode="aspectFill" />
    </view>
  </view>
  <view wx:else style="margin-left: 20rpx;">无</view>



  <view>
    <van-cell title="视频" border="{{ false }}" />
    <video enable-play-gesture="{{true}}" class="image-class" object-fit="cover" show-mute-btn="{{true}}" show-center-play-btn="{{true}}" src="{{info.video.name?imageUrl+info.video.name:''}}" data-videoUrl="{{info.video.name}}" bindtap="searchVideo" show-play-btn="{{true}}" auto-pause-if-open-native="{{true}}"></video>
  </view>




  <view >
    <!-- 审核信息 -->
    <view>
      <van-divider contentPosition="center" customStyle="color: #1989fa; border-color: #1989fa; font-size: 18px;">
        售后记录
      </van-divider>
    </view>

    <view class="apply-audit" wx:if="{{info.audit_objection}}">
      <view style="margin-bottom: 10rpx;font-weight: bold;">[供应商]售后信息</view>

      <view class="audit-class">
        <view style="white-space: nowrap;">售后内容：</view>
        <view>{{info.supplier_audit_content}}</view>
      </view>

      <view class="audit-class">
        <view style="white-space: nowrap;">售后金额：</view>
        <view style="color: #5dd797;">{{info.supplier_audit_amount_fmt}}</view>
      </view>

      <view class="audit-class">
        <view style="white-space: nowrap;">回复时间：</view>
        <view>{{info.supplier_audit_at_fmt}}</view>
      </view>
    </view>

    <view class="apply-audit" wx:if="{{info.audit_objection}}">
      <view style="margin-bottom: 10rpx;font-weight: bold;">[客户]申诉信息</view>
      <view class="audit-class">
        <view style="white-space: nowrap;">申诉内容：</view>
        <view>{{info.audit_objection}}</view>
      </view>
    </view>

    <view class="apply-audit" wx:if="{{info.audit_status!=1}}">
      <view style="margin-bottom: 10rpx;font-weight: bold;">[平台]售后信息</view>
      <view class="audit-class" wx:if="{{info.audit_status!=1}}">
        <view style="white-space: nowrap;">售后留言：</view>
        <view>{{info.audit_note}}</view>
      </view>
      <view class="audit-class" wx:if="{{info.audit_status==2}}">
        <view style="white-space: nowrap;">售后金额：</view>
        <view style="color: #5dd797;">{{tool.toFixedTwo(info.audit_amount / 100)}}</view>
      </view>

      <view class="audit-class" wx:if="{{info.audit_status==2&&info.pay_method == 'balance'}}">
        <view style="white-space: nowrap;">交易流水号：</view>
        <view>{{info.refund_result.pay_interface_out_trade_no}}</view>
      </view>

      <view class="audit-class" wx:if="{{info.audit_status==2&&info.pay_method == 'wechat'}}">
        <view style="white-space: nowrap;">交易流水号：</view>
        <view>{{info.wx_cancel_result.transaction_id}}</view>
      </view>

      <view class="audit-class" wx:if="{{info.audit_status!=1}}">
        <view style="white-space: nowrap;">回复时间：</view>
        <view>{{info.updated_at_desc}}</view>
      </view>

    </view>

  </view>

  <view class="waite" wx:if="{{from=='supplier'&&info.audit_status=='1'}}">
    <!--  供应商进入 -->
    <view class="sales" bind:tap="handletoSales" wx:if="{{from == info.auditor_type}}">去售后</view>
    <view style="display: flex;justify-content: center;align-items: center;" wx:if="{{info.created_at_count >0 && info.auditor_type=='supplier'}}">
      <view style="font-size: 26rpx;">剩余时间：</view>
      <van-count-down format="HH:mm:ss" time="{{ info.created_at_count }}" />
    </view>
    <view wx:if="{{info.auditor_type=='platform'}}">平台审核中</view>
  </view>

  <view class="waite" wx:if="{{from=='platform'&&info.audit_status=='1'}}">
    <view style="margin-bottom: 10rpx;" wx:if="{{from !== info.auditor_type}}">等待供应商售后</view>
    <view class="sales" bind:tap="handletoSales" wx:if="{{from == info.auditor_type}}">去售后</view>
    <view style="display: flex;justify-content: center;align-items: center;gap: 10rpx;" wx:if="{{info.created_at_count >0&& info.auditor_type=='supplier'}}">

      <image src="/static/point/time.png" style="width: 50rpx;height: auto;" mode="widthFix" />
      <view>
        <van-count-down format="HH:mm:ss" time="{{ info.created_at_count }}" />
      </view>

    </view>
  </view>

  <view style="display: flex;padding-bottom: 200rpx;"></view>

</view>
<van-toast id="van-toast" style="position: fixed;top: 50%;z-index: 9999;width: 100%;height: 50rpx;" />
<van-dialog id="van-dialog" style="position: fixed;top: 20rpx;z-index: 10000;width: 100%;height:100rpx;" />

<van-dialog use-slot title="审核" show="{{ show }}" showConfirmButton="{{false}}">
  <view style="padding: 20rpx;font-size: 28rpx;">
    <view style="display: flex;gap: 10rpx;">
      <text style="white-space: nowrap;">商品标题：</text>
      <view>{{info.product_title}}</view>
    </view>
    <view style="display: flex;gap: 10rpx;align-items: center;margin: 10rpx 0;">
      <text>申请金额:</text>
      <input value="{{info.amount}}" disabled style="border: 1px solid #eee;text-align: center;padding: 6rpx 0;" />
    </view>

    <view style="display: flex;gap: 10rpx;align-items: center;">
      <text>审核金额:</text>
      <input value="{{audit_amount_fmt}}" type="digit" style="border: 1px solid #eee;text-align: center;padding: 6rpx 0;" bindinput="inputAmount" />
    </view>

    <view style="margin: 20rpx 0;display: flex;align-items: center;">
      <view>状态：</view>
      <van-radio-group value="{{ radio }}" bind:change="onChange">
        <view style="display: flex;align-items: center;gap: 20rpx;">
          <van-radio name="2">通过</van-radio>
          <van-radio name="3">不通过</van-radio>
        </view>
      </van-radio-group>
    </view>
    <view>
      <view style="white-space: nowrap;margin-bottom: 10rpx;">备注：</view>
      <textarea placeholder="请输入" value="{{audit_note}}" auto-focus class="text-content" bindinput="inputReason" />
    </view>

    <view style="display: flex;align-items: center;justify-content: space-around;margin-top: 20rpx;">
      <view style="width: 30%;" class="cancle" bind:tap="onClose">取消</view>
      <view style="width: 30%;" class="sure" bind:tap="handleConfirm">确认</view>
    </view>
  </view>
</van-dialog>