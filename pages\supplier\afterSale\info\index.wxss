.goods_detail {
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}
.goods_cover {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
}

.user{
  background-color: #fff;
  padding: 20rpx;
  margin: 20rpx;
  border: 1px solid #eee;
  border-radius: 10rpx;
  font-size: 26rpx;
}

.van-cell{
  padding: 4rpx 26rpx !important;
  font-size: 26rpx !important;
  color: #000 !important;
}

.van-field__label{
  color: #000 !important;
}
.goods_title{
  font-size: 26rpx;
  font-weight: bold;
}
.goods_content {
  flex: 1;
  margin-left: 20rpx;
}
.goods_amount_price {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}
.goods_param{
  font-size: 28rpx;
}
.order{
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
}
.sales{
  border-radius: 40rpx;
  background-color: #f35353;
  color: #fff;
  text-align: center;
  padding: 20rpx 0;
  margin: 20rpx 50rpx;
  font-weight: bold;
}
.text-content {
  border: 1rpx solid #ececec;
  padding: 10rpx;
  box-sizing: border-box;
  height: 220rpx;
  line-height: 40rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #4a4a4b;
}
.cancle {
  width: 30%;
  border: 1rpx solid #eee;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}
.sure {
  width: 40%;
  border: 1rpx solid #eee;
  background-color: #38abf8;
  color: #fff;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}
.image-class {
  width: 150rpx;
  height: 150rpx;
  margin-left: 20rpx;
  border-radius: 16rpx;
}

.waite{
  margin-left: 30rpx;
  color: #f06363;
  text-align: center;
  margin-top: 20px;
}


.apply-audit {
  border: 1px solid #e2e1e1;
  margin: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  box-shadow: 1px 1px 10px 0px #e2e1e1;
}

.audit-class {
  font-size: 26rpx;
  display: flex;
  gap: 10rpx;
  margin-bottom: 6rpx;
}