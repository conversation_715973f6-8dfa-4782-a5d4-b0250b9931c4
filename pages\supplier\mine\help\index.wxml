<view>
  <van-tabs active="{{ active }}" bind:change="onChangeActive">
    <van-tab title="售后率" name="after">
      <view class="container">
        <view class="title">售后率</view>
        <view class="content">
          <view>1. 最近30天的售后单数 / 最近30天的总订单数</view>
        </view>
      </view>
    </van-tab>
    <van-tab title="待结算" name="agentPay">
      <view class="container">
        <view class="title">待结算记录</view>
        <view class="content">
          <view>1. 待结算记录的金额 = 订单商品总额 - 已品控退款的商品金额 - 售后的商品金额</view>
        </view>
      </view>
    </van-tab>

  </van-tabs>

</view>