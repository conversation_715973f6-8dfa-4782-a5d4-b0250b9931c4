const app = getApp()
import {
  pay_not_amount,
  merchant_balance_supplier,
  coupon_supplier,
  profit_supplier,
  profit_list
} from '../../../apis/servicePoint/center';

import {
  supplierStats,
  get_supplier_search
} from '../../../apis/supplier/center';

import {
  supplierIDKey,
  userIDKey
} from '../../../utils/dict';

import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';

import regeneratorRuntime from 'regenerator-runtime'
import dayjs from '../../../libs/dayjs';

Page({

  data: {
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    imageUrl: app.globalData.imageUrl,
    supplierDetail: {},
    supplier_name: '',
    balance: 0,
    balanceShow: 0,
    balance_next: 0,
    funcList: [{
      id: 3,
      name: "评论管理",
      icon: "icon/comment.png"
    },

    ],

    funcListTwo: [{
      id: 2,
      name: "切换角色",
      icon: "icon/role.png"
    },
    {
      id: 21,
      name: "帮助",
      icon: "icon/helps1.png"
    },
    {
      id: 3,
      name: "密码",
      icon: "icon/pwd.png"
    },
    {
      id: 1,
      name: "设置",
      icon: "icon/setup.png"
    },
    ],
    to_settle_amount: 0,
    frozen_balance_amount: 0,
    profit_show: false,
    currentDate: dayjs().valueOf(),
    profitDate: dayjs().valueOf(),
    minDate: dayjs().subtract(4, "month").startOf('month').valueOf(),
    maxDate: dayjs().valueOf(),
    discount_price: '￥0',
    profit_text: "查看",
    profit_info: null,
    profit_list_data: [],
  },

  async onLoad(options) {
    this.queryStats()
    this.supplierDetail()
    this.queryToSettleAmount()
    this.querySupplier()
  },



  // 利润查看
  handlepProfitTimeShow() {
    this.setData({
      profit_show: true
    })
  },
  onCloseProfit() {
    this.setData({
      profit_show: false
    })
  },
  confirmProfit(e) {
    this.setData({
      profitDate: e.detail,
      currentDate: e.detail,
      profit_text: dayjs(e.detail).format("YYYY年M月")
    })
    this.profitSupplier()
    this.couponSupplier()
  },
  cancelProfit() {
    this.setData({
      profit_show: false
    })
  },

  // 利润结算记录
  getProfitList() {
    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
      month_timestamp: this.data.profitDate
    }
    profit_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if(!list){
          list  = []
        }
        list.forEach(item=>{
          item.amount_fmt = this.dealMoney(item.amount)
          item.created_at_fmt = dayjs(item.created_at).format("YYYY-MM-DD")
        })

        this.setData({
          profit_list_data: list
        })
      }

    })
  },

  // 补贴接口
  couponSupplier() {
    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
      month_time_stamp: this.data.currentDate
    }
    coupon_supplier(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          discount_price: "￥" + this.dealMoney(res.data.data.amount),
          profit_show: false
        })
      }
    }).catch(err => {
      Toast(err.data.message)
    })
  },

  dealMoney(fen) {
    return Math.round(fen) / 100
  },
  // 利润
  profitSupplier() {
    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
      month_timestamp: this.data.profitDate
    }
    profit_supplier(data).then(res => {
      if (res.data.code == 0) {
        let info = res.data.data
        info.total_product_amount_fmt = this.dealMoney(info.total_product_amount) //总商品金额
        info.total_after_sale_refund_amount_fmt = this.dealMoney(info.total_after_sale_refund_amount) //总售后金额
        info.total_debt_amount_fmt = this.dealMoney(info.total_debt_amount) //总补差金额
        info.total_profit_amount_fmt = this.dealMoney(info.total_profit_amount) //总利润
        info.total_product_buy_price_amount_fmt = this.dealMoney(info.total_product_buy_price_amount) //总采购成本
        info.total_quality_refund_amount_fmt = this.dealMoney(info.total_quality_refund_amount) //总品控退款
        this.setData({
          profit_info: res.data.data,
          profit_show: false
        })
        this.getProfitList()
      }
    }).catch(err => {
      Toast(err.data.message)
    })
  },

  // 冻结额
  querySupplier() {
    let data = {
      user_id: wx.getStorageSync(userIDKey)
    }
    get_supplier_search(data).then(res => {
      if (res.data.code == 0) {
        let info = res.data.data
        this.setData({
          frozen_balance_amount: this.dealMoney(info.frozen_balance_amount)
        })
      }
    })
  },

  queryToSettleAmount() {
    let data = {
      supplier_id: wx.getStorageSync('supplierid')
    }
    pay_not_amount(data).then(res => {
      if (res.data.code == 0) {
        let d = res.data.data
        let amount = this.dealMoney(d)

        this.setData({
          to_settle_amount: amount,
        })
      }
    }).catch(err => {
      Toast(err.data.message)
    })
  },

  dealMoney(fen) {
    return Math.round(fen) / 100
  },


  //供应商详情
  supplierDetail() {
    let data = {
      supplier_id: wx.getStorageSync(supplierIDKey)
    }
    merchant_balance_supplier(data).then(res => {
      if (res.data.code == 0) {
        //  循环
        let accountInfoList = res.data.data.accountInfoList
        accountInfoList.forEach(ele => {
          if (ele.accountType == 'FUND_ACCOUNT') {
            this.setData({
              balance: ele.balance
            })
          }

          if (ele.accountType == 'SETTLE_ACCOUNT') {
            this.setData({
              balance_next: ele.balance
            })
          }
        })
      }
    }).catch(err => {
      if (err.data.code == 4001) {
        this.setData({
          balance: '0.00'
        })
      }
    })
  },

  commonFunction(e) {
    let id = e.currentTarget.dataset.id
    switch (id) {
      case 3:
        wx.navigateTo({
          url: '/packageComment/commentManage/index',
        })
        break;
    }
  },
  commonFunctionTwo(e) {
    let id = e.currentTarget.dataset.id
    let supplierDetail = JSON.stringify(this.data.supplierDetail)
    switch (id) {
      case 1:
        wx.navigateTo({
          url: '/pages/supplier/mine/setup/index?id=' + supplierDetail.id
        })
        break;
      case 2:
        wx.reLaunch({
          url: '/pages/index/index',
        })
        break;
      case 3:
        wx.navigateTo({
          url: '/pages/supplier/password/index',
        })
        break;
      case 21:
        wx.navigateTo({
          url: '/pages/supplier/mine/help/index',
        })
        break;

    }
  },

  // 去采购总览
  jumpProcure() {
    wx.navigateTo({
      url: '/pages/procure/index'
    })

  },

  // 去订单调价
  jumpSettle() {
    wx.navigateTo({
      url: '/pages/supplier/mine/orderSettle/index'
    })

  },

  //跳转钱包
  jumpWallet() {
    wx.navigateTo({
      url: '/pages/supplier/mine/wallet/index?from=supplier',
    })
  },

  //  统计
  queryStats() {
    let supplier_id = wx.getStorageSync('supplierid')
    let data = {
      supplier_id: supplier_id
    }
    supplierStats(data).then(res => {
      if (res.data.code === 0) {
        let stats = res.data.data
        this.setData({
          stats
        })
      }
    })
  },

  handleToSettlement() {
    wx.navigateTo({
      url: '/pages/supplier/mine/settlement/index',
    })
  },

  onShow() {

  },

})