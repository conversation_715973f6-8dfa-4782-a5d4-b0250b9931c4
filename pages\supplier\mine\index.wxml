<view class="nav" style="height:{{navBarHeight}}px;z-index: 999;">
  <view style="position: relative;text-align: center;height: {{menuHeight}}px;top:{{navBarHeight-menuHeight}}px;">
    个人中心
  </view>
</view>

<view class="container" style="position: relative;">
  <view>
    <image class="bg-img" src="{{imageUrl+'/icon/bg.jpg'}}" mode="widthFix" />
    <view class="base_info">
      <view class="store_cover" bindtap="jumpSetup">
        <image class="store_cover" wx:if="{{supplierDetail.avatar_img.name}}" src="{{supplierDetail.avatar_img.name?imageUrl + supplierDetail.avatar_img.name:''}}" mode="aspectFill" />
      </view>
      <view style="margin-left: 30rpx;margin-top: 10rpx;" class="">
        <view>
          {{stats.supplier_simple_name}}
        </view>
        <view class="after-sale">
          <text class="title"> 售后率:</text>
          <text class="content">{{stats.after_sale_rate}}%</text>
        </view>
      </view>
    </view>

    <view class="amount_module_wrap">
      <view class="amount_module">
        <view class="amount_module_list" bindtap="jumpWallet">
          <view class="title">余额</view>
          <view class="amount">{{balance}}</view>
          <view style="display: flex;gap: 10rpx;font-size: 22rpx;color: #8a8a8a;">
            <view>冻结额</view>
            <view>{{frozen_balance_amount}}</view>
          </view>
        </view>

        <view class="amount_module_list" bind:tap="handleToSettlement">
          <view class="title">待结算</view>
          <view class="amount">{{to_settle_amount}}</view>
          <view style="display: flex;gap: 10rpx;font-size: 22rpx;color: #8a8a8a;">
            <view>次日到账</view>
            <view>{{balance_next}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>




  <view class="function_list" style="margin-top:90rpx">
    <view class="common_function_wrap">


      <view class="profit">
        <view style="display: flex;justify-content: space-between;">
          <view>经营信息</view>
          <view bind:tap="handlepProfitTimeShow">{{profit_text}} ></view>
        </view>

        <view wx:if="{{profit_info}}" style="display: flex;flex-direction: column;justify-content: center;">
          <view style="display: flex;margin-top: 30rpx;justify-content: space-between;">
            <view class="profit-item">
              <view>采购</view>
              <view>{{profit_info.total_product_buy_price_amount_fmt}}</view>
            </view>
            <view class="profit-item">
              <view>销售</view>
              <view>{{profit_info.total_product_amount_fmt}}</view>
            </view>
            <view class="profit-item">
              <view>利润</view>
              <view style="color: {{profit_info.product_profit_amount > 0? 'green': 'red'}};">{{profit_info.total_profit_amount_fmt}}</view>
            </view>
          </view>

          <view style="display: flex;margin-top: 30rpx;justify-content: space-between;">
            <view class="profit-item">
              <view>品控</view>
              <view>{{profit_info.total_quality_refund_amount_fmt}}</view>
            </view>
            <view class="profit-item">
              <view>补差</view>
              <view>{{profit_info.total_debt_amount_fmt}}</view>
            </view>
            <view class="profit-item">
              <view>售后</view>
              <view>{{profit_info.total_after_sale_refund_amount_fmt}}</view>
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{profit_info}}">
        <view class="profit-list" wx:if="{{profit_list_data.length > 0}}">
          <view>利润结算（{{profit_text}}）</view>
          <view wx:for="{{profit_list_data}}" wx:key="index" style="margin-top: 10rpx;">
            <view style="display: flex;align-items: center;justify-content: space-between;">
              <view>金额：￥{{item.amount_fmt}}</view>
              <view>时间：{{item.created_at_fmt}}</view>
            </view>
            <view style="margin-top: 10rpx;">备注：{{item.remark}}</view>
          </view>
        </view>
        <view wx:else class="profit-list">
          暂无结算信息
        </view>
      </view>

      <view class="discount" wx:if="{{profit_info}}">
        <view>优惠券金额：</view>
        <view bind:tap="handleDiscountTimeShow">{{discount_price}}</view>
      </view>

      <view class="common_function">
        <view class="common_function_list_wrap">
          <view class="common_function_list">
            <view class="common_function_model" bindtap="jumpProcure">
              <image src="/static/point/procure.png" mode="widthFix" class="func_icon" />
              <view class="func_name">经营情况</view>
            </view>
          </view>

          <view class="common_function_list">
            <view class="common_function_model" bindtap="jumpSettle">
              <image src="/static/point/settle.png" mode="widthFix" class="func_icon" />
              <view class="func_name">订单调价</view>
            </view>
          </view>
        </view>
      </view>

      <view class="common_function">
        <view class="common_function_list_wrap">
          <view class="common_function_list" wx:for="{{funcListTwo}}" wx:key="key" bindtap="commonFunctionTwo" data-id="{{item.id}}">
            <view class="common_function_model">
              <view class="icon">
                <image src="{{imageUrl + item.icon}}" mode="widthFix" class="func_icon" />
                <view class="mark" wx:if="{{item.id===3&&tabbar_tip.mine===true}}"></view>
              </view>
              <view class="func_name">{{item.name}}</view>
            </view>
          </view>
        </view>
      </view>



    </view>

  </view>
</view>



<van-toast id="van-toast" />
<van-dialog id="van-dialog" />


<van-action-sheet show="{{ profit_show }}" title="时间" bind:close="onCloseProfit">
  <view>
    <van-datetime-picker type="year-month" value="{{ profitDate }}" min-date="{{ minDate }}" max-date="{{ maxDate }}" bind:confirm="confirmProfit" bind:cancel="cancelProfit" />
  </view>
</van-action-sheet>