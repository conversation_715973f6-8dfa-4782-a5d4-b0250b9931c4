Page{
  background-color: #f6f6f6;
}

.bg-img {
  width: 100%;
  height: auto;
  position: absolute;
  z-index: -1;
}

.base_info {
  display: flex;
  align-items: flex-start;
  padding: 50rpx;
  box-sizing: border-box;
  z-index: 1;
}

.after-sale .title {
  font-size: 24rpx;
  color: #fff;
  margin-right: 10rpx;
}

.after-sale .content {
  font-size: 24rpx;
  color: #fff;
}

.store_cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.amount_module_wrap {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.amount_module {
  width: 100%;
  height: 190rpx;
  background-color: #fff;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  border-radius: 16rpx;
  position: relative;
  bottom: -70rpx;
  padding: 30rpx 0;
}

.amount_module_list {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 10rpx;
}

.amount_module_list .title {
 font-size: 32rpx;
}

.amount_module_list .amount {
  font-size: 40rpx;
 }

.common_function_wrap {
  padding: 20rpx;
  box-sizing: border-box;
}

.division_line {
  width: 100%;
  height: 2rpx;
  background-color: #f6f6f6;
  margin-top: 16rpx;
}

.order_class,
.common_function {
  padding: 20rpx 20rpx 40rpx 20rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  border-radius: 20rpx;
  margin-bottom: 20rpx;

}

.order_class_list_wrap {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order_class_model_icon {
  width: 50rpx;
  height: 50rpx;
}


.title_font {
  margin-bottom: 20rpx;
}


.order_class_model {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.mark {
  width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  background-color: red;
  box-sizing: border-box;
  color: #ffffff;
  font-size: 22rpx;
  position: absolute;
  border-radius: 50%;
  right: 5rpx;
  top: 0;
}

.order_class_name {
  margin-top: 20rpx;
}



.func_name {
  font-size: 22rpx;
  margin-top: 20rpx;
}

.common_function_list_wrap {
  margin-top: 10rpx;
  display: flex;
  flex-wrap: wrap;

}

.common_function_list {
  width: 100%;
  width: calc(100% / 5);
}

.common_function_model {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon {
  box-sizing: border-box;
  display: flex;
  position: relative;
}

.func_icon {
  width: 50rpx;
  height: auto;
}

.mark {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  background-color: red;
  border-radius: 50%;
  top: 0;
  right: 0;
  z-index: 10;
}

.discount{
  background-color: #fff;
  padding:20rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  display: flex;
  justify-content: space-between;
}

.profit{
  background-color: #fff;
  padding:20rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}

.profit-item{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  width: 130rpx;
}
.profit-list{
  background-color: #fff;
  padding:20rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}