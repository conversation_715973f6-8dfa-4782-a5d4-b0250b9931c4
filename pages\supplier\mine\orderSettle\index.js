const app = getApp()

import {
  supplierIDKey,
} from '../../../../utils/dict';

import {
  adjust_settle_list
} from '../../../../apis/servicePoint/center';
import {
  dealTime,
} from '../../../../utils/check';
Page({

  data: {
    page: 0,
    is_end: false,
    imgUrl: app.globalData.imageUrl,
    list: [],
    color: ''
  },

  onLoad(options) {
    this.getSettleList()
  },

  getSettleList() {
    if (this.data.is_end) return
    this.data.page++
    let data = {
      page: this.data.page,
      limit: 4,
      supplier_id: wx.getStorageSync(supplierIDKey)
    }
    adjust_settle_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list || []
        list.forEach(ele => {
          ele.status_text = this.statusText(ele.status)
          ele.total_amount_fmt = (ele.total_amount / 100).toFixed(2)
          ele.updated_at_fmt = dealTime(ele.updated_at)
          ele.product_list.forEach(item => {
            item.order_amount_fmt = (item.order_amount / 100).toFixed(2)
            item.adjust_amount_fmt = (item.adjust_amount / 100).toFixed(2)
          })
        });
        let new_list = [...this.data.list, ...list]
        this.setData({
          list: new_list,
          is_end: new_list.length >= res.data.data.count
        })
      }
    }).catch(err => {
      wx.showToast({
        title: err.message,
        icon: 'none'
      })
    })
  },

  statusText(e) {
    let text = ''
    let color = ''
    switch (e) {
      case 'draft':
        text = '草稿'
        color = "#626063"
        break;
      case 'confirmed':
        text = '已确认'
        color = "#b8c43a"
        break;
      case 'refunding':
        text = '退款中'
        color = "#b55233"
        break;
      case 'completed':
        text = '已完成'
        color = "#2bb232"
        break;
      case 'failed':
        text = '退款失败'
        color = "#e8373d"
        break;
      case 'closed':
        text = '已关闭'
        color = "#a29779"
        break;
    }
    this.setData({
      color: color
    })
    return text
  },

  jumpDetaiils(e) {
    let id = e.currentTarget.dataset.info.order_id
    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id,
    })
  },

  onPullDownRefresh() {

  },

  onReachBottom() {
    if (!this.data.is_end) {
      this.getSettleList()
    }
  },

  onShareAppMessage() {

  }
})