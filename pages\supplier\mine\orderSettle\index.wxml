<view class="container">
  <view wx:for="{{list}}" wx:key="index" class="list" data-info="{{item}}" bind:tap="jumpDetaiils" >
    <view style="display: flex;justify-content: space-between;align-items: center;">
      <view>{{item.supplier_name}}</view>
      <view class="tag" style="background-color: {{color}};">{{item.status_text}}</view>
    </view>


    <view class="item">
      <view wx:for="{{item.product_list}}" wx:key="index" wx:for-item="ele" style="display: flex;gap: 10rpx;">
        <view style="white-space: nowrap;">
          <image src="{{imgUrl+ele.product_cover_img}}" mode="widthFix" style="width: 150rpx;height: auto;border-radius: 10rpx;" />
        </view>
        <view style="display: flex;flex-direction: column;gap: 10rpx;">
          <view style="font-weight: bold;">{{ele.product_title}}</view>
          <view style="color: orange;">{{ele.sku_name}}</view>
          <view style="display: flex;gap: 20rpx;font-size: 24rpx;">
            <view style="color: red;">调价：{{ele.adjust_amount_fmt}}</view>
            <view style="color: red;">订单价：{{ele.order_amount_fmt}}</view>
          </view>
          <view style="color: #949393;font-size: 24rpx;">备注：{{ele.adjust_remark}}</view>
        </view>
      </view>
    </view>
    <view style="font-size: 24rpx;">调价总价：{{item.total_amount_fmt}}</view>
    <view style="font-size: 24rpx;color: #949393;margin: 10rpx 0;">调价备注：{{item.remark}}</view>
    <view style="font-size: 24rpx;color: #949393;">调价时间：{{item.updated_at_fmt}}</view>
  </view>

</view>