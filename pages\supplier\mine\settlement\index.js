// pages/supplier/center/settlement/index.js
import {
  pay_not_amount_list
} from '../../../../apis/servicePoint/center';
import {
  dealTimeFormat1,
  dealTimeFormatDay
} from '../../../../utils/dict';
const app = getApp()
Page({

  data: {
    isEnd: false,
    page: 1,
    list: [],
    imageUrl: app.globalData.imageUrl,
    navHeights: app.globalData.navHeight * 2, //导航栏总高度
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    num: 0
  },

  onLoad(options) {
    console.log(options.payNotAmount)
    this.setData({
      num: options.payNotAmount
    })
    this.amountList()
  },
  amountList() {
    let page = this.data.page++
    if (this.data.isEnd == true) {
      return
    }

    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
      page: page,
      limit: 30
    }
    pay_not_amount_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list ) {
          list= []
        }
        
        list.forEach(item => {
          item.creat_at_fmt = dealTimeFormatDay(item.order.created_at)
          item.stock_up_day_time_fmt = dealTimeFormat1(item.stock_up_day_time)
          if (item.order.order_status_record.receive_time !== 0) {
            item.receive_time_fmt = dealTimeFormatDay(item.order.order_status_record.receive_time)
          }

          item.amount_fmt = item.amount / 100
          item.order.product_list.forEach(items => {
            items.prices = items.product_amount / 100
          })
        })

        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
        })
      }
    })
  },

  toOrderDetail(e) {
    let id = e.currentTarget.dataset.info.order.id
    // let id = e.currentTarget.dataset.info.id
    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id,
    })
  },

  
  onReady() {

  },

  onShow() {

  },

  onHide() {

  },

  onUnload() {

  },

  onPullDownRefresh() {

  },

  onReachBottom() {
    this.amountList()
  },

  onShareAppMessage() {

  }
})