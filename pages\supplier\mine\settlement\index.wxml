<view class="container">
  <view>
    <view class="lists" wx:if="{{list.length>0}}">
      <view wx:for="{{list}}" wx:key="id" class="listInfo" data-info="{{item}}" bind:tap="toOrderDetail">
        <view class="top">
          <view style="margin-bottom: 10rpx;font-size: 50rpx;">
            {{item.amount_fmt}}
          </view>
          <view style="font-size: 26rpx;display: flex;gap: 20rpx;align-items: center;">
            <view>
              {{item.order.buyer_name}}
            </view>
            <view>
              <van-tag wx:if="{{item.exist_after_sale}}" type="danger">售后中</van-tag>
            </view>
          </view>
        </view>
        <view style="display: flex;gap: 20rpx;margin-top: 10rpx;">
          <view style="color: #c0c0bc; font-size: 26rpx; margin-right: 10rpx;">
            下单时间：{{item.creat_at_fmt}}
          </view>
          <view style="color: #c0c0bc; font-size: 26rpx;"  wx:if="{{item.order.order_status_record.receive_time!==0}}">
            收货时间：{{item.receive_time_fmt}}
          </view>
        </view>
      </view>
    </view>
  </view>
  <view wx:if="{{list.length==0}}" style="margin-top: 100rpx;">
    <van-empty description="暂无数据" />
  </view>
  <view style="height:140rpx; ">
  </view>
</view>