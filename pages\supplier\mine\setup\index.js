// pages/supplier/center/setup/index.js
const app = getApp()
import {
  send_code,
  bind_phone
} from '../../../../apis/servicePoint/center'

import {
  userIDKey
} from '../../../../utils/dict';

import {
  get_supplier_search,
} from '../../../../apis/supplier/center';

import {
  upload_sign,
} from '../../../../utils/api';
const uploadFile = require('../../../../utils/uploadFile');
const util = require('../../../../utils/util');
import {
  update_avatar
} from '../../../../apis/supplier/center';
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import {
  dealTime
} from '../../../../utils/check';

Page({
  data: {
    formData: {
      mobile: "",
      captcha: "",
      created_at_fmt: ''
    },
    payPhone: 0,
    currentTime: "获取验证码", //倒计时
    imgUrl: app.globalData.imageUrl,
    fileList: [],
    supplier_detail: {}, // 
  },

  onLoad(options) {
    this.querySupplier()
  },


  //更换头像
  choosePortrait(e) {
    let data = {
      avatar_img: e.detail
    }
    update_avatar(data).then(res => {
      if (res.data.code == 0) {
        Toast('更换头像成功');
      }
    })
  },

  // 手机验证码
  sms(e) {
    this.setData({
      'formData.captcha': e.detail
    })
  },


  querySupplier() {
    let that = this
    let data = {
      user_id: wx.getStorageSync(userIDKey)
    }
    get_supplier_search(data).then(res => {
      if (res.data.code == 0) {
        let info = res.data.data
        info.created_at_fmt =   dealTime(info.created_at) 
        
        const {
          fileList = []
        } = that.data;
        let avatar = info.avatar_img.name

        if (avatar == '') {
          avatar = 'icon/shop1.png'
        }

        fileList.push({
          url: that.data.imgUrl + avatar
        });

        this.setData({
          supplier_detail: info,
          fileList: fileList,
        })
      }
    })
  },

  //发送验证码
  sendCode() {
    let that = this
    let data = {
      mobile: this.data.supplier_detail.pay_mobile
    }
    send_code(data).then(res => {
      if (res.data.code == 0) {
        that.verificationCode()
        Toast('短信验证码已发送');
      }
    })
  },

  // 验证码倒计时
  verificationCode() {
    let that = this
    let currentTime = 60
    //设置一分钟的倒计时
    var interval = setInterval(function () {
      currentTime--;
      //每执行一次让倒计时秒数减一
      that.setData({
        currentTime: currentTime + 's', //按钮文字变成倒计时对应秒数
        disabled: true
      })
      //如果当秒数小于等于0时 停止计时器 且按钮文字变成重新发送 
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          currentTime: '重新发送',
          disabled: false,
          color: 'red'
        })
      }
    }, 1000);
  },
  //提交
  submit() {
    if (this.data.formData.captcha == '') {
      Toast('请输入验证码');
      return
    }

    this.data.formData.mobile = this.data.supplier_detail.pay_mobile

    bind_phone(this.data.formData).then(res => {
      if (res.data.code == 0) {
        Toast('保存成功');
        setTimeout(function () {
          wx.navigateBack()
        }, 2000)
      }
    }).catch(err => {
      Toast(err.data.message);
    })
  },

  toSupplierInfo(e) {
    let id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: './supplierInfo/index?id=' + id,
    })
  },
  //  查看图片
  viewImage(e) {
    let current = this.data.imgUrl + e.currentTarget.dataset.url
    let urls = []
    urls.push(current)
    wx.previewImage({
      current: current,
      urls: urls,
    })
  },
})