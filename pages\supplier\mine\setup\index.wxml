<view class="container">
  <view class="content">

    <van-cell-group>
      <van-cell title="头像" is-link>
        <view slot="">
          <updata-avatar fileList="{{fileList}}" bind:choosePortrait="choosePortrait"></updata-avatar>
        </view>
      </van-cell>
    </van-cell-group>

    <view class="info">
      <view class="name">中心仓</view>
      <view style="color: gray;margin-left: 10rpx;">{{supplier_detail.service_point_name}}</view>
    </view>

    <view class="info" wx:if="{{supplier_detail.level == 'station'}}">
      <view class="name">城市仓</view>
      <view style="color: gray;margin-left: 10rpx;">{{supplier_detail.station_name}}</view>
    </view>

    <view class="info">
      <view class="name">店铺名称</view>
      <view style="color: gray;margin-left: 10rpx;">{{supplier_detail.shop_simple_name}}</view>
    </view>

    <view class="info">
      <view class="name">联系人</view>
      <view style="color: gray;margin-left: 10rpx;">{{supplier_detail.contact_user}}</view>
    </view>

    <view class="info">
      <view class="name">支付手机号</view>
      <view style="color: gray;margin-left: 10rpx;">{{supplier_detail.pay_mobile}}</view>
    </view>

    <view class="info" style="flex-direction: column;" wx:if="{{supplier_detail.is_mobile_verify ===false}}">
      <van-field center clearable label="短信验证码" placeholder="请输入短信验证码" bind:change="sms" use-button-slot>
        <van-button slot="button" maxlength="6" size="small" type="primary" bind:click="sendCode">
          {{currentTime}}
        </van-button>
      </van-field>
      <view style="margin-top: 30rpx;width: 300rpx;">
        <van-button type="info" round   block bindtap="submit">保存</van-button>
      </view>
    </view>

    <view class="info">
      <view class="name">服务费费率</view>
      <view style="color: gray;flex: 1;">{{supplier_detail.supplier_service_fee}}%</view>
    </view>

    <view class="info">
      <view class="name">地址</view>
      <view style="color: gray;flex: 1;">{{supplier_detail.address}}</view>
    </view>

    <view class="info">
      <view class="name">定位</view>
      <view style="color: gray;flex: 1;">{{supplier_detail.location.address}}</view>
    </view>


    <view class="info" style="justify-content: space-between;align-items: center;" bind:tap="toSupplierInfo" data-id="{{supplier_detail.id}}">
      <view class="name">认证信息</view>
      <view style="color: gray;">
        <image style="width: 40rpx;height: auto;" src="/static/point/right.png" mode="widthFix" />
      </view>
    </view>
    

    <view class="info" style="border-bottom: none;">
      <view class="name">创建时间</view>
      <view style="color: gray;">{{supplier_detail.created_at_fmt}}</view>
    </view>

  </view>


  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>