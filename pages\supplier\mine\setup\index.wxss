/* pages/supplier/center/setup/index.wxss */
page{
  background-color: #f6f6f6;
}

.container{
  padding: 30rpx;
}

.van-uploader__upload, .van-uploader__preview-image{
  width: 60rpx !important;
  height: 60rpx !important;
}




.van-uploader__upload, .van-uploader__preview-image{
  width: 60rpx !important;
  height: 60rpx !important;
}

/* .container {
  min-height: 100vh;
  background-color: #eee;
  padding: 20rpx;
  box-sizing: border-box;
} */

.content {
  font-size: 26rpx;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 20rpx;
}

.info {
  border-bottom: 1rpx solid #ecebeb;
  padding: 26rpx 10rpx;
  display: flex;
  align-items: center;
}

.name {
  white-space: nowrap;
  width: 230rpx;
}

.nameTwo {
  white-space: nowrap;
  width: 400rpx;
}

.two {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #e4e3e3;
  padding: 20rpx;
}
.van-cell{
  padding: 0 !important;
}
.van-field__body{
padding: 10rpx 0;
}