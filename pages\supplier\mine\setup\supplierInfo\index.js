
import {
  authentication
} from '../../../../../utils/api';

const app = getApp()
Page({

  data: {
    info: {},
    imgUrl: app.globalData.imageUrl,
  },

  onLoad(options) {
    this.authInfo(options.id)
  },
  // 认证信息
  authInfo(id) {
    let data = {
      object_id: id,
      object_type: 2
    }
    authentication(data).then(res => {
      if(res.data.code == 0){
        this.setData({
          info: res.data.data
        })
        console.log(res.data.data)
      }
    })
  },
  //  查看图片
  viewImage(e) {
    let current = this.data.imgUrl + e.currentTarget.dataset.url
    let urls = []
    urls.push(current)
    wx.previewImage({
      current: current,
      urls: urls,
    })
  },

  onShow() {

  },

})