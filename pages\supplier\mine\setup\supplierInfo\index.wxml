<view class="container">
  <view class="content">

    <view class="info">
      <view class="name">企业名称</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.company.company_name}}</view>
    </view>

    <view class="info">
      <view class="name">认证企业类型</view>
      <view style="color: gray;margin-left: 10rpx;" wx:if="{{info.company.company_type == 1}}">公司</view>
      <view style="color: gray;margin-left: 10rpx;" wx:if="{{info.company.company_type == 2}}">个体工商户</view>
    </view>

    <view class="info">
      <view class="name">社会统一信用码</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.company.credit_code}}</view>
    </view>

    <view class="info">
      <view class="name">法人</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.sign_acct_name}}</view>
    </view>

    <view class="info">
      <view class="name">法人身份证号</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.company.legal.legal_ids}}</view>
    </view>

    <view class="info">
      <view class="name">法人手机号</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.company.legal.legal_phone}}</view>
    </view>

    <view class="info">
      <view class="name">银行账户</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.bank_account.card_number}}</view>
    </view>

    <view class="info" wx:if="{{info.bank_account.bankcard_basic.bank_name !==''}}">
      <view class="name">银行名称</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.bank_account.bankcard_basic.bank_name}}</view>
    </view>

    <view class="info">
      <view class="name">银行预留手机号</view>
      <view style="color: gray;margin-left: 10rpx;">{{info.bank_account.bank_reserved_mobile}}</view>
    </view>

    <view class="info">
      <view class="name">营业执照</view>
      <view style="color: gray;margin-left: 10rpx;">
        <image style="width: 200rpx;height: auto;" wx:if="{{info.company.business_license_img.name}}" src="{{imgUrl + info.company.business_license_img.name}}" data-url="{{info.company.business_license_img.name}}" bind:tap="viewImage" mode="widthFix" />
      </view>
    </view>

    <view class="info" style="border-bottom: none;">
      <view class="name">法人身份证</view>
      <view style="color: gray;margin-left: 10rpx;">
        <image style="width: 200rpx;height: auto;" wx:if="{{ info.company.legal.id_card_front_img.name}}" src="{{imgUrl + info.company.legal.id_card_front_img.name}}" mode="widthFix" data-url="{{info.company.legal.id_card_front_img.name}}" bind:tap="viewImage" />
        <image style="width: 200rpx;height: auto;margin-left: 10rpx;" wx:if="{{info.company.legal.id_card_back_img.name}}" src="{{imgUrl + info.company.legal.id_card_back_img.name}}" data-url="{{info.company.legal.id_card_back_img.name}}" mode="widthFix" bind:tap="viewImage" />
      </view>
    </view>

  </view>
</view>