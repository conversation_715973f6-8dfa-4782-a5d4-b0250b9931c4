.container {
  min-height: 100vh;
  background-color: #eee;
  padding: 20rpx;
  box-sizing: border-box;
}

.content {
  font-size: 26rpx;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 20rpx;
}

.info {
  border-bottom: 1rpx solid #ecebeb;
  padding: 26rpx 10rpx;
  display: flex;
  align-items: center;
}

.name {
  white-space: nowrap;
  width: 200rpx;
}

.nameTwo {
  white-space: nowrap;
  width: 400rpx;
}

.two {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #e4e3e3;
  padding: 20rpx;
}