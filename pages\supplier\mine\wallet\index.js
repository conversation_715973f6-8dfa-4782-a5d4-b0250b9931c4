// pages/supplier/center/wallet/index.js
import {
  merchant_balance_supplier
} from '../../../../apis/servicePoint/center';
import {
  withdraw_list
} from '../../../../apis/warehouse/center';
import {
  supplierIDKey,
} from '../../../../utils/dict';
import Toast from '@vant/weapp/toast/toast';
Page({

  data: {
    from: '',
    service_point_id: ''
  },
  //跳转提现页面
  withdraw() {
    let from = this.data.from
    let servicePointId = this.data.service_point_id
    wx.navigateTo({
      url: '/pages/supplier/mine/wallet/withdraw/index?from=' + from + '&servicePointId=' + servicePointId,
    })
  },
  onLoad(options) {
    this.setData({
      from: options.from,
      service_point_id: options.servicePointId
    })
  },

  //供应商
  supplierDetail() {
    let data = {
      supplier_id: wx.getStorageSync(supplierIDKey)
    }
    merchant_balance_supplier(data).then(res => {
      if (res.data.code == 0) {

        let accountInfoList = res.data.data.accountInfoList
        accountInfoList.forEach(ele => {
          if (ele.accountType == 'FUND_ACCOUNT') {
            this.setData({
              userBalance: ele.balance
            })
          }
        })
      }
    }).catch(err => {
      if (err.data.code == 4001) {
        this.setData({
          userBalance:  0
        })
      }
    })
  },


  withdrawList() {
    let from = this.data.from
    // 根据from传回来的值不通所传的参数不同
    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
    }
    withdraw_list(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          withdrawList: res.data.data
        })
      }
    }).catch(err => {})
  },

  onReady() {
  },

  onShow() {
    if (this.data.from == 'supplier') {
      this.supplierDetail()
    }
    if (this.data.from != '') {
      this.withdrawList()
    }
  },

})