/* pages/supplier/center/wallet/index.wxss */



.balance_wrap {
  width: calc(100% - 60rpx);
  height: 300rpx;
  background: linear-gradient(orange, #ff9912);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  margin-top: 30rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  justify-content: space-between;
  color: #ffffff;
}

.money{
  margin-top: 10rpx;
}

.money text:nth-child(1){
  font-size: 26rpx;
}
.money text:nth-child(2){

  font-size: 40rpx;
}
.withdraw {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #ffffff;
  border-radius: 22rpx;
  color: #ff9912;
}

.detail_record_title {
  padding: 30rpx;
}

.detail_record_title view:nth-child(1){
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}



.record_list {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  width: 100%;
  height: 114rpx;
  background: rgba(251, 181, 87, 0.2);
  padding: 0 30rpx;
  justify-content: space-between;
  box-sizing: border-box;
}

.record_list_{
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  width: 100%;
  padding: 10rpx 30rpx;
  padding-top: 20rpx;
  justify-content: space-between;
  box-sizing: border-box;
  border-bottom: 2rpx solid #eeeeee;
}


.record_list_left view:nth-child(1) {
  font-size: 28rpx;
  font-weight:bold;
  color: #333333;
}

.record_list_left view:nth-child(2) {
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  margin-top: 5rpx;
}

.record_value_ {
  font-size: 36rpx;
  font-weight: 500;
  color: #666666;
}

.record_value {
  font-size: 36rpx;
  font-weight: 500;
  color: #FC4840;
}
