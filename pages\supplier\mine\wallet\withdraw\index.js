
import {
  get_supplier_search
} from '../../../../../apis/supplier/center';
import {
  withdraw_by_supplier,
  merchant_balance_supplier,
  merchant_Withdrawal,
  supplier_network_info,
  withdraw_bind
} from '../../../../../apis/servicePoint/center';
import {
  userIDKey
} from '../../../../../utils/dict';

const app = getApp()
import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
import dayjs from "../../../../../libs/dayjs"

import regeneratorRuntime from 'regenerator-runtime'

Page({
  data: {
    amount: 0,
    from: '',
    supplier_id: '',
    withdraw_info: {},
    is_bank: true,
    show_dialog: false,
    account_no: '',
    bank_code: '',
    balance_int: 0,
    is_wish: false,
    frozen_balance_amount: 0,
  },


  inputAmount(e) {
    this.setData({
      amount: e.detail.value,
    })
  },

  async onLoad(options) {
    this.setData({
      supplier_id: wx.getStorageSync('supplierid')
    })
    await this.getWithdrawalInfo()
    this.getSupplierNetwork()
    this.userBalanceBySupplier()
    this.querySupplier()
  },

  //供应商
  userBalanceBySupplier() {
    return new Promise((resolve) => {

      let data = {
        supplier_id: this.data.supplier_id
      }
      merchant_balance_supplier(data).then(res => {
        if (res.data.code == 0) {
          let accountInfoList = res.data.data.accountInfoList
          accountInfoList.forEach(ele => {
            if (ele.accountType == 'FUND_ACCOUNT') {
              this.setData({
                balance_amount_fmt: ele.balance
              })
            }
          })
        }
      }).catch(err => {
        if (err.data.code == 4001) {
          this.setData({
            balance_amount_fmt: 0
          })
        }
        resolve()
      })


    })

  },


  // 冻结额
  querySupplier() {

    return new Promise((resolve) => {
      let data = {
        user_id: wx.getStorageSync(userIDKey)
      }
      get_supplier_search(data).then(res => {
        if (res.data.code == 0) {
          let info = res.data.data
          this.setData({
            frozen_balance_amount: this.dealMoney(info.frozen_balance_amount),
            withdrawable_amount: this.dealMoney(this.data.balance_amount_fmt * 100 - info.frozen_balance_amount)
          })
        }
      }).catch(err => { }).finally(() => {
        resolve()
      })
    })

  },

  dealMoney(fen) {
    return Math.round(fen) / 100
  },

  // 查询提现卡
  getWithdrawalInfo() {
    return new Promise((resolve) => {
      let data = {
        supplier_id: wx.getStorageSync('supplierid'),
      }
      merchant_Withdrawal(data).then(res => {
        if (res.data.code == 0) {
          let list = res.data.data.bankCardAccountList
          let is_bank = true
          if (!list) {
            list = []
            is_bank = false
          }
          this.setData({
            is_bank: is_bank,
            withdraw_info: list
          })
        }
      }).catch(err => { }).finally(() => {
        resolve()
      })
    })
  },


  // 查询入网信息
  getSupplierNetwork() {
    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
    }
    supplier_network_info(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          network_info: {
            sign_name: res.data.data.merchant_subject_info.sign_name,
            legal_name: res.data.data.merchant_corporation_info.legal_name
          }
        })
      }
    })
  },

  // 绑定银行卡
  bindingCard() {
    this.setData({
      show_dialog: true,
      account_no: '',
      bank_code: ''
    })
  },

  inputNo(e) {
    this.setData({
      account_no: e.detail
    })
  },
  inputCode(e) {
    this.setData({
      bank_code: e.detail
    })
  },

  // 取消
  onClose() {
    this.setData({
      show_dialog: false,
    })
  },
  // 绑定
  handleSure() {
    if (!this.data.account_no) {
      Toast('请输入卡号')
      return
    }

    if (!this.data.bank_code) {
      Toast('请输入开户行')
      return
    }
    this.setData({
      is_wish: true
    })
    let data = {
      supplier_id: wx.getStorageSync('supplierid'),
      account_no: this.data.account_no,
      bank_code: this.data.bank_code,
    }

    withdraw_bind(data).then(res => {
      if (res.data.code == 0) {
        Toast('绑定成功')
        this.setData({
          is_bank: true,
          show_dialog: false
        })
        this.getWithdrawalInfo()
      }
    }).catch(err => {
      this.setData({
        show_dialog: false,
        is_wish: false
      })
      Toast(err.data.message)
    })
  },

  //提现申请
  withdrawApply() {
    let withdrawable_amount = parseFloat(this.data.withdrawable_amount) * 100
    if (parseInt(this.data.amount * 100) < 101) {
      Toast('提现不能小于1.01');
      return
    }

    if (parseInt(this.data.amount * 100) > withdrawable_amount) {
      Toast('提现不能超过可提现金额');
      return
    }

    let account_no = this.data.withdraw_info[0].accountNo
    let data = {
      amount: parseInt(this.data.amount * 100),
      account_no: account_no,
      supplier_id: this.data.supplier_id,
    }
    Dialog.confirm({
      title: '提示',
      message: '确认提现',
    })
      .then(() => {
        withdraw_by_supplier(data).then(res => {
          if (res.data.code == 0) {
            Toast('提现成功');
            setTimeout(function () {
              wx.navigateBack()
            }, 2000)
          }
        }).catch(err => {
          Toast(err.data.message);
        })
      })
      .catch(() => { })
  },


  onShow() {

  },


})