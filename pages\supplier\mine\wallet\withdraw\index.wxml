<!--pages/supplier/center/wallet/withdraw/index.wxml-->
<wxs src="../../../../../utils/tool.wxs" module="tool" />
<view>
  <view>
    <view style="padding: 0 30rpx;font-size: 28rpx;color: #4b4a4a;" wx:if="{{!is_bank}}">
      <view style="display: flex;align-items: center;">
        <view style="width:150rpx;">主体名称：</view>
        <view>{{network_info.sign_name}}</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:150rpx;">持卡法人：</view>
        <view>{{network_info.legal_name}}</view>
      </view>
      <view style="margin-top: 20rpx;color: orange;">* 请绑定该法人的银行卡,便于提现</view>
    </view>


    <view style="padding: 0 30rpx;font-size: 28rpx;color: #4b4a4a;" wx:for="{{withdraw_info}}" wx:key="index">
      <view style="display: flex;align-items: center;">
        <view style="width:180rpx;">银行卡类型:</view>
        <view wx:if="{{item.bankCardType == 'DEBIT_CARD'}}">借记卡</view>
        <view wx:if="{{item.bankCardType == 'ENTERPRISE_ACCOUNT'}}">对公账户</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">开户名:</view>
        <view>{{item.accountName}}</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">银行账号:</view>
        <view>{{item.accountNo}}</view>
      </view>
      <view style="border-bottom: 1rpx dashed #adadad;margin-top: 20rpx;"></view>
      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">余额:</view>
        <view>￥{{balance_amount_fmt}}</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">冻结额:</view>
        <view>￥{{frozen_balance_amount}}</view>
      </view>

      <view style="display: flex;align-items: center;margin-top: 20rpx;">
        <view style="width:180rpx;">可提现金额:</view>
        <view>￥{{withdrawable_amount}}</view>
      </view>
    </view>


    <view class="inputs" wx:if="{{is_bank}}">
      <view class="unit">￥</view>
      <input type="digit" placeholder="{{amount}}" maxlength="8" bindinput="inputAmount" />
    </view>

    <view class="withdraw">
      <view bindtap="withdrawApply" wx:if="{{is_bank}}" class="btn">提现</view>
      <view bindtap="bindingCard" wx:if="{{!is_bank}}" class="btn">绑定银行卡</view>
    </view>
  </view>


  <view class="tips" wx:if="{{is_bank}}">
    提示：
    <view>a. 提现无手续费</view>
    <!-- <view>b.每日最多进行5笔提现</view>
    <view>c.提现时间：8:00至20:00</view>
    <view>d.提现前需签署提现协议</view> -->
  </view>


  <van-dialog id="van-dialog" />
  <van-dialog use-slot title="" show="{{ show_dialog }}" show-confirm-button="{{false}}" bind:close="onClose" closeOnClickOverlay>
    <view style="text-align: center;margin: 30rpx 0;font-size: 28rpx;">绑定银行卡信息</view>
    <view style="padding: 20rpx 6rpx;">
      <van-cell-group>
        <van-field value="{{ account_no }}" type="number" clearable label="卡号" placeholder="请输入卡号" hide-clear bind:input="inputNo" />
        <van-field value="{{ bank_code }}" clearable label="开户行" placeholder="请输入开户行" hide-clear bind:input="inputCode" />
      </van-cell-group>

      <view style="width: 100%;display: flex;margin-top: 50rpx; justify-content: space-around;">
        <view class='cancelbnt' bindtap='onClose'>取消</view>
        <view class='wishbnt' bindtap='handleSure' wx:if="{{!is_wish}}">确定</view>
        <view class='wishbnt' wx:if="{{is_wish}}">绑定中...</view>
      </view>
    </view>
  </van-dialog>

  <van-toast id="van-toast" style="z-index: 99999;position: fixed;" />
</view>