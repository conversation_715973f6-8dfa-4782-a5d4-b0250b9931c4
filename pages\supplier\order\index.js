const app = getApp()
import {
  supplier_order
} from '../../../apis/supplier/order';

import {
  dealTimeFormat1,
  categoryCoverProcess,
  orderStatus,
  dealFenToYuan
} from '../../../utils/dict';


import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
import dayjs from "../../../libs/dayjs"
Page({
  data: {
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    nav: app.globalData.nav,
    safeArea: app.globalData.safeArea,
    statusBarHeight: app.globalData.statusBarHeight,
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    list: [],
    page: 1,
    loadFinish: false,
    isScrolling: false,
    show: false,
    time_range: [],
    time_begin: 0,
    time_end: 0,
    date: '',
  },

  onLoad(options) {

    let now = dayjs()
    let minDate = now.subtract(3, "month").startOf('day').valueOf()
    let maxDate = now.endOf('day').valueOf()
    let time_begin = now.subtract(2, "day").startOf('day').valueOf()
    let time_range = [time_begin, maxDate]
    this.setData({
      date: `${this.dealTimeToDay(time_begin) }-${ this.dealTimeToDay(maxDate)}`,
      page: 1,
      time_range: time_range,
      time_begin: time_begin,
      time_end: maxDate,
      minDate,
      maxDate
    });
    this.listOrder()

  },

  dealTimeToDay(at) {
    return dayjs(at).format('MM/DD')
  },
  onDisplay() {
    this.setData({
      show: true
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },
  onConfirm(event) {
    const [start, end] = event.detail;
    let time_begin = start.getTime()
    let time_end = dayjs(end.getTime()).endOf('day').valueOf()
    let time_range = [time_begin, time_end]

    this.setData({
      show: false,
      page: 1,
      list: [],
      date: `${this.dealTimeToDay(start)}-${this.dealTimeToDay(time_end)}`,
      time_begin,
      time_range,
      time_end,
      loadFinish: false,
      isScrolling: false,
    });
    //  查询列表
    this.listOrder()
  },



  //供应商订单
  listOrder() {
    if (this.data.isScrolling === true) {
      return
    }
    this.data.isScrolling = true

    if (this.data.loadFinish) {
      return
    }

    let supplier_id = wx.getStorageSync("supplierid")
    let data = {
      supplier_id: supplier_id,
      page: this.data.page,
      time_begin: this.data.time_begin,
      time_end: this.data.time_end,
      limit: 10
    }
    supplier_order(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list) {
          list = []
        } else {
          list.forEach((item) => {
            item.created_at_show = dealTimeFormat1(item.created_at)
            item.order_status_show = orderStatus(item.order_status)
            item.product_list.forEach(items => {
              items.prices = items.price / 100
            })
          })
        }
        this.data.page++
        this.data.isScrolling = false;
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          loadFinish: list.length === 0,
        })
      }
    })
  },


  toOrderDetail(e) {
    let id = e.currentTarget.dataset.info.id
    wx.navigateTo({
      url: '/pages/supplier/order/info/index?id=' + id,
    })
  },

  onReachBottom() {
    this.listOrder()
  },

})