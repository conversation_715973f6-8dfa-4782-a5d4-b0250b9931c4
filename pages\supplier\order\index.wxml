<view class="nav" style="height:{{navBarHeight}}px;z-index: 999;">
  <view class="capsule-box" style="position: relative;height: {{menuHeight}}px;top:{{navBarHeight-menuHeight}}px;">
    <view>
      <view bindtap="onDisplay">时间：{{date}}</view>
      <van-calendar class="calendar" show="{{ show }}" default-date="{{ time_range}}" max-date="{{maxDate}}" min-date="{{ minDate }}" show-confirm="{{false}}" show-title="{{false}}" type="range" bind:close="onClose" bind:confirm="onConfirm" allow-same-day="true" />
    </view>
  </view>

</view>

<view class="con" style="position: relative;top: {{navBarHeight + 10}}px;min-height: 100%;margin-bottom: 150rpx;">
  <view wx:for="{{list}}" wx:key="key">
    <view class="order" data-info="{{item}}" bind:tap="toOrderDetail">
      <view class="shop_title" style="font-size: 30rpx;">
        <view style="display: flex;gap: 20rpx;align-items: center;">
          <text>{{item.buyer_name}}</text>
          <text wx:if="{{item.order_type==='retail'}}" style="background-color: red;color: white;border-radius: 14rpx;padding: 2rpx 16rpx;font-size: 22rpx;">零售</text>
        </view>
        <view style="display: flex;gap: 10rpx;">
          <van-tag wx:if="{{item.pay_status===1||item.pay_status===3}}" type="danger">
            待支付
          </van-tag>
          <van-tag wx:if="{{item.order_status===1||item.order_status===2}}" type="warning">
            {{item.order_status_show}}
          </van-tag>
          <van-tag wx:else type="{{item.order_status== 3?'success':'primary'}}">
            {{item.order_status_show}}
          </van-tag>
        </view>
      </view>
      <view class="order-con">
        <view class="product-list">
          <block wx:for="{{item.product_list}}" wx:key="key" wx:for-item="items">
            <view class="per">
              <image class="goods_cover" src="{{items.product_cover_img.name?imageUrl+categoryCoverProcess + items.product_cover_img.name:''}}" mode="widthFix" />
              <view class="left">
                <view class="titleName">{{items.product_title}}</view>
                <view style="font-size: 24rpx;margin-top: 10rpx;color: #da571b;">{{items.sku_name}}</view>
                <view style="display: flex; justify-content: space-between; margin-top: 10rpx;">
                  <text style="color: #f7ab1e;">￥{{items.prices}}</text>
                  <text>x{{items.num}}</text>
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
      <view class="order-time">
        下单时间: {{item.created_at_show}}
      </view>
    </view>
    <!-- </view> -->
  </view>
</view>
<view style="height: 200rpx;">
</view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />