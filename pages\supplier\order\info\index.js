// pages/supplier/orderManagement/orderDetail/index.js

const app = getApp()
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import {
  categoryCoverProcess
} from '../../../../utils/dict';

import {
  serch_debt_order,
  agent_pay_list,
  get_order,
  refund_after_sale_by_order
} from '../../../../apis/supplier/order';
import {
  dealTime
} from '../../../../utils/check'

import {
  dealFenToYuan
} from '../../../../utils/dict';

import regeneratorRuntime from 'regenerator-runtime'
import dayjs from "../../../../libs/dayjs"

Page({

  data: {
    order_id: '',
    order_info: {},
    imgUrl: app.globalData.imageUrl + categoryCoverProcess,
    imgUrls: app.globalData.imageUrl,
    sortRefundPop: false,
    debtOrderDetail: {
      id: "",
      all_price_fmt: ''
    },
    tableHeadList: ['类型', '实付金额', '已退金额', '分账金额', '服务费', '手续费', '供应商', '时间'],
    agentPayList: [],
    showAccount: false,
    splitAmount: 0,
    id: '',
    from: '',
    steps: [{
        text: '',
        desc: '',
        inactiveIcon: 'clock-o',
        activeIcon: 'clock-o',
      },
      {
        text: '',
        desc: '',
        inactiveIcon: 'circle',
        activeIcon: 'circle',
      },
    ],

    showAddress: false,
    logisticsPop: false,
    refund_list:[],
    show_snapshot:false,
    product_snapshot_list:[]

  },

  async onLoad(options) {
    if (!options.id) {
      wx.switchTab({
        url: '/pages/supplier/mine/index',
      })
      return
    }
    let from = options.from
    this.setData({
      order_id: options.id,
      from
    })
    await this.queryOrder()
    await this.getRefoundAll()
    await this.queryDebtOrder()
    this.qualityInfo()

    this.dealAfterSaleRefund()
  },

    // 交易快照
    handleSeeSnapshot() {
      let list = this.data.order_info.product_list
      this.setData({
        show_snapshot: true,
        product_snapshot_list: list
      })
  
    },
  
    onCloseSnapshot() {
      this.setData({
        show_snapshot: false
      })
    },

    handletoSnapshot(e) {
      let product_id = e.currentTarget.dataset.productid
      let order_id = this.data.order_info.id
      wx.navigateTo({
        url: `./snapshot/index?productId=${product_id}&orderId=${order_id}`,
      })
      this.setData({
        show_snapshot: false
      })
    },

  //图片
  see(e) {
    let photo_list = e.currentTarget.dataset.information.photo_list
    this.setData({
      actionShow: true,
      quality_photo_list: photo_list
    })
  },
  //分拣详情
  refundDetail(e) {
    let product_id = e.currentTarget.dataset.info.product_id
    let order_id = e.currentTarget.dataset.order_id
    if (this.data.from == "afterSale") {
      wx.navigateBack()
      return
    }
    wx.navigateTo({
      url: '/pages/supplier/afterSale/info/index?id=' + product_id + '&order_id=' + order_id,
    })
  },
  // 关闭分拣退款弹窗
  sortRefundPopClose() {
    this.setData({
      sortRefundPop: false
    })
  },

  dealTime(at) {
    return dayjs(at).format('YYYY-MM-DD HH:mm:ss')
  },

  dealAfterSaleRefund() {
    let refund = this.data.refund_list
    let order_info = this.data.order_info
    if (order_info.product_list) {
      order_info.product_list.forEach(ele => {
        refund.forEach(item => {
          if (ele.product_id == item.product_id) {
            if (item.refund_type == 1) {
              ele.refund_after_sale_info = item
            }
          }
        })
      })
    }


    this.setData({
      order_info: order_info,
    })

  },

  queryOrder() {
    return new Promise(callback => {
      let id = this.data.order_id
      let data = {
        id: id,
      }
      get_order(data).then(res => {
        if (res.data.code === 0) {
          let detail = res.data.data
          let newList = []
          detail.record_list.map((item, index) => {
            newList[index] = {
              text: item.text,
              desc: item.timestamp == 0 ? "" : this.dealTime(item.timestamp),
            }
          })
          let is_include_brand = false
          detail.product_list.forEach((product) => {
            product.price_fmt = (product.price / 100).toFixed(2)
            product.settle_unit_price_fmt = (product.settle_unit_price / 100).toFixed(2)

            if (!is_include_brand) {
              if (product.link_brand_status == 2) {
                is_include_brand = true
              }
            }
          })

          detail.paid_amount_fmt = this.dealMoney(detail.paid_amount)
          detail.total_transport_fee_fmt = this.dealMoney(detail.total_transport_fee)
          detail.total_service_fee_fmt = this.dealMoney(detail.total_service_fee)

          detail.coupon_amount_fmt = this.dealMoney(detail.coupon_amount) //优惠券金额
          detail.coupon_min_amount_fmt = this.dealMoney(detail.coupon_min_amount) //优惠券最低满减金额
          detail.coupon_split_amount_fmt = this.dealMoney(detail.coupon_split_amount) //优惠券最低满减金额
          detail.status = this.backStatus(detail.order_status)

          this.setData({
            order_info: detail,
            logistics: newList,
          })
          this.calcSplitAmount(this.data.order_info)
          this.dealStep()
          callback()
        }

      })

    }).finally(() => {

    })
  },

  // 新版品控售后
  getRefoundAll() {
    return new Promise(resolve => {
      setTimeout(() => {
        let order_info = this.data.order_info
        let data = {
          order_id: order_info.id
        }
        refund_after_sale_by_order(data).then(res => {
          if (res.data.code == 0) {
            let list = res.data.data
            if (!list) {
              list = []
            }
            this.setData({
              refund_list: list,
            })
            resolve()
          }
        })
      }, 200)

    }).finally(() => {

    })
  },

  queryDebtOrder() {
    return new Promise(resolve => {
      let order_info = this.data.order_info
      if (order_info.order_status != 7 && order_info.order_status != 8 && order_info.order_status != 9) {
        resolve()
        return
      }
      let orderID = this.data.order_id

      serch_debt_order({
        order_id: orderID,
      }).then(res => {
        if (res.data.code == 0) {
          let data = res.data.data

          if (!data.product_list) {
            data.product_list = []
          }
          if (!data.settle_product_list) {
            data.settle_product_list = []
          }

          // 退款
          data.settle_product_list.forEach(pro => {
            pro.price_fmt = (pro.price / 100).toFixed(2)
            pro.diff_product_amount_fmt = (pro.diff_product_amount / 100).toFixed(2)
            pro.due_weight_fmt = (pro.due_weight / 1000).toFixed(1)
            pro.sort_weight_fmt = (pro.sort_weight / 1000).toFixed(1)
            pro.product_rough_weight_unit_price_kg_fmt = this.dealMoney(pro.product_rough_weight_unit_price_kg)
            pro.settle_unit_price_fmt = this.dealMoney(pro.settle_unit_price)
            pro.order_product_amount_fmt = this.dealMoney(pro.order_product_amount)

            pro.product_rough_weight_unit_price_kg_fmt = this.dealMoney(pro.product_rough_weight_unit_price_kg)
            pro.price_fmt = this.dealMoney(pro.price)

            if (pro.settle_result_type == 'refund') {
              pro.total_amount_fmt = this.dealMoney(pro.diff_product_amount + pro.total_service_fee + pro.total_transport_fee)
            }
          })

          data.offset_product_amount_fmt = this.dealMoney(data.offset_product_amount)
          data.total_product_amount_fmt = this.dealMoney(data.total_product_amount)
          data.paid_product_amount_fmt = this.dealMoney(data.paid_product_amount)

          data.refund_total_product_amount_fmt = this.dealMoney(data.refund_total_product_amount)
          data.refund_total_service_fee_fmt = this.dealMoney(data.refund_total_service_fee)
          data.refund_total_transport_fee_fmt = this.dealMoney(data.refund_total_transport_fee)
          data.refund_final_amount_fmt = this.dealMoney(data.refund_final_amount)

          let refund_total_amount = data.refund_total_transport_fee + data.refund_total_service_fee + data.refund_total_product_amount

          data.refund_total_amount = refund_total_amount
          data.refund_total_amount_fmt = this.dealMoney(refund_total_amount)

          this.setData({
            debtOrderDetail: data,
          })
        }
      }).catch(err => {
        if (err.data.code == 4002) {}
      }).finally(() => {
        resolve()
      })
    })
  },

  qualityInfo() {
    let order_info = this.data.order_info
    let debtOrderDetail = this.data.debtOrderDetail
    //  匹配补差
    if (debtOrderDetail.settle_product_list) {
      debtOrderDetail.settle_product_list.forEach(pro => {
        order_info.product_list.forEach(item => {
          if (pro.product_id == item.product_id) {
            item.debt_info = pro
          }
        })
      })
    }

    this.setData({
      order_info: order_info,
    })

  },
  dealMoney(fen) {
    return Math.round(fen) / 100
  },
  dealWeight(n) {
    return Math.round(n) / 1000
  },


  //分拣退款详情
  sortRefundDetail(e) {
    this.setData({
      sortRefundPop: true
    })
    let info = e.currentTarget.dataset.info
    // 品控退款
    this.setData({
      refund_quality_info: info.refund_quality_info,
    })
  },


  dealStep() {
    let order = this.data.order_info
    let steps = this.data.steps
    let prefix = ''
    if (order.deliver_type === 1 || order.deliver_type === 3) {
      prefix = '送至 '
    }
    if (order.deliver_type === 2) {
      prefix = '自提 '
    }

    steps[1].text = prefix + ' ' + order.address.address + '---' + order.address.location.address
    let person = order.address.contact.name + '  ' + order.address.contact.mobile
    person += ' 【' + order.buyer_name + '】'
    steps[1].desc = person

    let status = this.backStatus(order.order_status)

    if (order.pay_status === 99 || order.pay_status === 1) {
      status = '待付款'
    }

    status += " >"
    steps[0].text = status
    steps[0].desc = ''

    this.setData({
      steps: steps
    })
  },

  backStatus(status) {
    switch (status) {
      case 1:
        return '已关闭';
      case 2:
        return '已取消';
      case 3:
        return '待备货';
      case 4:
        return '已备货待品控';
      case 5:
        return '已品控待分拣';
      case 6:
        return '已品控待发货';
      case 7:
        return '已发货运输中';
      case 8:
        return '待收货';
      case 9:
        return '已完成';
    }
  },


  step(e) {
    this.setData({
      logisticsPop: true
    })
  },

  //关闭物流弹窗
  onClose() {
    this.setData({
      logisticsPop: false,
      imgModal: false,

    });
  },

  onCloseShowAddr() {
    this.setData({
      showAddress: false
    })
  },

  calcSplitAmount(order) {
    let total = order.product_total_amount
    let refund = 0
    order.product_list.forEach(item => {
      refund += item.has_refund_amount - item.has_refund_warehouse_load_amount
    })

    let splitAmount = (total - refund) / 100

    this.setData({
      splitAmount
    })
  },

  // 展示分账信息
  handleShowAccounts() {
    this.setData({
      showAccount: false
    })
    if (this.data.agentPayListL !== undefined) return
    if (this.data.order_info.has_agent_pay == true && this.data.showAccount == true) {
      let data = {
        order_id: this.data.order_info.id
      }
      agent_pay_list(data).then(res => {
        if (res.data.code == 0) {
          let list = res.data.data
          list.map(item => {
            item.created_at_fmt = dealTime(item.created_at)
            item.order_paid_amount_fmt = dealFenToYuan(item.order_paid_amount)
            item.order_amont_fmt = dealFenToYuan(item.order_paid_amount - item.order_split_amount)
            item.order_split_amount_fmt = dealFenToYuan(item.order_split_amount)
            item.service_fmt = dealFenToYuan(item.warehouse_commission_amount + item.service_point_commission_amount + item.platform_amount)
            item.supplier_fee_amount_fmt = dealFenToYuan(item.supplier_fee_amount)
            item.supplier_amount_fmt = dealFenToYuan(item.supplier_amount)
            let status = item.agent_pay_type
            let status_desc = ''
            switch (status) {
              case 1:
                status_desc = '普通订单'
                break;
              case 2:
                status_desc = '补差'
                break;
              case 3:
                status_desc = '代金券'
                break;
              default:
                break;
            }
            item.desc = status_desc
          })

          this.setData({
            agentPayListL: list
          })
        }
      })
    }
  },

  // 补差弹出框
  handleDebt(e) {
    let info = e.currentTarget.dataset.info
    info.amount_fmt = (info.amount / 100).toFixed(2)
    info.total_warehouse_load_fee_fmt = (info.total_warehouse_load_fee / 100).toFixed(2)
    info.over_weight_fmt = (info.over_weight / 1000)
    info.sort_weight_fmt = (info.sort_weight / 1000)
    info.due_weight_fmt = (info.due_weight / 1000)
    info.all_amount = ((info.amount + info.total_warehouse_load_fee + info.total_service_fee) / 100).toFixed(2)
    info.per_price = (info.amount / info.over_weight) * 10

    this.setData({
      debt_show: true,
      debt_show_info: info
    })
  },
  handleDebtClose() {
    this.setData({
      debt_show: false,
    })
  },


  showDeliveryImg(e) {
    let src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src,
      urls: [src],
    })
  },

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    // let from = "normal"
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  },

  onCloseQualityPhoto() {
    this.setData({
      actionShow: false
    })
  },


    //  查看图片
    previewReceive(e) {
      let img = e.currentTarget.dataset.src
      wx.previewImage({
        current: img,
        urls: [img] 
      })
    },
})