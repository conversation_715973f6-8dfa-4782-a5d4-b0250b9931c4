<!--pages/center/order/orderDetail/index.wxml-->
<wxs src="../../../../utils/tool.wxs" module="tool" />
<view class="container">
  <view class="status" bind:tap="step">{{order_info.status}} ></view>


  <view style="background-color: #fff;margin-bottom: 20rpx;padding: 20rpx;border-radius: 20rpx;" wx:if="{{debtOrderDetail && (debtOrderDetail.paid_product_amount> 0 || debtOrderDetail.refund_final_amount > 0)}}">
    <view wx:key="index" >
      <view style="margin-bottom: 20rpx;">订单结算</view>
      <view>
        <view style="margin: 10rpx 0;">
          <view class="" wx:if="{{debtOrderDetail.refund_total_amount>0}}" class="btn-content">
            <view>品控总退款：</view>
            <view>{{debtOrderDetail.refund_total_amount_fmt}}元</view>
          </view>
          <view class="" wx:if="{{debtOrderDetail.total_product_amount>0}}" class="btn-content">
            <view>补差总金额：</view>
            <view>{{debtOrderDetail.total_product_amount_fmt}}元</view>
          </view>
          <view class="" wx:if="{{debtOrderDetail.refund_total_amount>0}}" class="btn-content">
            <view>实退：</view>
            <view>{{debtOrderDetail.refund_final_amount_fmt}}元</view>
          </view>
          <view class="" wx:if="{{debtOrderDetail.paid_product_amount>0}}" class="btn-content">
            <view wx:if="{{debtOrderDetail.pay_status==4}}">补差已支付：</view>
            <view wx:else>补差待支付：</view>
            <view>{{debtOrderDetail.paid_product_amount_fmt}}元</view>
          </view>
        </view>
      </view>
    
    </view>

  </view>

  <view class="pay_content_wrap part" wx:key="key">
    <view style="display: flex;justify-content: space-between;">
      <view style="font-size: 28rpx;">{{order_info.supplier_name}}</view>
    </view>


    <view class="goods_content" wx:for="{{order_info.product_list}}" wx:for-item="items" wx:key="key">
      <image class="goods_cover" bindtap="jumpProductDetail" data-info="{{items}}" src="{{items.product_cover_img.name?imgUrl+items.product_cover_img.name:''}}" mode="" />
      <view class="goods_base_param">
        <view class="goods_title">

          <view bindtap="jumpProductDetail" data-info="{{items}}">
            <text class="weight" wx:if="{{!items.is_check_weight}}">计件销售</text>
            <text class="weight" wx:if="{{items.is_check_weight}}">称重销售</text>
            <text style="font-size: 28rpx;"> {{items.product_title}}</text>
          </view>
          <view style="font-size: 26rpx; margin-top: 10rpx;color: #da571b;">{{items.sku_name}}</view>

          <view style="display: flex;gap: 20rpx;margin-top: 6rpx;align-items: center;">
            <view class="chart" bindtap="see" data-information="{{items}}" wx:if="{{items.photo_list && items.photo_list.length > 0}}">查看发货实拍图</view>
          </view>
        </view>

        <view style="display: flex;justify-content: space-between;align-items: center;">
          <view style="color: #ff5000;display: flex;gap: 10rpx;align-items: flex-end;flex:1">
            <view>
              <text style="font-size: 22rpx;">￥</text>
              <text style="font-size: 30rpx;">{{items.price_fmt}}</text>
            </view>
            <text class="settle" wx:if="{{items.product_rough_weight_unit_price_kg !== items.settle_unit_price && items.is_check_weight}}">
              结算单价：￥{{items.settle_unit_price_fmt}}/kg
            </text>
            <text class="settle" wx:if="{{items.price !== items.settle_unit_price&& !items.is_check_weight}}">
              结算单价：￥{{items.settle_unit_price_fmt}}/件
            </text>
          </view>
          <view style="font-size: 26rpx;">x{{items.num}}</view>
        </view>


        <view class="apply_service">
          <view>
            <!-- TODO判断是品控 -->
            <view data-info="{{items}}" wx:if="{{items.refund_quality_info}}" bindtap="sortRefundDetail" style="font-size: 22rpx;color: #0c86da;margin-top: 10rpx;">
              <text wx:if="{{items.sort_num ==0}}">缺货已退款:</text>
              <text wx:if="{{items.sort_num !=0 && items.sort_num != items.num}}">部分缺货退款:</text>
              <text wx:if="{{items.sort_num !=0 && items.sort_num == items.num}}">品控已退款:</text>
              <text>￥{{items.debt_info.total_amount_fmt}}></text>
            </view>


            <view wx:if="{{items.debt_info}}" style="display: flex;gap: 10rpx;" bind:tap="handleDebt" data-info='{{items.debt_info}}'>
              <view>
                <text wx:if="{{items.debt_info.settle_result_type == 'debt'}}">结算补差:￥{{items.debt_info.diff_product_amount_fmt}}></text>
                <text wx:if="{{items.debt_info.settle_result_type == 'refund'}}">结算退款：￥{{items.debt_info.total_amount_fmt}}></text>
              </view>
            </view>


          </view>
          <view wx:if="{{!items.is_ship_refund_all}}">
            <view data-info="{{items}}" class="per finish" data-order_id="{{order_info.id}}" wx:if="{{items.refund_after_sale_info &&order_info.order_status==9}}" catchtap="refundDetail" style="color: #0c86da;">售后详情> </view>
          </view>
        </view>
      </view>

    </view>

    <view class="order_detail">
      <view style="display: flex;gap: 20rpx;">
        <van-tag type="warning" wx:if="{{order_info.deliver_type===1}}">交付方式：送货到店</van-tag>
        <van-tag type="warning" wx:if="{{order_info.deliver_type===2}}">交付方式：自提</van-tag>
        <van-tag type="warning" wx:if="{{order_info.deliver_type===3}}">交付方式：第三方物流</van-tag>
        <van-tag color="#245e56" text-color="#ffffff" wx:if="{{order_info.deliver_type==3}}" style="margin-left: 10rpx;">{{order_info.logistics_name}}</van-tag>
        <van-tag type="warning" wx:if="{{order_info.deliver_type===4}}">交付方式：即时配送</van-tag>
        <van-tag color="#245e56" text-color="#ffffff" wx:if="{{order_info.deliver_type==4}}" style="margin-left: 10rpx;">{{order_info.instant_deliver_name}}</van-tag>
        <van-tag type="warning" wx:if="{{order_info.deliver_type===5}}">交付方式：快递</van-tag>
        <view style="color: #0b85db;border-bottom: 1px solid #0b85db;" wx:if="{{order_info.deliver_type===2}}" catch:tap="handleView">查看自提地址</view>
      </view>

      <view class="info">
        <view class="title-line">
          <text class="leftTitle">商品总额：</text>
          <text style="margin-left: 10rpx; font-weight: bold;" wx:if="{{order_info.origin_product_total_amount>0}}">￥{{tool.toFixedTwo(order_info.origin_product_total_amount / 100)}}</text>
          <text style="margin-left: 10rpx; font-weight: bold;" wx:else>￥{{tool.toFixedTwo(order_info.product_total_amount / 100)}}</text>
        </view>

        <view class="title-line">
          <text class="leftTitle">服务费：</text>
          <view>
            <text style="margin-left: 10rpx;font-weight: bold;">￥{{order_info.total_service_fee_fmt}}</text>
          </view>
        </view>

        <view class="title-line">
          <text class="leftTitle">干线费：</text>
          <text style="margin-left: 10rpx; font-weight: bold;">￥{{order_info.total_transport_fee_fmt }}</text>
        </view>

        <view class="title-line" wx:if="{{order_info.deliver_type===1 || order_info.deliver_type == 4}}">
          <text class="leftTitle">配送费：</text>
          <view>
            <text class="tip">（同时下单的订单共享同一配送费）</text>
            <text style="margin-left: 10rpx; font-weight: bold;">￥{{tool.toFixedTwo(order_info.deliver_fee_res.final_deliver_fee / 100)}}</text>
          </view>
        </view>

        <view class="title-line" wx:if="{{order_info.coupon_amount > 0}}">
          <text class="leftTitle">优惠券：</text>
          <view>
            <text style="margin-left: 10rpx; " class="tip" wx:if="{{order_info.coupon_amount > 0}}">(满{{ order_info.coupon_min_amount_fmt}}减{{order_info.coupon_amount_fmt}})</text>
            <text style="margin-left: 10rpx;font-weight: bold;">￥{{order_info.coupon_split_amount_fmt}}</text>
          </view>
        </view>

        <view class="title-line" wx:if="{{order_info.discount_amount>0}}">
          <text>折扣：</text>
          <text style="color: #ff5000;  font-weight: bold;"> - ￥{{tool.toFixedTwo(order_info.discount_amount / 100)}}</text>
        </view>

        <view class="title-line" wx:if="{{order_info.official_subsidy_amount>0}}">
          <text>补贴：</text>
          <text style="color: #ff5000;  font-weight: bold;"> - ￥{{tool.toFixedTwo((order_info.official_subsidy_amount) / 100)}}</text>
        </view>

        <view class="title-line">
          <text class="leftTitle">金额小计：</text>
          <text style=" font-weight: bold; color: #ff5000;">￥{{tool.toFixedTwo(order_info.paid_amount / 100)}}</text>
        </view>
        <view style="border-bottom: 2rpx solid  #f4f4f4;"></view>
        <view class="title-line" wx:if="{{order_info.station_name !== ''}}">
          <text class="leftTitle">订单归属：</text>
          <text wx:if="{{order_info.supplier_level=='station'}}">服务仓</text>
          <text wx:if="{{order_info.supplier_level=='point'}}">中心仓</text>
        </view>
        <view class="title-line" wx:if="{{order_info.station_name !== ''}}">
          <text class="leftTitle">订单站点：</text>
          <text>{{order_info.station_name}}</text>
        </view>
      

        <view class="title-line">
          <text class="leftTitle">订单编号：</text>
          <text>{{order_info.id_num}}<text bind:tap="copyOrderNum" style="font-size: 24rpx;color: #a8a8a8;margin-left: 10rpx;">复制</text></text>
        </view>

        <view class="title-line">
          <text class="leftTitle">订单快照：</text>
          <view style="display: flex;align-items: center;" catch:tap="handleSeeSnapshot">
            <text>发生交易争议时，可作为判断依据</text>
            <image src="/static/point/right.png" mode="widthFix" style="width: 30rpx;height: auto;" />
          </view>
        </view>
        <view class="title-line" wx:if="{{order_info.pay_status===4}}">
          <text class="leftTitle">支付方式：</text>
          <text wx:if="{{order_info.pay_method==='yee_wechat'}}">微信支付</text>
          <text wx:if="{{order_info.pay_method==='yee_balance'}}">钱包支付</text>
        </view>

        <view class="title-line">
          <text class="leftTitle">订单时间：</text>
          <text>{{tool.filtersData(order_info.created_at)}}</text>
        </view>

        <view class="title-line" wx:if="{{order_info.order_status_record.receive_time!==0 && order_info.order_status == 9}}">
          <text class="leftTitle">收货时间：</text>
          <text>{{tool.filtersData(order_info.order_status_record.receive_time)}}</text>
        </view>

        <view class="title-line">
          <text class="leftTitle">订单留言：</text>
          <view class="text">
            <view class="word">{{orderNote}}</view>
            <view wx:if="{{order_info.order_status==3}}">
              <image src="{{imgUrls+'icon/edit.png'}}" mode="widthFix" data-note="{{orderNote}}" bind:tap="handleNote" style="height: auto;width: 30rpx;"></image>
            </view>

          </view>
        </view>

      </view>
    </view>
  </view>


  <view class="receipt_img_list part" wx:if="{{order_info.deliver_type===3&&(order_info.order_status==8||order_info.order_status==9)}}">
    <view style="margin-bottom: 20rpx;font-size: 30rpx;font-weight: bold;">
      物流单
    </view>

    <view wx:key="keys" style="margin-right: 20rpx;display: flex; flex-wrap: wrap;">
      <view wx:for="{{order_info.logistics_image_list}}" wx:key="keys">
        <van-image style="margin-right: 20rpx;" bindtap="previewReceive" data-src="{{imgUrls + item.name}}" width="100" height="100" wx:if="{{item.name!==''}}" src="{{imgUrls + item.name}}" />
      </view>

      <view wx:if="{{!order_info.logistics_image_list}}" style="font-size: 26rpx;">待上传</view>
    </view>
    <view style="font-size: 26rpx;" wx:if="{{logisticsTimeFmt}}">自动确认收货时间：{{logisticsTimeFmt}}</view>
  </view>

  <view class="receipt_img_list part" wx:if="{{order_info.deliver_type===5&&(order_info.order_status==8||order_info.order_status==9)}}">
    <view style="margin-bottom: 20rpx;font-size: 30rpx;font-weight: bold;">
      快递单
    </view>
    <view wx:key="keys" style="margin-right: 20rpx;display: flex; flex-wrap: wrap;">
      <view wx:for="{{order_info.logistics_image_list}}" wx:key="keys">
        <van-image style="margin-right: 20rpx;" bindtap="previewReceive" data-src="{{imgUrls + item.name}}" width="100" height="100" wx:if="{{item.name!==''}}" src="{{imgUrls + item.name}}" />
      </view>
    </view>
  </view>
  <view class="receipt_img_list part">
    <view style="font-size: 26rpx;margin-bottom: 10rpx;">地址：{{order_info.address.address}}</view>
    <view style="font-size: 26rpx;margin-bottom: 10rpx;">联系人：{{order_info.address.contact.name}}</view>
    <view style="font-size: 26rpx;margin-bottom: 10rpx;">电话：{{order_info.address.contact.mobile}}</view>
    <view style="font-size: 26rpx;">会员名称：【{{order_info.buyer_name}}】</view>
  </view>

  <settlePage orderId="{{order_id}}"></settlePage> 


  <view class="receipt_img_list part" wx:if="{{order_info.order_status==9&&order_info.delivery_img_list!==null&&order_info.delivery_img_list.length!==0}}">
    <view style="margin-bottom: 20rpx;font-size: 30rpx;font-weight: bold;">
      交付回单
    </view>
    <view style="margin-right: 20rpx;display: flex;  flex-wrap: wrap;">
      <view wx:for="{{order_info.delivery_img_list}}" wx:key="keys">
        <van-image style="margin-right: 20rpx;" bindtap="previewReceive" data-src="{{imgUrls + item.name}}" width="100" height="100" wx:if="{{item.name!==''}}" src="{{imgUrls + item.name}}" />
      </view>
    </view>
    <view style="font-size: 22rpx;" wx:if="{{order_info.delivery_user_name !==''}}">配送员：{{order_info.delivery_user_name}} </view>
  </view>

  <view style="width: 100%;height: 180rpx;"></view>

</view>


<van-action-sheet show="{{ logisticsPop }}" bind:close="onClose" title="订单进度">
  <view>
    <van-steps steps="{{ logistics }}" active="{{ order_info.order_status - 2 }}" direction="vertical" active-color="#ee0a24" />
  </view>
</van-action-sheet>

<van-action-sheet show="{{ sortRefundPop }}" bind:close="sortRefundPopClose" title="退款明细">
  <view class="sort_refund_detail">
    <view class="Delivery">
      <text style="width: 24%; ">商品名称:</text>
      <text>{{refund_quality_info.product_title}}</text>
    </view>

    <view class="Delivery">
      <text style="width: 24%; ">品控图片:</text>
      <image wx:if="{{refund_quality_info.reason_img && refund_quality_info.reason_img.name!== ''}}" style="width: 100rpx; height: 100rpx;" src="{{imgUrl+refund_quality_info.reason_img.name}}" bindtap="handleImg"></image>
      <text wx:else>暂无</text>
    </view>

    <view class="Delivery" wx:if="{{refund_quality_info.is_check_weight}}">
      <text style="width: 24%; ">退款重量:</text>
      <text>{{refund_quality_info.refund_weight_fmt}}kg</text>
    </view>
    <view class="Delivery" wx:else>
      <text style="width: 24%; ">退款数量:</text>
      <text>{{refund_quality_info.num}}</text>
    </view>
    <view class="Delivery">
      <text style="width: 24%; ">商品:</text>
      <text>￥{{refund_quality_info.product_amount_fmt}}元</text>
    </view>

    <view class="Delivery">
      <text style="width: 24%; ">干线费:</text>
      <text>￥{{refund_quality_info.total_transport_fee_fmt }}元</text>
    </view>

    <view class="Delivery">
      <text style="width: 24%; ">服务费:</text>
      <text>￥{{refund_quality_info.total_service_fee_fmt}}元</text>
    </view>

    <view class="Delivery" style="font-weight: bold;">
      <text style="width: 24%; ">退款小计:</text>
      <text>￥{{refund_quality_info.total_amount_fmt}}元</text>
    </view>


  </view>
</van-action-sheet>

<van-action-sheet show="{{ debt_show }}" bind:close="handleDebtClose" title="结算详情">
  <view class="sort_refund_detail">
    <view class="Delivery">
      <text style="width: 26%; ">商品名称:</text>
      <text>{{debt_show_info.product_title}}</text>
    </view>

    <view class="Delivery">
      <text style="width: 24%; ">订单总金额:</text>
      <text>￥{{debt_show_info.order_product_amount_fmt}}元</text>
    </view>

    <view class="Delivery">
      <text style="width: 24%; ">商品单价:</text>
      <text wx:if="{{debt_show_info.is_check_weight}}">￥{{debt_show_info.product_rough_weight_unit_price_kg_fmt}}/kg</text>
      <text wx:if="{{!debt_show_info.is_check_weight}}">￥{{debt_show_info.price_fmt}}/kg</text>
    </view>

    <view class="Delivery" wx:if="{{debt_show_info.is_check_weight}}">
      <text style="width: 24%; ">订单重量:</text>
      <text>{{debt_show_info.due_weight_fmt}}kg</text>
    </view>

    <view class="Delivery" wx:if="{{debt_show_info.is_check_weight}}">
      <text style="width: 24%; ">发货重量:</text>
      <text>{{debt_show_info.sort_weight_fmt}}kg</text>
    </view>

    <view class="Delivery" wx:if="{{!debt_show_info.is_check_weight}}">
      <text style="width: 24%; ">订单数量:</text>
      <text>{{debt_show_info.num}}</text>
    </view>
    <view class="Delivery" wx:if="{{!debt_show_info.is_check_weight}}">
      <text style="width: 24%; ">发货数量:</text>
      <text>{{debt_show_info.sort_num}}</text>
    </view>

    <view class="Delivery" wx:if="{{debt_info.total_service_fee > 0}}">
      <text style="width: 24%; ">服务金额:</text>
      <text>￥{{debt_show_info.total_service_fee_fmt}}元</text>
    </view>

    <view class="Delivery">
      <text style="width: 24%; ">结算单价:</text>
      <text>￥{{debt_show_info.settle_unit_price_fmt}}/kg</text>
    </view>

    <view class="Delivery" style="font-weight: bold;">
      <text style="width: 24%; ">金额小计:</text>
      <text wx:if="{{debt_show_info.settle_result_type == 'debt'}}">￥{{debt_show_info.diff_product_amount_fmt }}元</text>
      <text wx:if="{{debt_show_info.settle_result_type == 'debt'}}">（补差）</text>
      <text wx:if="{{debt_show_info.settle_result_type == 'refund'}}">￥{{debt_show_info.total_amount_fmt }}元</text>
      <text wx:if="{{debt_show_info.settle_result_type == 'refund'}}">（退款）</text>
    </view>
  </view>
</van-action-sheet>


<van-action-sheet show="{{ point_show }}" bind:close="clostPoint" title="城市仓">
  <view class="sort_refund_detail">
    <view class="pickup_point_content">
      <view style="font-size: 28rpx;">
        <text style="font-weight: bold;">{{servicePointDetail.name}}</text>
      </view>

      <view style="font-size: 24rpx;margin:10rpx 0 ;">{{servicePointDetail.address}} </view>
      <view style="display: flex;align-items: center;gap: 40rpx;">
        <view style="font-size: 24rpx;">
          <text> 联系电话 </text>
          <text style="padding-right: 6rpx;" bind:tap="makePhoneCall"> {{servicePointDetail.contact_mobile}}</text>
          <van-icon name="phone-o" bind:tap="makePhoneCall" />
        </view>
        <view bind:tap="mapNavigation" style="display: flex;">
          <text style="color: orange;font-size: 24rpx;">查看地图></text>
        </view>
      </view>

    </view>


  </view>
</van-action-sheet>


<van-popup show="{{ imgModal }}" bind:close="onClose">
  <image wx:if="{{sortRefundDetail!=''&&sortRefundDetail.reason_img &&sortRefundDetail.reason_img.name!== ''}}" src="{{imgUrl+sortRefundDetail.reason_img.name}}"></image>
</van-popup>
<van-toast id="van-toast" />


<van-dialog use-slot title="" show="{{ showNote }}" show-confirm-button="{{false}}" bind:close="onCloseNote">
  <view class="modalDlg">
    <view>
      <view style="margin-bottom: 20rpx;text-align: center;font-weight: bold;">
        留言
      </view>
      <view>
        <van-field custom-class="input1" value="{{ noteValue }}" bind:change="onInputNote" type="textarea" maxlength="30" show-word-limit autosize />
      </view>
    </view>

    <view style="width: 100%;display: inline-flex;margin-top: 20rpx;">
      <view style="width:100rpx" class='cancelbnt' bindtap='onCloseNote'>取消</view>
      <view style="width:60%" class='wishbnt' bindtap='confirmNote'>确定</view>
    </view>
  </view>
</van-dialog>



<!-- 品控图 -->
<van-action-sheet show="{{ actionShow }}" title="分拣发货实拍图" bind:close="onCloseQualityPhoto">
  <view style="display: flex;padding: 0 20rpx;flex-wrap: wrap;justify-content: center;">
    <view wx:for="{{quality_photo_list}}" wx:key="index" style="margin: 0 15rpx 15rpx 15rpx;">
      <image style="width: 200rpx;height: auto;" src="{{imgUrl + item.name}}" mode="widthFix" data-url="{{item.name}}" bind:tap="viewImage" />
    </view>
  </view>
  <view style="margin-bottom: 50rpx;"></view>
</van-action-sheet>


<!-- 交易快照 -->
<van-action-sheet show="{{ show_snapshot }}" title="查看交易快照" bind:close="onCloseSnapshot">
  <view class="action-snapshot" style="padding: 20rpx;">
    <view style="font-size: 26rpx;">订单包含多件商品，请选择单一商品查看</view>
    <view wx:for="{{product_snapshot_list}}" wx:key="index" class="snapshot">
      <image class="img" src="{{imgUrl + item.product_cover_img.name}}" mode="widthFix" data-url="{{item.product_cover_img.name}}" bind:tap="viewImage" />
      <view class="right-info">
        <view style="font-size: 26rpx;font-weight: bold;">{{item.product_title}}</view>
        <view style="font-size: 26rpx; margin-top: 10rpx;">{{item.sku_name}}</view>
        <view class="s-btn">
          <view style="font-size: 26rpx;">
            <text>￥{{item.price_fmt}}</text>
            <text style="font-size: 22rpx;margin-left: 10rpx;">x{{item.num}}</text>
          </view>
          <view class="snapshot-btn" catch:tap="handletoSnapshot" data-productid="{{item.product_id}}">交易快照</view>
        </view>
      </view>

    </view>
  </view>
  <view style="margin-bottom: 50rpx;"></view>
</van-action-sheet>