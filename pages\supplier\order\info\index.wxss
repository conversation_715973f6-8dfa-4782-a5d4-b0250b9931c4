/* pages/center/order/orderDetail/index.wxss */
.container {
  background-color: #f2f2f2;
  padding: 16rpx;
  box-sizing: border-box;
}


.status {
  padding: 50rpx 20rpx;
  font-size: 40rpx;
  font-weight: bold;
  background: #07c160;
  border-radius: 20rpx;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.part {
  border-radius: 20rpx;
  background-color: #ffffff;
  padding: 16rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.debt_order_detail {
  background-color: #fffcf7;
}

.order_status {
  width: 100%;
  height: 150rpx;
  line-height: 100rpx;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #fd9b1a;
  color: #ffffff;
  font-size: 36rpx;
}

.pay_tips {
  font-size: 28rpx;
  color: #fd9b1a;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 8rpx;
  background-color: #FFFCF7;
}

.receiving_info {
  padding: 30rpx 30rpx 0 30rpx;
  font-size: 24rpx;
  color: #666666;
}


.pickup_point {
  padding: 16rpx;
}


.pickup_point_cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}

.pickup_point {
  display: flex;
  justify-content: space-between;
}


.pay_content_wrap {
  box-sizing: border-box;
}

.goods_cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}

.goods_content {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.goods_base_param {
  width: calc(100% - 180rpx);
}

.goods_base_params {
  width: calc(100% - 180rpx);
}

.goods_base_params view:nth-child(n+2) {
  font-size: 24rpx;
  color: #666666;
  /* margin-top: 10rpx; */
}

.debt_all_amount {
  display: flex;
  justify-content: flex-end;
  font-size: 32rpx;

}

.color {
  color: #666666;
}


.order_detail {
  margin-top: 50rpx;
  font-size: 24rpx;
  /* color: #666666; */
}

.apply_service {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
  align-items: center;
}

.apply_service view:nth-child(1) {
  font-size: 22rpx;
  color: #0c86da;
}

.sort_refund_detail {
  padding: 0rpx 30rpx 30rpx 30rpx;
  box-sizing: border-box;
}

.sort_refund_detail view {
  font-size: 28rpx;
  margin-top: 15rpx;
  color: #666666;
}

.van-action-sheet__header {
  font-weight: bold !important;
}

.weight {
  border-radius: 6rpx;
  font-size: 20rpx;
  padding: 0rpx 6rpx;
  color: #07c160;
  border: 1rpx solid #07c160;

}

.settle {
  font-size: 22rpx;
  margin-left: 20rpx;
  color: #f89494;
}

.per {
  border-radius: 6rpx;
  border: 1rpx solid #cfcfcf;
  white-space: nowrap;
  color: #cfcfcf;
  padding: 0 6rpx;
  font-size: 22rpx;
  text-align: center;
}

.operation_wrap {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}


.refund_detail {
  display: flex;
  justify-content: flex-end;
}

.refund_detail view {
  font-size: 28rpx;
  color: #ffffff;
  background-color: #fd9b1a;
  padding: 4rpx 10rpx;
  box-sizing: border-box;
  border-radius: 44rpx;
}

.receipt_img_list {
  padding: 30rpx;
}


/*  订单信息 */
.info {
  margin-top: 20rpx;
}

.info .title-line {
  font-size: 26rpx;
  margin-bottom: 12rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.leftTitle {
  width: 19%;
  display: inline-block;
  white-space: nowrap;
}

.tipOne {
  text-align: right;
  font-size: 22rpx;
  color: #666666;
}

.info .tip {
  font-size: 22rpx;
  color: #666666;
}

.chart {
  font-size: 22rpx;
  border: 1rpx solid #fd9b1a;
  display: inline-block;
  border-radius: 10rpx;
  padding: 0 10rpx;
  height: 34rpx;
  color: #fd9b1a;
}

.Delivery {
  display: flex;
  align-content: center;
}

.bottomBtn {
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 20rpx 30rpx calc(env(safe-area-inset-bottom) + 20rpx) 30rpx;
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  border-top: 1px solid #f1f0f0;
}

.son {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.bottomSure {
  width: 180rpx;
  height: 70rpx;
  text-align: center;
  line-height: 70rpx;
  border-radius: 40rpx;
  /* background-color: #f0bd5f; */
  border: 1rpx solid #e65e16;
  color: #e65e16;
  font-size: 36rpx;
}

.bottom-cancel {
  width: 180rpx;
  height: 70rpx;
  text-align: center;
  line-height: 70rpx;
  border-radius: 40rpx;
  box-sizing: border-box;
  border: 1rpx solid #858585;
  font-size: 36rpx;
}

.bottom-debt {
  width: 130rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
  border-radius: 20rpx;
  background-color: #f8983d;
  color: #fff;
  font-size: 32rpx;
  margin-left: 20rpx;
}

.upload_wrap {
  padding: 30rpx;
}

.uplaod_content_wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.uplaod_content {
  width: 160rpx;
  height: 160rpx;
  border: 1rpx solid #e5e5e5;
  margin: 10rpx;
  position: relative;
  border-radius: 16rpx;
  /* border: 1px solid red; */
}

.cancel {
  width: 30rpx;
  position: absolute;
  padding: 10rpx;
  right: 0rpx;
  top: 0rpx;
  z-index: 9;
}

.uplaod_img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}


.upload_button {
  width: 160rpx;
  height: 160rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.upload_button image {
  width: 60rpx;
  height: auto;
}

.submit {
  position: fixed;
  bottom: 0rpx;
  text-align: center;
  /* border: 1px solid red; */
  width: 100%;
  height: 200rpx;
  line-height: 200rpx;
  background-color: #fff;
}

.submit button {
  width: 300rpx;
  margin-top: 50rpx;
}

.toast {
  color: #808080;
  margin: 50rpx 0;
  font-size: 28rpx;
}


.btn {
  /* justify-content: flex-end; */
  justify-content: center;
  display: flex;
}

.save {
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  width: 60%;
  background-color: #f0a12b;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.ToastText {
  color: red;
  font-size: 24rpx;
}

.text {
  flex: 1;
  display: flex;
}

.word {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  margin-right: 10rpx;
  text-align: right;
  flex: 1;
}


.modalDlg {
  margin: 0 auto;
  padding: 55rpx;
  padding-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  /* display: flex; */
  /* flex-direction: column; */
  /* align-items: center; */
}

.input1 {
  width: 100%;
  background-color: red;
  border: 3rpx solid #faca82;
}

/* .view-contain-ti {
  display: flex;
  height: 40px;
  margin: 20px 0;
  border: 3rpx solid #faca82;
  border-radius: 10rpx;
} */

.text-ti {
  position: absolute;
  font-size: 12px;
  background: white;
  margin: -10px 0 0 10px;
  padding: 0 5px;
  color: rgb(144, 147, 167);
}

.cancelbnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: #07c060;
  margin-right: 20rpx;
}

.wishbnt {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
}

.goodsPrice {
  color: #ff5000;
  font-weight: bold;
}


.step-desc {
  color: #464646 !important;
}

.footer_operation {
  width: 100%;
  height: 160rpx;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  z-index: 9;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 999;
}

.subsidy-tag {
  color: #cc785b;
  background-color: #fce3cc;
  font-size: 24rpx;
  padding: 2rpx 6rpx;
  border-radius: 10rpx;
  /* font-weight: bold; */
}

.refund {
  background-color: #fff;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  padding: 20rpx;
}

.debt {
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
  background-color: #fff;
  padding: 0 20rpx;
}

.refund-list {
  border: 1px solid #eee;
  margin-bottom: 10rpx;
  padding: 10rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  gap: 20rpx;
  margin-top: 10rpx;
}

.debt-list {
  border: 1px solid #eee;
  margin-bottom: 10rpx;
  padding: 10rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  gap: 20rpx;
  margin-top: 10rpx;
}

.btn-content {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: orange;
  margin-top: 10rpx;
}

.action-snapshot{
  height: 500rpx;
  overflow-y: scroll;
}

.snapshot {
  margin: 20rpx;
  display: flex;
}

.img {
  width: 140rpx;
  height: auto;
  border-radius: 20rpx;
  margin-right: 10rpx;
}
.right-info{
  height: 130rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}
.s-btn{
  display: flex;
  justify-content: space-between;
  color: #666666;
  align-items: center;
}

.snapshot-btn{
  border: 1rpx solid #8a8a8a;
  font-size: 24rpx;
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
}