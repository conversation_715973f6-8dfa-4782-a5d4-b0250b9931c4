


const app = getApp()
import {
  adjust_settle_get
} from '../../../../../apis/servicePoint/center';
import {
  dealTime,
} from '../../../../../utils/check';
Component({

  properties: {
    orderId: {
      type: String,
      value: ''
    }
  },

  lifetimes: {
    ready: function() {
      this.getSettle(this.properties.orderId)
    },
  },

  data: {
    settle_data: null,
    color: '',
    imgUrl: app.globalData.imageUrl,
  },

  methods: {
    getSettle(id) {
      let data = {
        order_id:id
      }
      adjust_settle_get(data).then(res => {
        if (res.data.code == 0) {
          let data = res.data.data
          data.status_text = this.statusText(data.status)
          data.total_amount_fmt = (data.total_amount / 100).toFixed(2)
          data.updated_at_fmt = dealTime(data.updated_at)
          data.product_list.forEach(item => {
            item.order_amount_fmt = (item.order_amount / 100).toFixed(2)
            item.adjust_amount_fmt = (item.adjust_amount / 100).toFixed(2)
          })
          this.setData({
            settle_data: data
          })
        }
      }).catch(err => {
        wx.showToast({
          title: err.message,
          icon: 'none'
        })
      })
    },

    statusText(e) {
      let text = ''
      let color = ''
      switch (e) {
        case 'draft':
          text = '草稿'
          color = "#626063"
          break;
        case 'confirmed':
          text = '已确认'
          color = "#b8c43a"
          break;
        case 'refunding':
          text = '退款中'
          color = "#b55233"
          break;
        case 'completed':
          text = '已完成'
          color = "#2bb232"
          break;
        case 'failed':
          text = '退款失败'
          color = "#e8373d"
          break;
        case 'closed':
          text = '已关闭'
          color = "#a29779"
          break;
      }
      this.setData({
        color: color
      })
      return text
    },
  }
})