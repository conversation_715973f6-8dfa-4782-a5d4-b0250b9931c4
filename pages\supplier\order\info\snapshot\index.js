const app = getApp()
import {
  order_product_snapshot
} from '../../../../../apis/order';
import {
  displayProcess,
} from '../../../../../utils/dict';
Page({

  data: {
    order_id: '',
    product_id: '',
    imgUrl: app.globalData.imageUrl,
    snapshot_info: null,
    imageProcess: displayProcess,
  },

  onLoad(options) {
    this.setData({
      order_id: options.orderId,
      product_id: options.productId
    })

    this.getSnapshot()
  },
  getSnapshot() {
    let data = {
      order_id: this.data.order_id,
      product_id: this.data.product_id
    }
    order_product_snapshot(data).then(res => {
      if (res.data.code == 0) {
        let info = res.data.data.product
        info.start_price_fmt =  (info.start_price / 100).toFixed(2)
        if (info.sku_list) {
          info.sku_list.forEach(ele => {
            ele.price_fmt = (ele.price / 100).toFixed(2)
            ele.estimate_purchase_price_fmt = (ele.estimate_purchase_price / 100).toFixed(2) //采购价
            ele.market_wholesale_price_fmt = (ele.market_wholesale_price / 100).toFixed(2)
            ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(1)
            ele.net_weight_fmt = (ele.net_weight / 1000).toFixed(1)
            ele.out_weight_fmt = (ele.out_weight / 1000).toFixed(1)
            ele.unit_price_fmt = ((ele.price / ele.rough_weight) * 10).toFixed(1)
          })
        }
        this.setData({
          snapshot_info: info,
        })

      }

    })
  },

  //轮播图切换下标
  swiperChange(e) {
    let index = e.detail.current + 1
    this.setData({
      dot: index,
    })
  },

  calcImageHeight(e) {
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = winWid * imgh / imgw //等比设置swiper的高度。  
    //即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度  -->  swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    this.setData({
      swiperHeight: swiperH //设置swiper高度
    })
  },

  handleSeeNew() {
    let id = this.data.snapshot_info.id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  },

  onShow() {

  },

})