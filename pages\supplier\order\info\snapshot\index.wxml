<view class="container">

<view class="carousel">
  <swiper circular="{{true}}" current="{{0}}" autoplay="{{true}}" indicator-dots="{{true}}" interval="2000" duration="1000" bindchange="swiperChange" style="height: 100%;">
    <block wx:for="{{snapshot_info.display_file}}" wx:key="name">
      <swiper-item>
        <image webp="{{true}}" show-menu-by-longpress="true" wx:if="{{item.type=='image'}}" src="{{item.name?imgUrl+imageProcess+item.name:''}}" mode="widthFix" data-info="{{item}}" style="width:100%;height: auto;" bindload="calcImageHeight"></image>
        <video enable-play-gesture="{{true}}" object-fit="cover" show-mute-btn="{{true}}" show-fullscreen-btn="{{true}}" show-center-play-btn="{{true}}" controls="{{true}}" style="width:100%;height: {{swiperHeight}}px;" bindplay="videoPlay" bindpause="videoPause" bindended="videoEnd" wx:if="{{item.type=='video'&&item.name}}" src="{{item.name?imgUrl+item.name:''}}"></video>
      </swiper-item>
    </block>
  </swiper>
</view>

<view class="box">
  <view class="price">
    <view style="font-weight: bold;font-size: 40rpx;">￥{{snapshot_info.start_price_fmt}}</view>
  </view>
  <view style="font-weight: bold;font-size: 28rpx;">{{snapshot_info.title}}</view>
  <view class="prompt">
    当前页面为订单快照，包含订单创建时的商品描述和下单信息，买卖双方和平台在发生交易争议时，将作为判定依据。
    <view style="color: red;" catch:tap="handleSeeNew">查看最新商品详情</view>
  </view>

</view>

<view style="background-color: #fff;margin-bottom: 20rpx; padding: 20rpx; border-radius: 20rpx;">
  <view class="sale_progress_wrap">
    <!-- 规格 -->
    <view>规格</view>
    <view wx:for="{{snapshot_info.sku_list}}" wx:key="index" class="sku" >
      <view style="display: flex;align-items: center;justify-content: space-between;font-weight: bold;">
        <view>{{item.name}}</view>
        <view style="color: red;">￥{{item.price_fmt}}</view>
      </view>
      <view style="display: flex;align-items: center;margin-top: 10rpx;font-size: 24rpx;gap: 20rpx;">
        <view>毛重：{{item.rough_weight_fmt}}kg</view>
        <view>皮重：{{item.out_weight_fmt}}kg</view>
        <view>净重：{{item.net_weight_fmt}}kg</view>
      </view>

    </view>
  </view>
</view>

<view class="box">
  <view class="title">商品介绍</view>
  <view style="font-size: 24rpx;color: #858484;">{{snapshot_info.desc}}</view>
</view>

<view class="box">
  <view class="title">规格参数</view>
  <view class="attr">
    <view class="border" >基本信息</view>
    <view wx:for="{{snapshot_info.attr_info}}" wx:key="index" class="border-item" style="{{index == snapshot_info.attr_info.length-1? 'border: 0':''}}">
      <view class="field">{{item.field}}</view>
      <view class="value">{{item.value}}</view>
    </view>
  </view>
</view>

</view>