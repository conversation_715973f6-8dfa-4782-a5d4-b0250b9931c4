.container {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.carousel {
  height: 750rpx;
  width: 100%;
}

.box {
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 16rpx;
}

.price {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  align-items: center;
  color: red;
}

.title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.prompt {
  font-size: 24rpx;
  background-color: #f5f6fa;
  color: #8b8b8b;
  line-height: 36rpx;
  padding: 10rpx;
  margin-top: 20rpx;
}

.desc {
  padding: 20rpx;
  background-color: #fff;
}

.attr {
  border: 1rpx solid #ebeaea;
  color: #707070;
  margin-bottom: 20rpx;
}

.border {
  border-bottom: 1rpx solid #ebeaea;
  display: flex;
  font-size: 24rpx;
  padding: 10rpx;
}

.border-item {
  border-bottom: 1rpx solid #ebeaea;
  display: flex;
  font-size: 24rpx;
  
}

.field {
  width:150rpx;
  border-right: 1rpx solid #ebeaea;
  white-space: nowrap;
  padding: 10rpx;
  display: flex;
  align-items: center;
  font-size: 22rpx;
}
.value{
  white-space: pre-wrap;
  flex: 1;
  display: flex;
  align-items: center;
  padding: 10rpx;
  font-size: 22rpx;
}
.sku {
  border: 1rpx solid #d6d5d5;
  padding: 10rpx;
  margin-top: 10rpx;
  border-radius: 10rpx;
}