const app = getApp()
import {
  reset_pwd
} from '../../../apis/servicePoint/center'
import { send_code } from '../../../utils/api'
import Toast from '@vant/weapp/toast/toast';
Page({

  data: {
    show: false,
    currentTime: "获取验证码", //倒计时
    mobile: '',
    pwd: '',
    captcha:''
  },

  onLoad(options) {
    this.setData({
      mobile: wx.getStorageSync('x_supplier').pay_mobile
    })

  },
  handlePwd() {
    this.setData({
      show: true,
      pwd: '',
      captcha:''
    })
  },

  //发送验证码
  sendCode() {
    let that = this
    let data = {
      mobile: wx.getStorageSync('x_supplier').pay_mobile
    }
    send_code(data).then(res => {
      if (res.data.code == 0) {
        that.verificationCode()
        Toast('短信验证码已发送');
      }
    })
  },

  // 验证码倒计时
  verificationCode() {
    let that = this
    let currentTime = 60
    //设置一分钟的倒计时
    var interval = setInterval(function () {
      currentTime--;
      //每执行一次让倒计时秒数减一
      that.setData({
        currentTime: currentTime + 's', //按钮文字变成倒计时对应秒数
        disabled: true
      })
      //如果当秒数小于等于0时 停止计时器 且按钮文字变成重新发送 
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          currentTime: '重新发送',
          disabled: false,
          color: 'red'
        })
      }
    }, 1000);
  },
  onChange(e) {
    this.setData({
      pwd: e.detail
    })
  },
  handleCancle() {
    this.setData({
      show: false
    })
  },
  // 手机验证码
  sms(e) {
    this.setData({
      captcha: e.detail
    })
  },
  submit() {
    let data = {
      mobile: this.data.mobile,
      captcha: this.data.captcha,
      pwd: this.data.pwd
    }
    reset_pwd(data).then(res => {
      if (res.data.code == 0) {
        wx.showToast({
          title: '更新密码成功',
          icon: "none",
          duration: 2000
        })
        this.setData({
          show: false
        })
      }
    }).catch(err => {

    })
  }
})