<view style="padding-top: 30rpx;">
  <!-- <view class="btn" bind:tap="handlePwd">重置密码</view> -->
  <van-cell is-link title="重置密码" bind:tap="handlePwd" />
</view>

<van-dialog use-slot title="密码" show="{{ show }}" show-confirm-button="{{false}}">
  <view style="margin-top: 20rpx;">
    <van-cell-group>
      <van-field label="电话号码" value="{{ mobile }}" disabled border="{{ false }}" />
    </van-cell-group>

    <van-field center clearable label="短信验证码" value="{{ captcha }}" placeholder="请输入短信验证码" bind:change="sms" use-button-slot>
      <van-button slot="button" maxlength="6" size="small" type="primary" bind:click="sendCode">
        {{currentTime}}
      </van-button>
    </van-field>


    <van-cell-group>
      <van-field value="{{ pwd }}" label="新密码" placeholder="请输入新密码" border="{{ false }}" bind:change="onChange" />
    </van-cell-group>
  </view>

  <view class="button">
    <view class="cancle" bind:tap="handleCancle">取消</view>
    <view class="sure" bind:tap="submit">提交</view>
  </view>
</van-dialog>

<van-toast id="van-toast" />