<!--pages/supplier/productManagement/productAddition/components/base_param/index.wxml-->

<view class="">
  <view wx:for="{{formData}}" wx:key="key">
    <van-field value="{{ item.value }}" wx:if="{{item.field == '产品描述'||item.field == '售后说明'}}" label="{{item.field}}" type="textarea" placeholder="请输入" autosize  bind:change="inputContent" data-index="{{index}}" />
    <van-field value="{{ item.value }}" wx:else label="{{item.field}}" bind:change="inputContent" data-index="{{index}}" placeholder="请输入" input-class="input-class" />
  </view>
</view>