// pages/supplier/productManagement/productAddition/components/classification/index.js
import {
  category_list_main,
  class_list_next,
} from '../../../../../../utils/api';

const app = getApp()


Component({
  /**
   * 组件的属性列表
   */
  properties: {
    classList:{
      type:Array,
      value:""
    },
    level:{
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    fieldNames: {
      text: 'name',
      value: 'id',
    },
    classShow: false,
    options: [],
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //分类选择
    classifySelect() {
      let level = this.properties.level
      if(level == 'station'){
        return
      }
      category_list_main("2").then(res => {
        if (res.data.code == 0) {
          let arr = res.data.data
          arr.forEach(item => {
            let key = 'children'
            let value = []
            item[key] = value
          });
          this.setData({
            options: arr
          })
        }

      })

      this.setData({
        classShow: true
      })
    },

    // 关闭分类弹窗
    closeClassPop() {
      this.setData({
        classShow: false,
      })
    },

    nextLower(e) {
      let that = this
      let selectList = e.detail.selectedOptions
      let datass = []
      let classList = []
      selectList.map(res => {
        datass.push(res.id),
          classList.push(res.name)
      })
      this.setData({
        classList: classList
      })
      class_list_next(e.detail.value, 2).then(res => {
        if (res.data.code == 0) {
          let datas = res.data.data
          if (res.data.data.length > 0 && e.detail.tabIndex == 0) {
            res.data.data.forEach(item => {
              // console.log(item, 5656, e.detail.value)
              if (item.parent_id == e.detail.value) {
                let key = 'children'
                let value = []
                item[key] = value
              }
            });
            let newList = []
            that.data.options.forEach(response => {
              if (e.detail.value == response.id) {
                res.data.data.forEach(res => {
                  if (res.is_special == false) {
                    newList.push(res)
                  }
                })
                response.children = newList
              }
            })

          } else if (res.data.data.length > 0 && e.detail.tabIndex == 1) {
            that.data.options.forEach(response => {
              response.children.forEach(res => {
                if (res.id == selectList[1].id) {
                  res.children = datas
                }

              })
            })

          } else {
            this.setData({
              classShow: false
            })
          }
          that.setData({
            options: that.data.options,
            cascaderValue: e.detail.value,
          })

          this.triggerEvent("nextLower", {datass:datass,classList:classList})

        }


      })
    },
  }
})