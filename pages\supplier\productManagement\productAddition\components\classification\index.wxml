<!--pages/supplier/productManagement/productAddition/components/classification/index.wxml-->

<view style="width: 100%;">
  <!-- <filed-input label="分类"  disabled="true"  value="{{classList}}" placeholder="请选择分类" bindtap="classifySelect" isIcon="true"></filed-input> -->

  <view style="display: flex;width: 100%;justify-content: space-between;align-items: center;padding-left: 10rpx;" bind:tap="classifySelect">
    <view wx:if="{{classList&&classList.length>0}}">{{classList}}</view>
    <view wx:else style="color: #808080;font-size: 22rpx;" bind:tap="classifySelect">请选择</view>
    <image style="width: 40rpx;height: auto;" src="{{imageUrl+'icon/right-slim.png'}}" mode="widthFix" bind:tap="classifySelect" />
  </view>
</view>

<!-- 分类选择 -->
<van-popup show="{{ classShow }}" round position="bottom">
  <van-cascader value="{{cascaderValue}}" bind:finish="finishClass" title="请选择分类" options="{{ options }}" field-names="{{ fieldNames }}" bind:close="closeClassPop" bind:change="nextLower" bind:finish="onFinish" />
</van-popup>