// pages/supplier/productManagement/productAddition/components/grade-fruit/index.js
import {
  fruit_class,
} from '../../../../../../utils/api';

const app = getApp()

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    categoryid:{
      type:Array,
      value:[]
    },
    gradeName:{
      type:String,
      value:[]
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    gradeShow: false, //等级弹窗显示
    gradeList: [], //等级列表
    gradeName: "", //等级名称
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 动作面板
    gradeActions() {
      if (this.data.categoryid.length > 0) {
        let data = {
          category_id:this.data.categoryid[1],
        }
        fruit_class(data).then(res => {
          console.log(res)
          if (res.data.code == 0) {
            // let datas = {
            //   id: "",
            //   name: "无"
            // }
            // let newList = res.data.data.push(datas)
            this.setData({
              gradeList: res.data.data,
              gradeShow: true
            })
  
          }
        })
      } else {
        wx.showToast({
          title: '请先选择分类',
          icon:"none"
        })
      }
    },
  
    closeGradeActions(e) {
      this.setData({
        gradeShow: false
      })
    },
  
    
    gradeSelect(e) {
      this.setData({
        // "formData.non_standard_attr.fruit_class_id": e.detail.id,
        // "formData.non_standard_attr.fruit_class_name": e.detail.name,
        gradeShow: false,
        gradeName: e.detail.name
      })
      let name = e.detail.name
      let id = e.detail.id
    this.triggerEvent("gradeSelect",{name,id})
    },
  }
})
