import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';

Component({
  properties: {
    discount_price_list: {
      type: Array,
      value: [],
    },
    price: {
      type: Number,
      value: 0,
    },
    price_fmt: {
      type: Number,
      value: 0,
    },
    product_id: {
      type: String,
      value: '',
      observer(val) {
        if(val!==''){
          this.init()
        }
     },
    },
    index: {
      type: Number,
      value: 0,
    },
    show:{
      type:Boolean,
      value:false,
    }

  },

  data: {
    price: 0,
    price_fmt: 0,
    list: [],
    show: false,
  },

  methods: {
    toEdit() {
      this.setData({
        show: true,
      })
    },

    onClose() {
      this.setData({
        show: false,
      })
    },

    // 添加
    addInfo() {
      let list = this.data.list
      let item = {
        num: 2,
        price: 0,
        discount: 0,
        price_fmt: 0,
      }
      list.push(item)
      this.setData({
        list: list,
      })
    },

    calcDiscountByPrice() {
      //  计算折扣
      let list = this.data.list
      let price = this.data.price

      if (list) {
        list.forEach(item => {
          console.log(item);
          if (item.price != 0) {
            let discount = parseInt((item.price / price) * 100)
            item.discount = discount
            item.price_fmt = this.dealMoney(item.price)
            item.warning = false
            if (discount < 90) {
              item.warning = true
            }
          }
        });
      }

      this.setData({
        list: list,
      })
    },

    dealMoney(fen) {
      return Math.round(fen) / 100
    },

    calcByDiscount() {
      let list = this.data.list
      let price = this.data.price
      if (list) {
        list.forEach(item => {
          let price_fmt = this.dealMoney((item.discount * price) / 100)
          item.price_fmt = price_fmt
          item.price = parseInt(price_fmt * 100)
          item.warning = false
        });
      }

      this.setData({
        list: list,
      })
    },

    // 删除
    delete(e) {
      let i = e.currentTarget.dataset.index
      let list = this.data.list
      list = list.filter((item, index) => {
        return index !== i
      })

      this.setData({
        list: list,
      })
    },


    // 编辑数量
    inputNum(e) {
      let num = e.detail.value
      let index = e.currentTarget.dataset.index
      if (num === '') {
        return
      }
      num = parseInt(num)
      let list = this.data.list
      list.forEach((item, i) => {
        if (index == i) {
          item.num = num
        }
      })
      this.setData({
        list
      })
    },

    // 编辑折扣
    inputDiscount(e) {
      let v = e.detail.value.replace(/\s+/g, '')
      if (v === '') {
        return
      }
      let i = e.currentTarget.dataset.index
      //  格式化
      let discount = parseInt(v)
      if (discount < 90 || discount > 99) {
        Toast("折扣最低90")
        discount = 90
      }
      let list = this.data.list
      list.forEach((item, index) => {
        if (index == i) {
          item.discount = discount
        }
      })
      this.setData({
        list
      })
console.log(list);
      this.calcByDiscount()
    },

    blurPrice(e) {
      let v = e.detail.value.replace(/\s+/g, '')
      if (v == "") {
        Toast("请输入价格")
      }
      v = this.splitDotTwo(v)
      let price_fmt = v
      let price = parseInt(price_fmt * 100)
      this.setData({
        price_fmt: price_fmt,
        price: price
      })

      this. calcDiscountByPrice()
    },


    splitDotTwo(e) {
      let price = e.toString().replace(/\s+/g, '')
      if (price == '') {
        return 0
      }
      price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
      price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
      price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      price = price.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
      if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        price = parseFloat(price);
      }
      return price
    },

    // 编辑折扣价
    inputDiscountPrice(e) {
      let v = e.detail.value.replace(/\s+/g, '')
      let i = e.currentTarget.dataset.index
      if (v === '') {
        return
      }
      let price_fmt = this.handleInputTwo(v)
      let price = parseInt(price_fmt * 100)
      let list = this.data.list
      list.forEach((item, index) => {
        if (index == i) {
          item.price = price
          item.price_fmt = price_fmt
        }
      })
      this.setData({
        list: list
      })

      //  计算折扣
      this.calcDiscountByPrice()
    },

    handleInput(e) {
      let price = e
      price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
      price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
      price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      price = price.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3'); //只能输入一个小数
      if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        price = parseFloat(price);
      }
      return price
    },

    handleInputTwo(e) {
      let price = e
      price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
      price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
      price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      price = price.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
      if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        price = parseFloat(price);
      }
      return price
    },

    check(list) {

      console.log(list, 121);

      let numSet = new Set()
      list.forEach(item => {
        numSet.add(item.num)
      })
      if (numSet.size != list.length) {
        Toast('数量不能相同')
        return false
      }

      let valueSet = new Set()
      list.forEach(item => {
        valueSet.add(item.discount)
      })
      if (valueSet.size != list.length) {
        Toast('折扣不能相同')
        return false
      }

      const isNum = list.every(ele => (ele.num !== '' && ele.num > 1))
      if (!isNum) {
        Toast('数量不合理')
        return false
      }
      const isValue = list.every(ele => (90 <= ele.discount && ele.discount <= 99))
      if (!isValue) {
        Toast('折扣不合理')
        return false
      }
      return true
    },

    save() {
      setTimeout(() => {
        //  保存
        let list = this.data.list
        let f = this.check(list)
        if (!f) {
          return
        }
        let price = this.data.price
        console.log(price, 909);
        if (price < 1) {
          Toast("请输入价格")
          return
        }

        let data = {
          discount_price_list: list,
          origin_price: origin_price,
          origin_price_fmt: origin_price_fmt,
          price: price,
          price_fmt: this.data.price_fmt,
          product_id: this.properties.product_id,
          index: this.properties.index,
        }

        this.triggerEvent("backPrice", data)

        // this.setData({
        //   show: false,
        // })
      }, 200);
    },

    init(){
      console.log(this.properties, 111);
      let list = this.properties.discount_price_list
      if (!list) {
        list = []
      }
      let price_fmt = this.properties.price_fmt
      let price = parseInt(this.properties.price)
      this.setData({
        price,
        price_fmt,
        list,
      })
      this.calcDiscountByPrice()
    }
  },

  lifetimes: {
    attached: function () {
    
    }
  }
})