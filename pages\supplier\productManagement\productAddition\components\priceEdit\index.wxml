<view class="container">
  <!-- 列表-展示价格-->
  <view style="margin-right: 10rpx; display: flex;">
    <!-- <text>价格：</text> -->
    <text>会员价：</text>
    <view style="border-bottom: 1rpx solid #ffac13; text-align: center; padding: 0 10rpx; color: #ffac13;"  bind:tap="toEdit"> {{price_fmt}}</view>
  </view>
  <!-- <van-button type="primary" bind:tap="toEdit" size="mini">点击编辑</van-button> -->
  <!-- 编辑-展示价格和折扣 -->
</view>

<van-dialog use-slot show="{{ show }}" closeOnClickOverlay showConfirmButton="{{false}}" bind:close="onClose" z-index="1000">
  <view class="dailogContent">
    <view class="top">
      <view style="margin-right: 20rpx;">价格</view>
      <input class="edit-input" type="digit" bindblur="blurPrice" value="{{price_fmt }}" style="width: 150rpx; margin: 20rpx 0;" />
    </view>
    <van-divider contentPosition="left" customStyle="color: #1989fa; border-color: #1989fa; font-size: 14px;">
      折扣价列表（非必填）
    </van-divider>

    <view class="projectTitle">
      <view style="width: 150rpx;">数量</view>
      <view style="width: 150rpx;">价格</view>
      <view style="width: 150rpx;">折扣</view>
    </view>

    <block wx:for="{{list}}" wx:key="num">
      <view style="display: flex; align-items: center; position: relative;">
        <view style="display: flex; ">
          <!-- 数量 -->
          <input class="edit-input" type="number" data-index="{{index}}" bindinput="inputNum" value="{{ item.num}}" style="width: 100rpx;  margin:20rpx 25rpx 20rpx 0;" />
          <input class="edit-input" type="digit" data-index="{{index}}" bindblur="inputDiscountPrice" value="{{item.price_fmt }}" style="width: 100rpx; margin: 20rpx 0;" />
          <view>
            <input class="edit-input" type="number" data-index="{{index}}" bindblur="inputDiscount" value="{{ item.discount}}" style="width: 100rpx; margin:20rpx 25rpx 20rpx 0;" />
            <view wx:if="{{item.warning == true}}" style="font-size: 16rpx; color: red;">* 折扣不合理</view>
          </view>
        </view>
        <view class="del" bind:tap="delete" data-index="{{index}}">删除</view>
      </view>
    </block>
    <view class="add" bind:tap="addInfo" wx:if="{{list.length < 2}}">添加折扣</view>
    <view style="margin-top:50rpx">
      <view style="font-size: 24rpx; color: red;">
        注：
      </view>
      <view style="text-indent: 1rem;">
        <view style="font-size: 24rpx; color: red;">1. 数量范围：大于1且不相等</view>
        <view style="font-size: 24rpx; color: red;">2. 价格需要大于基准价</view>
        <view style="font-size: 24rpx; color: red;">3. 折扣四舍五入向下取整，仅参考</view>
        <view style="font-size: 24rpx; color: red;">4. 折扣区间限制90至99</view>
      </view>
    </view>

  </view>


  <view class="bottom" style="width: 100%;display: inline-flex;">
    <view style="width:100rpx" class='cancelbnt' bindtap='onClose'>取消</view>
    <view style="width:60%" class='wishbnt' bindtap='save'>确定</view>
  </view>

</van-dialog>

<van-toast id="van-toast" zIndex="999" />