/* pages/supplier/productManagement/productAddition/discount/index.wxss */
.container{
  /* padding: 20rpx; */
  box-sizing: border-box;
  display: flex;
}

.dailogContent{
  padding: 20rpx;
  padding-top: 0;
}

.top{
  display: flex;
  align-items: center;
}
.title{
  margin-bottom: 20rpx;
}
.projectTitle{
  display: flex;
  font-size: 24rpx;
}
.edit-input {
  border: 1rpx solid #d6d4d4;
  margin-top: 6rpx;
  border-radius: 10rpx;
  padding: 10rpx;
  color: #5f5f5f;
}
.del {
  font-size: 24rpx;
  border: 1px solid #dfdbdb;
  text-align: center;
  margin: 10rpx 5rpx 25rpx 20rpx;
  border-radius: 10rpx;
  padding: 10rpx;
  box-sizing: border-box;
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}
.add {
  border: 1px solid #b6b4b4;
  width: 200rpx;
  padding: 8rpx 6rpx;
  box-sizing: border-box;
  text-align: center;
  border-radius: 10rpx;
  font-size: 24rpx;
  margin-top: 60rpx;
}

.ok {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
  /* margin:  0 20rpx; */
  box-sizing: border-box;
  width: 100%;
}

.bottom {
  background-color: #fff;
  width: 100%;
  margin-top: 20rpx;
  padding: 20rpx ;
  box-sizing: border-box;
  z-index: 2;
}

.cancelbnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: #07c060;
  margin-right: 20rpx;
}

.wishbnt {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
}

