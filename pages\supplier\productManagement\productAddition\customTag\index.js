// pages/supplier/productManagement/productAddition/custom/index.js
import Toast from '@vant/weapp/toast/toast';
Page({

  data: {
    custom_tag_list: [{
      key: '',
      value: '',
      id:1
    }]
  },

  onLoad(options) {
    let custom_tag_list = JSON.parse(options.custom_tag_list)
    if(!custom_tag_list){
      custom_tag_list=[]
    }
    this.setData({
      custom_tag_list: custom_tag_list,
    })

  },

  addTag() {
    let item = {
      key: '',
      value: '',
      id: this.data.custom_tag_list.length + 1
    }
    let list = this.data.custom_tag_list
    list.push(item)
    this.setData({
      custom_tag_list: list
    })
  },


  // 删除
  delete(e) {
    let i = e.currentTarget.dataset.index
    let list = this.data.custom_tag_list
    list = list.filter((item, index) => {
      return index !== i
    })

    this.setData({
      custom_tag_list: list,
    })
  },
  // 编辑
  inputTitle(e) {
    let title = e.detail.value
    let index = e.currentTarget.dataset.index
    if (title === '') {
      return
    }
    let list = this.data.custom_tag_list
    list.forEach((item, i) => {
      if (index == i) {
        item.key = title
      }
    })
    this.setData({
      custom_tag_list: list
    })
    console.log(this.data.custom_tag_list)
  },

  // 编辑内容
  inputContent(e) {
    let value = e.detail.value
    let index = e.currentTarget.dataset.index
    if (value === '') {
      return
    }
    let list = this.data.custom_tag_list
    list.forEach((item, i) => {
      if (index == i) {
        item.value = value
      }
    })
    this.setData({
      custom_tag_list: list
    })
  },


  check(list) {
    const title = list.every(ele => (ele.key !== ''))
    if (!title) {
      Toast('请填写标题')
      return false
    }

    let numSet = new Set()
    list.forEach(item => {
      numSet.add(item.key)
    })
    if (numSet.size != list.length) {
      Toast('标题不能相同')
      return false
    }

    const value = list.every(ele => (ele.value !== ''))
    if (!value) {
      Toast('请填写内容')
      return false
    }
  
    return true
  },

  save() {
    //  保存
    let list = this.data.custom_tag_list
    let f = this.check(list)
    if (!f) {
      return
    }
    // 上一级页面参数
    let pages = getCurrentPages()
    let prevPage = pages[pages.length - 2];
    prevPage.setData({
      'formData.custom_tag_list': list,
    })
    wx.navigateBack()
  },
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})