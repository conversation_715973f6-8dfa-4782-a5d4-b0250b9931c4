<view class="container">

  <view style="width: calc(100% - 120rpx - 30rpx);display: flex;gap: 30rpx; margin-bottom: 30rpx;justify-content: space-around;">
    <view>标题</view>
    <view>内容</view>
  </view>
  <view wx:for="{{custom_tag_list}}" wx:key="index">
    <view style="display: flex; margin-bottom: 20rpx;gap: 30rpx;align-items: center;">
      <input class="input" data-index="{{index}}" bindinput="inputTitle" value="{{ item.key}}" maxlength="4" placeholder="请输入" />
      <input class="input" data-index="{{index}}" bindinput="inputContent" value="{{ item.value}}" maxlength="4" placeholder="请输入" />
      <view class="del" data-index="{{index}}" bind:tap="delete">删除</view>
    </view>
  </view>
  <view wx:if="{{custom_tag_list.length < 4}}" class="add" bind:tap="addTag">添加标签</view>


  <view style="margin-top: 160rpx;">
    <view style="font-weight: bold;">
      注:
    </view>
    <view style="font-size: 26rpx;">
      <view>
        1. 标题和内容长度最多4个字符
      </view>
      <view>
        2. 标题和内容不能包含空格
      </view>
    </view>
  </view>
  
</view>

<view class="bottom">
  <view class="ok" bind:tap="save">
    完成
  </view>
</view>

<van-toast id="van-toast" />