import Toast from '@vant/weapp/toast/toast';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    skuList: [], // 规格列表
    list: [], // 
    numList: [],
    numListIndex: 0,
    show: false,
    index: 0,
    valueNum: 0,
    priceList: [],
    toast: false
  },
  onLoad(options) {
    let discount_list = JSON.parse(options.discount_list)
    let skuList = JSON.parse(options.skuList)
    if (!skuList || skuList.length == 0) {
      wx.navigateBack()
      return
    }
    this.setData({
      list: discount_list,
      skuList: skuList,
    })

    this.fmtDiscountList()
    this.calcByDiscount()
  },


  fmtDiscountList() {
    let list = this.data.list
    list.forEach(item => {
      item.value_fmt = (item.value / 10).toFixed(1)
    });
    this.setData({
      list: list,
    })
  },


  calcByDiscount() {
    //  计算折扣
    let skuList = this.data.skuList
    let list = this.data.list
    list.forEach(item => {
      let discount_price_list = []
      skuList.forEach(itemTwo => {
        let origin = itemTwo.price * item.value / 100 / 100
        let price_fmt = origin.toFixed(2)
        discount_price_list.push(price_fmt)
      })
      item.discount_price_list = discount_price_list
    });

    this.setData({
      list: list,
    })
  },



  // 添加
  addInfo() {
    let list = this.data.list
    let item = {
      num: 2,
      value: 99,
      value_fmt: 9.9,
      discount_price_list:[0,0]
    }
    list.push(item)
    this.setData({
      list: list,
      valueNum: 0
    })

    this.calcByDiscount()
  },



  // 编辑数量
  inputNum(e) {
    let num = e.detail.value
    let index = e.currentTarget.dataset.index
    if (num === '') {
      return
    }
    num = parseInt(num)
    let list = this.data.list
    list.forEach((item, i) => {
      if (index == i) {
        item.num = num
      }
    })
    this.setData({
      list
    })
  },


  // 编辑折扣
  inputDiscount(e) {
    let v = e.detail.value
    if (v === '') {
      return
    }
    let i = e.currentTarget.dataset.index
    //  格式化
    let value_fmt = this.handleInput(e)
    let value = parseInt(value_fmt * 10)
    if (value < 90) {
      Toast("折扣最低9.0")
      value_fmt = '9.0'
      value = 90
    }
    let list = this.data.list
    list.forEach((item, index) => {
      if (index == i) {
        item.value = value
        item.value_fmt = value_fmt
      }
    })
    this.setData({
      list
    })

    this.calcByDiscount()
  },

  // 编辑折扣价
  inputDiscountPrice(e) {
    let i1 = e.currentTarget.dataset.index_one
    let i2 = e.currentTarget.dataset.index_two
    let v = e.detail.value
    if (v === '') {
      return
    }
    let discounts_price_fmt = this.handleInput(e)
    let discounts_price = parseInt(discounts_price_fmt * 100)
    let list = this.data.list
    //  计算折扣
    let origin_price = this.data.skuList[i2].price
    let discount = parseInt(discounts_price / origin_price * 100)
    let discount_fmt = (discount / 10).toFixed(1)
    list[i1].warning = false
    console.log(discount, discount_fmt, 999);
    if (discount < 90 || discount > 99) {
      // tip
      list[i1].warning = true
    }
    list[i1].value = discount
    list[i1].value_fmt = discount_fmt
    list[i1].discount_price_list[i2] = discounts_price_fmt
    //  其他sku的折扣价
    list[i1].discount_price_list.forEach((item, index) => {
      if (index != i2) {
        let op = this.data.skuList[index].price
        let p = parseInt(op * discount / 100)
        let p_fmt = (p / 100).toFixed(2)
        list[i1].discount_price_list[index] = p_fmt
      }
    });
    this.setData({
      list: list
    })
  },


  handleInput(e) {
    let price = e.detail.value.replace(/\s+/g, '')
    price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
    price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    price = price.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      price = parseFloat(price);
    }
    return price
  },


  // 删除
  delete(e) {
    let i = e.currentTarget.dataset.index
    let list = this.data.list
    list = list.filter((item, index) => {
      return index !== i
    })

    this.setData({
      list: list,
    })
  },


  check(list) {
    let numSet = new Set()
    list.forEach(item=>{
    numSet.add(item.num)
    })
    if(numSet.size!=list.length){
      Toast('数量不能相同')
      return false
    }

    const isNum = list.every(ele => ( ele.num !== '' && ele.num>1 ))
    if (!isNum) {
      Toast('数量不合理')
      return false
    }
    const isValue = list.every(ele => (90 <= ele.value && ele.value <= 99))
    if (!isValue) {
      Toast('折扣不合理')
      return false
    }
    return true
  },

  save() {
    //  保存
    let list = this.data.list
    let f = this.check(list)
    if (!f) {
      return
    }
    // 上一级页面参数
    let pages = getCurrentPages()
    let prevPage = pages[pages.length - 2];
    prevPage.setData({
      'formData.discount_List': list,
    })
    // return
    wx.navigateBack()
  },

  
})