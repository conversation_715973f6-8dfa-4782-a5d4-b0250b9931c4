<view class="container">
  <view class="tableHead">
    <view class="left">
      <view style="display: flex;height: 200rpx;">
        <view style="width: 150rpx; font-size: 26rpx; line-height: 70rpx;">规格</view>
        <view style="width: 80rpx; font-size: 26rpx; line-height: 70rpx;">价格</view>
        <!-- <view style="width: 50rpx; font-size: 26rpx; line-height: 70rpx;">
          <view style="font-size: 26rpx;">数量</view>
          <view style="font-size: 26rpx; line-height: 60rpx;" >折扣</view>
        </view> -->
      </view>
      <view>
        <!-- 规格 -->
        <view wx:for="{{skuList}}" wx:key="num" style="display: flex;margin-top: 40rpx;">
          <view class="name">{{item.name}}</view>
          <view class="price">{{item.price_fmt}}</view>
        </view>
      </view>
    </view>

    <view class="right">

      <block wx:for="{{list}}" wx:key="num">
        <view wx:if="{{index==0}}">
          <view style="font-size: 26rpx; line-height: 70rpx;">
            <view style="font-size: 20rpx;white-space: nowrap;margin:20rpx 0">数量</view>
            <view style="font-size: 20rpx;white-space: nowrap;margin:20rpx 0">折扣</view>
          </view>
        </view>
        <view style="display: flex;flex-direction: column;">
          <!-- 数量 -->
          <input class="edit-input" type="number" data-index="{{index}}" bindinput="inputNum" value="{{ item.num}}" style="width: 100rpx;margin:20rpx 0" />
          <!-- 折扣 -->
          <view style="position: relative;">
            <input class="edit-input" type="digit" data-index="{{index}}" bindinput="inputDiscount" value="{{ item.value_fmt}}" style="width: 100rpx;margin:20rpx 0" />
            <view wx:if="{{item.warning == true}}" style="font-size: 16rpx; color: red; position: absolute; bottom: 0;">* 折扣不合理</view>
          </view>

          <block wx:for="{{item.discount_price_list}}" wx:for-item="itemTwo" wx:for-index="indexTwo" wx:key="num">
            <input class="edit-input" type="digit" data-index_one="{{index}}" data-index_two="{{indexTwo}}" bindinput="inputDiscountPrice" value="{{ itemTwo}}" style="width: 100rpx;margin: 20rpx 0;" />
          </block>
          <view class="del" bind:tap="delete" data-index="{{index}}">删除</view>
        </view>
      </block>

    </view>
  </view>

  <view class="add" bind:tap="addInfo" wx:if="{{list.length <3}}">添加折扣</view>

  <view style="margin-top:200rpx">
    <view style="font-size: 24rpx; color: red;">
      注：
    </view>
    <view style="font-size: 24rpx; color: red;">1. 数量范围：大于1且不相等</view>
    <view style="font-size: 24rpx; color: red;"> 2. 折扣范围：9.0至9.9</view>

  </view>

</view>




<view class="bottom" wx:if="{{!enableCropper}}">
  <view class="ok" bind:tap="save">
    完成
  </view>
</view>

<van-dialog use-slot title="编辑数量" show="{{ show}}" show-confirm-button="{{false}}" zIndex="99">
  <view style="display: flex; align-items: center; margin: 20rpx;">
    <text>数量</text>
    <input model:value="{{ valueNum }}" type="number" bind:input="inputNum" placeholder="请输入数量" style="border-bottom: 1px solid #e0dfdf;margin-left: 20rpx; padding-bottom: 4rpx; font-weight: bold;" />
  </view>
  <view>
    <!-- 事件 -->
    <view style="width: 100%;display: inline-flex;justify-content: space-around;margin: 30rpx 0;">
      <view style="width:100rpx" class='cancelbnt' bindtap='onClose'>取消</view>
      <view style="width:60%" class='wishbnt' bindtap='editNum'>确定</view>
    </view>
  </view>
</van-dialog>


<van-toast id="van-toast" />