.container {
  background-color: #eee;
  padding: 20rpx;
  height: 100%;
}

.tableHead {
  width: 100%;
  display: flex;
  padding-bottom: 20rpx;
}

.name {
  width: 150rpx;
  /* margin-top: 10rpx; */
  font-size: 24rpx;
  /* overflow: hidden; */
  /* text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical; */
  /* word-break: break-all; */
}

.price {
  width: 80rpx;
  /* margin-top: 10rpx; */
  font-size: 24rpx;
  /* line-height: 60rpx; */
}





.tableHead .right {
  flex: 1;
  display: flex;
  gap: 20rpx;
}

.edit-input {
  border: 1rpx solid #d6d4d4;
  margin-top: 6rpx;
  border-radius: 10rpx;
  padding: 10rpx;
  color: #5f5f5f;
}

.del {
  font-size: 24rpx;
  border: 1px solid #dfdbdb;
  text-align: center;
  margin: 10rpx 5rpx 0 5rpx;
  border-radius: 10rpx;
  padding: 10rpx;
  box-sizing: border-box;
}

.add {
  border: 1px solid #b6b4b4;
  width: 200rpx;
  padding: 12rpx 8rpx;
  box-sizing: border-box;
  text-align: center;
  border-radius: 10rpx;
  font-size: 26rpx;
  margin-top: 30rpx;
}

.cancelbnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: #07c060;
  margin-right: 20rpx;
}

.wishbnt {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
}



/* save */
.ok {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
  /* margin:  0 20rpx; */
  box-sizing: border-box;
  width: 100%;
}

.bottom {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20rpx 20rpx 60rpx 20rpx;
  box-sizing: border-box;
  z-index: 2;
}