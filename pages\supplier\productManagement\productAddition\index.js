import {
  upload_sign,
  add_product,
  updata_product,
  unit_product,
  productPureInfo,
  search_category, //查询分类
  duplicateProduct,
  delete_product,
  productAuditInfo,
} from "../../../../utils/api";
const util = require("../../../../utils/util");
const uploadFile = require("../../../../utils/uploadFile");
import Toast from "@vant/weapp/toast/toast";
import Dialog from "@vant/weapp/dialog/dialog";

const app = getApp();

Page({
  data: {
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    typeList: [
      {
        id: 1,
        name: "水果类",
      },
      {
        id: 2,
        name: "其他类",
      },
    ],

    unitList: [],
    active: 0,
    classShow: false, //分类弹窗
    unitShow: false,
    fieldNames: {
      text: "name",
      value: "id",
    },

    formData: {
      id: "",
      product_param_type: 1, // 规格类型 1水果类 2其他类
      product_param_type_fmt: "fruit", // 水果
      has_param: true, // 是否单一商品 规格参数有无
      has_param_fmt: "yes", // 是否单一商品 规格参数有无
      is_check_weight: true, // 是否检查重量（涉及退款和补差
      is_check_weight_fmt: "check", // 检查
      title: "", // 商品标题
      desc: "", // 商品描述（副标题
      purchase_note: "", //采购描述
      category_ids: [], // 类目，三级id的数组
      product_unit_id: "", // 商品单位  1箱 2件 3框 4袋
      video_file: {},
      display_file: [], // 轮播图
      desc_img: [], // 详情
      sale: true, // 是否上架
      weight: {
        rough_weight: 0, //标品只有这个一个/ 毛重或重量
        out_weight: 0, // 皮重
        net_weight: 0, //
      }, // 重量信息
      attr_info: [
        {
          field: "产地",
          value: "",
        },
        {
          field: "包装",
          value: "",
        },
        {
          field: "仓储",
          value: "常温",
        },
        {
          field: "保质期",
          value: "2天",
        },
        {
          field: "不良率",
          value: "3%",
        },
        {
          field: "售后说明",
          value: "水烂、发霉，超出不良率可售后",
        },
      ], // 商品自定义参数
      product_origin_type: "domestic",
      sku_list: [],
    },
    carouselImgList: [], //轮播图图片列表
    detailImgList: [], //详情图片列表
    carouselVideoList: [], //轮播图视频列表
    autoplay: true,
    imageUrl: app.globalData.imageUrl,
    classList: [], //分类列表
    isView: false, //是否禁止输入
    uploadName: "",
    imgClassName: "", //上传图片分类name
    showOther: false,
    actions: [
      {
        name: "复制",
        value: "copy",
        subname: "放入仓库，标题存在前缀【复制】",
        color: "#1989fa",
      },
      {
        name: "删除",
        value: "delete",
        subname: "不可恢复",
        color: "#ee0a24",
      },
    ],
    id: "", // 商品ID
    submiting: false, // 提交中
    showEditPrice: false, // 编辑价格
    unit_price: "0", //单价
    sku_list_str: null,
    from: "",
  },

  onLoad(options) {
    let operate = options.operate;
    let from = options.from;
    let id = options.id;
    this.setData({
      operate: operate,
      from: from,
      id: id,
    });

    if (operate === "edit" && from === "list") {
      this.queryProductDetail(id);
    }

    if (operate === "edit" && from === "audit") {
      this.queryProductAudit();
    }

    this.unit();
  },

  back() {
    wx.showModal({
      title: "提示",
      content: "确定要离开页面吗？",
      success: function (res) {
        if (res.confirm) {
          wx.navigateBack();
        } else if (res.cancel) {
          console.log("用户点击取消，可以取消页面卸载操作");
        }
      },
    });
  },
  // 标题
  title(e) {
    let v = e.detail.value;
    this.setData({
      "formData.title": v,
    });
  },

  // 描述
  descInput(e) {
    let v = e.detail.value;
    this.setData({
      "formData.desc": v,
    });
  },

  // 产地
  changeProductOriginType(e) {
    let v = e.detail;
    this.setData({
      "formData.product_origin_type": v,
    });
  },

  //分类选择
  nextLower(e) {
    this.setData({
      "formData.category_ids": e.detail.datass,
      classList: e.detail.classList,
    });
    if (e.detail.datass[0] == "6450d00498426603acf0a074") {
      this.setData({
        "formData.product_param_type": 1,
        "formData.product_param_type_fmt": "fruit",
      });
    } else {
      this.setData({
        "formData.product_param_type": 2,
        "formData.product_param_type_fmt": "other",
      });
    }
  },
  //单位选择
  unitActions(e) {
    this.setData({
      unitShow: true,
    });
  },
  //单位选择
  unitSelet(e) {
    let id = e.detail.id;
    let name = e.detail.name;
    this.setData({
      unitShow: false,
      "formData.product_unit_id": id,
      "formData.product_unit_type_name": name,
    });
  },

  //关闭单位选择
  closeUnit(e) {
    this.setData({
      unitShow: false,
    });
  },

  // 是否检查商品重量计价方式
  isCheckWeight(e) {
    let v = e.detail;
    let param = "yes";
    let check = true;
    if (v == "check") {
      (check = true), (param = "yes");
    }
    if (v == "notCheck") {
      check = false;
    }

    this.setData({
      "formData.has_param_fmt": param,
      "formData.is_check_weight": check,
      "formData.is_check_weight_fmt": v,
      rough_weight_fmt_all: "0",
      "formData.weight.rough_weight": 0,
    });
  },

  // 规格

  toEditSku() {
    let category_ids = this.data.formData.category_ids;

    if (!category_ids || category_ids.length != 3) {
      Toast("请先选择分类");
      return;
    }
    let v = JSON.stringify(this.data.formData.sku_list);
    let type = this.data.formData.product_param_type;
    let has_param = this.data.formData.has_param;

    let param = `?listStr=${v}&productParamType=${type}&has_param=${has_param}`;
    wx.navigateTo({
      url:
        "/pages/supplier/productManagement/productAddition/skuSet/index" +
        param,
    });
  },

  videoPlay(e) {
    let type = e.type;
    if (type == "play") {
      this.setData({
        autoplay: false,
      });
    }
  },

  videoEnd(e) {
    let type = e.type;
    if (type == "ended") {
      this.setData({
        autoplay: true,
      });
    }
  },

  onChange(e) {
    this.setData({
      active: e.detail.name,
    });
  },

  // 采购备注
  noteInput(e) {
    let v = e.detail.value;
    this.setData({
      "formData.purchase_note": v,
    });
  },

  isValueNaN(value) {
    return typeof value === "number" && isNaN(value);
  },

  inputContent(e) {
    let value = e.detail.value;
    let index = e.detail.index;
    this.data.formData.attr_info[index].value = value;
  },

  uploadImgs(e) {
    let imgClassName = e.detail.name;
    let src = e.detail.file.tempFilePath;
    wx.cropImage({
      cropScale: "1:1",
      src: src,
      success: (res) => {
        // 执行上传
        this.setData({
          imgClassName: imgClassName,
        });
        this.doUpload(res.tempFilePath);
      },
      fail: (res) => {
        console.log("fail");
      },
    });
  },

  // 图片裁剪后上传
  doUpload(img) {
    let that = this;
    let tempFilePaths = img;
    let fileType = "image";
    let type = "product";
    upload_sign(type).then((res) => {
      if (res.data.code == 0) {
        let uploadData = res.data.data;
        let newPath =
          uploadData.dir +
          "/" +
          uploadData.file_name_prefix +
          "." +
          util.substrImgType(util.siding(tempFilePaths));
        uploadFile(
          uploadData.host,
          tempFilePaths,
          newPath,
          uploadData.policy,
          uploadData.access_key_id,
          uploadData.signature
        ).then((data) => {
          if (data.statusCode == 200) {
            if (that.data.imgClassName == "轮播图片") {
              const { carouselImgList = [] } = that.data;
              carouselImgList.push({
                url: that.data.imageUrl + newPath,
              });
              let list = {
                type: fileType,
                origin_name: util.siding(tempFilePaths),
                name: newPath,
              };
              that.data.formData.display_file.push(list);
              that.setData({
                carouselImgList: carouselImgList,
                formData: that.data.formData,
              });
            }
          }
        });
      }
    });
  },

  //上传详情图
  uploadMultiple(e) {
    let that = this;
    let tempFilePaths = e.detail.file;
    let fileType = "image";
    let type = "product";

    tempFilePaths.map((item) => {
      upload_sign(type).then((res) => {
        if (res.data.code == 0) {
          let uploadData = res.data.data;
          let newPath =
            uploadData.dir +
            "/" +
            uploadData.file_name_prefix +
            "." +
            util.substrImgType(util.siding(item.url));
          uploadFile(
            uploadData.host,
            item.url,
            newPath,
            uploadData.policy,
            uploadData.access_key_id,
            uploadData.signature
          ).then((data) => {
            if (data.statusCode == 200) {
              const { detailImgList = [] } = that.data;
              detailImgList.push({
                url: that.data.imageUrl + newPath,
              });
              let list = {
                type: fileType,
                origin_name: util.siding(item.url),
                name: newPath,
              };
              that.data.formData.desc_img.push(list);
              that.setData({
                detailImgList: detailImgList,
                formData: that.data.formData,
              });
            }
          });
        }
      });
    });
  },

  afterRead(e) {
    let that = this;
    let tempFilePaths = e.detail.file.url;
    let fileType = e.detail.file.type;
    let type = "user";
    // this.data.fileList = []
    upload_sign(type).then((res) => {
      if (res.data.code == 0) {
        let uploadData = res.data.data;
        let newPath =
          uploadData.dir +
          "/" +
          uploadData.file_name_prefix +
          "." +
          util.substrImgType(util.siding(tempFilePaths));
        uploadFile(
          uploadData.host,
          tempFilePaths,
          newPath,
          uploadData.policy,
          uploadData.access_key_id,
          uploadData.signature
        ).then((data) => {
          if (data.statusCode == 200) {
            const { carouselVideoList = [] } = that.data;
            carouselVideoList.push({
              url: that.data.imageUrl + newPath,
            });
            let list = {
              type: fileType,
              origin_name: util.siding(tempFilePaths),
              name: newPath,
            };
            that.setData({
              carouselVideoList: carouselVideoList,
            });
            that.setData({
              "formData.video_file": list,
            });
          }
        });
      }
    });
  },

  delete(e) {
    let index = e.detail.index;
    this.data.carouselVideoList.splice(index, 1);
    this.setData({
      "formData.video_file": {},
      carouselVideoList: this.data.carouselVideoList,
    });
  },

  async submit() {
    this.setData({
      submiting: true,
    });

    if (this.data.operate == "edit") {
      await this.update();
    }

    if (this.data.operate == "create") {
      await this.create();
    }
    this.setData({
      submiting: false,
    });
  },

  //更新商品
  update() {
    return new Promise((callback) => {
      let data = this.data.formData;
      data.id = this.data.id;
      data.from = this.data.from;

      data.weight.net_weight =
        data.weight.rough_weight - data.weight.out_weight;

      if (!this.check(data)) {
        this.setData({
          submiting: false,
        });
        return;
      }
      updata_product(data)
        .then((res) => {
          if (res.data.code == 0) {
            Toast("提交成功");
            setTimeout(function () {
              wx.navigateBack();
              callback();
            }, 1200);
          }
        })
        .catch((err) => {
          Toast(err.data.message);
          callback();
        });
    });
  },

  create() {
    return new Promise((callback) => {
      //  新建
      let data = this.data.formData;

      data.from = this.data.from;
      data.id = this.data.id;

      data.weight.net_weight =
        data.weight.rough_weight - data.weight.out_weight;
      if (!this.check(data)) {
        this.setData({
          submiting: false,
        });
        return;
      }

      data.supplier_id = wx.getStorageSync("supplierid");

      add_product(data)
        .then((res) => {
          if (res.data.code == 0) {
            Toast("提交成功");
            setTimeout(function () {
              wx.navigateBack();
              callback();
            }, 1200);
          }
        })
        .catch((err) => {
          Toast(err.data.message);
          callback();
        });
    });
  },

  // 校验
  check(data) {
    if (data.title === "") {
      Toast("请填写标题");
      return false;
    }
    //  分类
    if (data.category_ids.length !== 3) {
      Toast("请选择分类");
      return false;
    }
    // 单位
    if (data.product_unit_id === "") {
      Toast("请选择单位");
      return false;
    }

    if (data.display_file.length === 0) {
      Toast("请上传轮播图");
      return false;
    }

    return true;
  },

  //图片模块删除
  deletes(e) {
    let name = e.detail.name;
    if (name == "轮播图片") {
      let index = e.detail.index;
      this.data.formData.display_file.splice(index, 1);
      this.data.carouselImgList.splice(index, 1);
      this.setData({
        carouselImgList: this.data.carouselImgList,
        formData: this.data.formData,
      });
    } else if (name == "详情图") {
      let index = e.detail.index;
      this.data.formData.desc_img.splice(index, 1);
      this.data.detailImgList.splice(index, 1);
      this.setData({
        detailImgList: this.data.detailImgList,
        formData: this.data.formData,
      });
    } else {
    }
  },

  //单位
  unit() {
    unit_product().then((res) => {
      if (res.data.code == 0) {
        this.setData({
          unitList: res.data.data,
        });
      }
    });
  },

  //产品详情
  queryProductDetail(id) {
    let data = {
      id: id,
    };
    productPureInfo(data).then((res) => {
      if (res.data.code == 0) {
        let info = res.data.data;
        let {
          carouselImgList = [],
          carouselVideoList = [],
          detailImgList = [],
        } = this.data;
        info.display_file.map((item) => {
          carouselImgList.push({
            url: this.data.imageUrl + item.name,
          });
        });
        if (info.video_file.name) {
          carouselVideoList.push({
            url: this.data.imageUrl + info.video_file.name,
          });
        }
        if (info.desc_img && info.desc_img.length > 0) {
          if (info.desc_img[0].name) {
            info.desc_img.map((item) => {
              detailImgList.push({
                url: this.data.imageUrl + item.name,
              });
            });
          }
        }
        //  参数
        info.has_param_fmt = info.has_param ? "yes" : "no";
        // 价格/重量处理
        info.sku_list.forEach((ele) => {
          ele.price_fmt = (ele.price / 100).toFixed(1);
          ele.market_wholesale_price_fmt = (
            ele.market_wholesale_price / 100
          ).toFixed(1);
          ele.estimate_purchase_price_fmt = (
            ele.estimate_purchase_price / 100
          ).toFixed(1);
          ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(1);
          ele.out_weight_fmt = (ele.out_weight / 1000).toFixed(1);
          ele.net_weight_fmt = (ele.net_weight / 1000).toFixed(1);
          ele.per_price = ((ele.price/ele.net_weight)*10).toFixed(1)
        });

        if (!info.has_param) {
          this.setData({
            rough_weight_fmt_all: (w.rough_weight / 1000).toFixed(1),
          });
        }
        //  分类
        this.queryCategoryBySecond(info.category_ids[2]);
        info.is_check_weight_fmt = info.is_check_weight ? "check" : "notCheck";
        // 规格类型
        info.product_param_type_fmt =
          info.product_param_type === 1 ? "fruit" : "other";
        this.setData({
          id: id,
          carouselImgList: carouselImgList,
          detailImgList: detailImgList,
          carouselVideoList: carouselVideoList,
          formData: info, //总数据
        });
      }
    });
  },

  queryProductAudit() {
    let data = {
      id: this.data.id,
    };
    productAuditInfo(data).then((res) => {
      if (res.data.code !== 0) {
        wx.showToast({
          title: res.data.message,
          icon: "none",
        });
        return;
      }
      let auditInfo = res.data.data;
      let info = auditInfo.product;
      let {
        carouselImgList = [],
        carouselVideoList = [],
        detailImgList = [],
      } = this.data;
      info.display_file.map((item) => {
        carouselImgList.push({
          url: this.data.imageUrl + item.name,
        });
      });
      if (info.video_file.name) {
        carouselVideoList.push({
          url: this.data.imageUrl + info.video_file.name,
        });
      }
      if (info.desc_img && info.desc_img.length > 0) {
        if (info.desc_img[0].name) {
          info.desc_img.map((item) => {
            detailImgList.push({
              url: this.data.imageUrl + item.name,
            });
          });
        }
      }
      //  参数
      info.has_param_fmt = info.has_param ? "yes" : "no";
      // 价格/重量处理
      info.sku_list.forEach((ele) => {
        ele.price_fmt = (ele.price / 100).toFixed(2);
        ele.market_wholesale_price_fmt = (
          ele.market_wholesale_price / 100
        ).toFixed(2);
        ele.estimate_purchase_price_fmt = (
          ele.estimate_purchase_price / 100
        ).toFixed(2);
        ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(1);
        ele.out_weight_fmt = (ele.out_weight / 1000).toFixed(1);
        ele.net_weight_fmt = (ele.net_weight / 1000).toFixed(1);
      });

      if (!info.has_param) {
        this.setData({
          rough_weight_fmt_all: (w.rough_weight / 1000).toFixed(1),
        });
      }
      //  分类
      this.queryCategoryBySecond(info.category_ids[2]);
      info.is_check_weight_fmt = info.is_check_weight ? "check" : "notCheck";
      // 规格类型
      info.product_param_type_fmt =
        info.product_param_type === 1 ? "fruit" : "other";
      this.setData({
        carouselImgList: carouselImgList,
        detailImgList: detailImgList,
        carouselVideoList: carouselVideoList,
        formData: info, //总数据
      });
    });
  },

  queryCategoryBySecond(id) {
    //  分类详情
    let data = {
      third_category_id: id,
    };
    search_category(data).then((ress) => {
      if (ress.data.code == 0) {
        ress.data.data.map((item) => {
          this.data.classList.push(item.name);
        });
        this.setData({
          classList: this.data.classList,
        });
      }
    });
  },

  other(e) {
    let operate = this.data.operate;
    if (operate === "edit" && this.data.from === "list") {
      this.setData({
        showOther: true,
      });
    } else {
      wx.showToast({
        title: "不可操作",
        icon: "none",
      });
    }
  },

  onCloseOther(e) {
    this.setData({
      showOther: false,
    });
  },

  onSelect(e) {
    let v = e.detail.value;
    let pid = this.data.id;
    if (v === "copy") {
      Dialog.confirm({
        title: "复制商品",
        message: "复制后，放入仓库，需要重新编辑标题",
      })
        .then(() => {
          duplicateProduct({
            product_id: pid,
          })
            .then((res) => {
              if (res.data.code === 0) {
                Toast.success("复制成功");
              }
            })
            .catch((err) => {
              Toast.fail(err.data.message);
            });
        })
        .catch(() => {
          // on cancel
        });
    }

    if (v === "delete") {
      Dialog.confirm({
        title: "删除商品",
        message: "删除后，将会从仓库中移除，不可恢复",
      })
        .then(() => {
          // on confirm
          delete_product({
            product_id: pid,
          })
            .then((res) => {
              if (res.data.code === 0) {
                Toast.success("删除成功,即将返回上一页");
                setTimeout(() => {
                  wx.navigateBack();
                }, 1800);
              }
            })
            .catch((err) => {
              Toast.fail(err.data.message);
            });
        })
        .catch(() => {
          // on cancel
        });
    }
  },

  dealMoney(fen) {
    return Math.round(fen) / 100;
  },

  onShow() {
    let sku_list_str = this.data.sku_list_str;
    if (sku_list_str) {
      this.setData({
        "formData.sku_list": JSON.parse(this.data.sku_list_str),
      });
    }
  },
});
