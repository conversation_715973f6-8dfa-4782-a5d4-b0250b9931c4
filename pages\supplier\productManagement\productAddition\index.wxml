<!-- pages/supplier/productManagement/productAddition/index.wxml -->
<view class="nav" style="height:{{navBarHeight}}px;z-index: 999;">
  <view class="capsule-box" style="position: relative;height: {{menuHeight}}px;top:{{navBarHeight-menuHeight-4}}px;display: flex;">
    <image src="/static/point/left.png" mode="widthFix" style="width: 40rpx; height: auto;" bind:tap="back" />
    <view class="edit">商品编辑</view>
  </view>
</view>
<view class="container">
  <view style="margin-bottom: 20rpx;margin-top: {{navBarHeight}}px;">
    <van-tabs active="{{ active }}" bind:change="onChange">
      <view style="margin-top: 20rpx;">
        <van-tab title="轮播图">
          <view wx:if="{{carouselImgList.length > 0}}" style="margin-bottom: 20rpx;">
            <swiper circular="{{true}}" autoplay="{{autoplay}}" indicator-dots="{{true}}" interval="2000" duration="1000" style="height: 375px;">
              <block wx:for="{{carouselImgList}}" wx:key="name">
                <swiper-item>
                  <image webp="{{true}}" show-menu-by-longpress="true" src="{{item.url}}" mode="heightFix" data-info="{{item}}" style="width:100%;height: 345px;"></image>
                </swiper-item>
              </block>
            </swiper>
          </view>
          <van-uploader bind:delete="deletes" file-list="{{ carouselImgList }}" max-count="5" name="轮播图片" accept="image" bind:after-read="uploadImgs" />
        </van-tab>
        <van-tab title="视频">
          <view wx:for="{{carouselVideoList}}" wx:key="index" style="width: 100%;height: 345px;margin-bottom: 10rpx;">
            <video enable-play-gesture="{{true}}" object-fit="contain" show-mute-btn="{{true}}" show-fullscreen-btn="{{true}}" show-center-play-btn="{{true}}" controls="{{true}}" style="width:100%;height: 345px;" bindplay="videoPlay" bindpause="videoPause" bindended="videoEnd" src="{{item.url}}"></video>
          </view>
          <van-uploader bind:delete="delete" file-list="{{ carouselVideoList }}" max-count="1" name="轮播视频" accept="video" bind:after-read="afterRead" />
        </van-tab>

        <van-tab title="详情图">
          <view wx:if="{{detailImgList.length > 0}}" style="margin-bottom: 20rpx;">
            <swiper circular="{{true}}" autoplay="{{autoplay}}" indicator-dots="{{true}}" interval="2000" duration="1000" style="height: 375px;">
              <block wx:for="{{detailImgList}}" wx:key="name">
                <swiper-item>
                  <image webp="{{true}}" show-menu-by-longpress="true" src="{{item.url}}" mode="heightFix" data-info="{{item}}" style="width:100%;height: 345px;"></image>
                </swiper-item>
              </block>
            </swiper>
          </view>
          <van-uploader bind:delete="deletes" file-list="{{ detailImgList }}" multiple="{{true}}" max-count="5" name="详情图" accept="image" bind:after-read="uploadMultiple" />
        </van-tab>
      </view>
    </van-tabs>
  </view>

  <view style="{{isEdit=='false'?'pointer-events: none;':''}}">
    <view class="per">
      <view class="title">标题</view>
      <view class="right" style="font-weight: bold;">
        <textarea class="input" auto-height disabled="{{disabled}}" maxlength="100" value="{{formData.title}}" bindinput="title" />
      </view>
    </view>
    <view class="per">
      <view class="title">描述</view>
      <view class="right" style="align-items: flex-start;flex-direction: column;padding-right: 20rpx;box-sizing: border-box;">
        <textarea class="input textarea" disabled="{{disabled}}" maxlength="100" placeholder="该描述是位于标题之下，是整个链接的描述，建议填写；如果径，甜度等" value="{{formData.desc}}" bindinput="descInput" />

      </view>
    </view>
    <view class="per">
      <view class="title">产地</view>
      <view class="right" style="align-items: flex-start;flex-direction: column;padding-right: 20rpx;box-sizing: border-box;">
        <van-radio-group value="{{ formData.product_origin_type }}" bind:change="changeProductOriginType">
          <view style="display: flex;gap: 150rpx;">
            <van-radio name="domestic" icon-size="16px" style="font-size: 26rpx;">国内</van-radio>
            <van-radio name="foreign" icon-size="16px" style="font-size: 26rpx;">国外进口</van-radio>
          </view>
        </van-radio-group>
      </view>
    </view>

    <view class="per">
      <view class="title">分类</view>
      <view class="right">
        <classification bind:nextLower="nextLower" classList="{{classList}}" level="{{level}}" style="width: 100%;background-color: #eeeeee60;padding: 10rpx 0;"></classification>
      </view>
    </view>
    <view class="per">
      <view class="title">单位</view>
      <view class="right unit-type-name" bind:tap="unitActions">
        <view wx:if="{{formData.product_unit_type_name}}">{{formData.product_unit_type_name}}</view>
        <view wx:else style="font-size: 24rpx;color: #919191;">该商品销售计价单位</view>
        <image style="width: 40rpx;height: auto;" src="{{imageUrl+'icon/right-slim.png'}}" mode="widthFix" bind:tap="unitActions" />
      </view>
    </view>
    <view class="per">
      <view class="title">
        <view>计价</view>
        <view>方式</view>
      </view>
      <view style="flex: 1;">
        <view class="right">
          <van-radio-group value="{{ formData.is_check_weight_fmt }}" bind:change="isCheckWeight">
            <view style="display: flex;gap: 100rpx;">
              <van-radio name="check" icon-size="16px" style="font-size: 26rpx;">称重销售</van-radio>
              <van-radio name="notCheck" icon-size="16px" style="font-size: 26rpx;">按件销售</van-radio>
            </view>
          </van-radio-group>
        </view>
        <view style="font-size: 20rpx;color: #fa9441;margin-top: 8rpx;margin-left: 50rpx;" wx:if="{{formData.is_check_weight_fmt=='check'}}">
          计量多退少补
        </view>
      </view>
    </view>
    <view>
      <view style="display: flex;gap: 20rpx;margin-bottom: 20rpx;justify-content: space-between;">
        <view class="title" style="width: 130rpx;">规格</view>
        <view class="editSku" bind:tap="toEditSku">编辑</view>
      </view>

      <view>
        <view wx:for="{{formData.sku_list}}" wx:key="index" class="sku-item">
          <view wx:if="{{item.stock == 0}}" class="stock">缺货</view>
        <view class="boli" wx:if="{{item.stock == 0}}"></view>
          <view style="display: flex;gap: 10rpx;">
            <image src="{{imageUrl + item.cover}}" mode="widthFix" style="width: 160rpx;height: auto;border-radius: 10rpx;" />
            <view style="flex: 1;">

              <view style="display: flex;justify-content: space-between;align-items: center;">
                <view style="margin-bottom: 10rpx;">{{item.name}}</view>
              
              </view>

              <view style="display: flex;gap: 50rpx;font-size: 24rpx;">
                <view style="display: flex;align-items: center;">
                  <view>毛重:</view>
                  <view>{{item.rough_weight_fmt}}kg</view>
                </view>
                <view style="display: flex;align-items: center;">
                  <view>单价:</view>
                  <view>{{item.per_price}}/kg</view>
                </view>
              </view>
              <view style="font-size: 24rpx;color: #575757;">{{item.description}}</view>
            </view>

          </view>

          <view class="weight">
            <view style="display: flex;justify-content: space-between;">
              <view style="display: flex;align-items: center;">
                <view style="width:90rpx;font-weight: bold;">批发价:</view>
                <view>{{item.market_wholesale_price_fmt}}元</view>
              </view>

              <view style="display: flex;align-items: center;">
                <view style="width:90rpx;font-weight: bold;">采购价:</view>
                <view>{{item.estimate_purchase_price_fmt}}元</view>
              </view>

              <view style="display: flex;align-items: center;">
                <view style="width:90rpx;font-weight: bold;">销售价:</view>
                <view>{{item.price_fmt}}元</view>
              </view>
            </view>

          </view>

        </view>
      </view>
    </view>

    <view style="margin-top: 20rpx;">
      <view style="margin-bottom: 10rpx;font-size: 28rpx;">产品描述</view>
      <view style="{{isEdit=='false'?'pointer-events: none;':''}}">
        <base_param formData="{{formData.attr_info}}" bind:addBaseIInfoList="addBaseIInfoList" bind:inputContent="inputContent"></base_param>
      </view>
    </view>
    <van-divider borderColor="#1989fa"></van-divider>
    <view class="per">
      <view class="title">采购备注</view>
      <view class="right" style="align-items: flex-start;flex-direction: column;padding:10rpx 20rpx 10rpx 0;box-sizing: border-box;">
        <textarea class="input" auto-height disabled="{{disabled}}" maxlength="100" value="{{formData.purchase_note}}" bindinput="noteInput" />
        <view style="font-size: 20rpx;margin-top: 10rpx;color: #fa9441;">客户不可看，内容可为商品采购信息</view>
      </view>
    </view>
  </view>
  <view class="submait_wrap">
    <view bindtap="other" style="margin-right: 40rpx;">
      <van-icon name="ellipsis" size="25px" />
    </view>
    <van-button block style="width: 300rpx;margin-left: 40rpx;" type="info" bind:click="submit" loading="{{submiting}}" loading-text="提交中" size="" round>
      提交
    </van-button>
  </view>
  <van-action-sheet show="{{ unitShow }}" actions="{{ unitList }}" bind:click-overlay="closeUnit" bind:select="unitSelet" />
  <van-action-sheet style="margin-bottom: 90rpx;" show="{{ showOther }}" bind:close="onCloseOther" bind:select="onSelect" actions="{{ actions }}"></van-action-sheet>
  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
  <view style="margin: 100rpx 0 ;"></view>
</view>