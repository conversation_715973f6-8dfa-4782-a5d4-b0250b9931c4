/* pages/supplier/productManagement/productAddition/index.wxss */
page,
.cropper_wrap {
  width: 100%;
  height: 100vh;
}

.nav {
  position: fixed;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.capsule-box {
  margin-left: 20rpx;
  display: flex;
  box-sizing: border-box;
  align-items: center;
}
.stock{
  font-size: 24rpx;
  background-color: red;
  padding: 4rpx 10rpx;
  border-top-right-radius: 10rpx;
  border-bottom-left-radius: 10rpx;
  color: #fff;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 100;
}
.edit {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.container {
  padding: 30rpx;
}

.submait_wrap {
  margin-top: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}


.stepper_list {
  display: flex;
}

.stepper_list .label {
  width: 240rpx;
}

.van-uploader__preview-delete {
  padding: 15rpx;
}

.van-radio {
  margin-top: 10rpx !important;
}


.per {
  display: flex;
  align-items: center;
  padding-bottom: 40rpx;
}

.per .title {
  font-size: 30rpx;
  width: 140rpx;
  white-space: nowrap;
}

.per .right {
  flex: 1;
  display: flex;
  align-items: center;
}

.unit-type-name {
  justify-content: space-between;
  background-color: #eeeeee60;
  padding: 10rpx 0rpx 10rpx 10rpx;
}

.price-content {
  flex: 1;
  /* display: flex; */
  /* align-items: center; */
}

.right-weight {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.input {
  width: 100%;
  border-bottom: 1rpx solid #d6d4d4;
  padding: 10rpx;
  color: #5f5f5f;
  font-size: 30rpx;
  background-color: #eeeeee60;
}

.textarea {
  height: 120rpx;
}

.skuName {
  display: flex;
  /* border: 1px solid red; */
  flex-wrap: wrap;
  margin: 6rpx 0;
  padding: 4rpx 0;
  font-size: 24rpx;
  color: #999898;
}

.discountName {
  display: flex;
  /* border: 1px solid red; */
  flex-wrap: wrap;
  margin: 6rpx 0;
  padding: 4rpx 0;
  font-size: 24rpx;
  color: #999898;
}

.edit-input {
  border-bottom: 1rpx solid #d6d4d4;
  padding: 10rpx;
  color: #5f5f5f;
  font-size: 28rpx;
  background-color: #eeeeee60;
  text-align: center;
}

.weui-input {
  background-color: #f6f6f6;
  width: 300rpx;
  padding: 10rpx 0 10rpx 10rpx;
}

.add {
  border: 1px solid #f6f6f6;
  padding: 0 15rpx;
  text-align: center;
  line-height: 60rpx;
  border-radius: 20rpx;
  width: 150rpx;
  font-size: 26rpx;
}





.searchTag {
  flex: 1;
  display: flex;

  gap: 10rpx;
  box-sizing: border-box;
  flex-wrap: wrap;
}

.limit .per {
  display: flex;
  align-items: center;
}

.limit .per .title {
  font-size: 28rpx;
  padding-right: 8rpx;
}

.weight {
  font-size: 24rpx;
  justify-content: space-between;
  margin-bottom: 10rpx;
  margin-top: 10rpx;
}

.sku-item {
  border: 1px solid #dfdfdf;
  padding: 10rpx;
  margin: 10rpx 0;
  border-radius: 10rpx;
  position: relative;
}
.boli {
  width: 100%;
  height: 100%;
  background-color: #eeeeee90;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.editSku {
  color: #fff;
  background-color: #067fd7;
  padding: 10rpx 50rpx;
  display: flex;
  justify-content: center;
  border-radius: 10rpx;
  font-size: 24rpx;
}


.custom-tag {
  background-color: #f7f8fa;
  display: flex;
  gap: 10rpx;
  margin: 10rpx 0;
  box-sizing: border-box;
  padding: 10rpx;
  border-radius: 20rpx;
  align-items: center;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
}

.custom-tag::-webkit-scrollbar {
  display: none;
}


.custom-tag .per-tag {
  /* width: 200rpx; */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.custom-tag .per-tag .key {
  font-size: 22rpx;
  color: #a6a8aa;
  white-space: nowrap;
  padding: 6rpx 0;
}

.custom-tag .per-tag .value {
  font-weight: bold;
  font-size: 26rpx;
  white-space: nowrap;
}

.child {
  display: flex;
  align-items: center;
}

.child-title {
  font-size: 24rpx;
  white-space: nowrap;
  margin-right: 6rpx;
}