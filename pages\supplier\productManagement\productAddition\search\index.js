// pages/supplier/productManagement/productAddition/search/index.js
import Toast from '@vant/weapp/toast/toast';
Page({
  data: {
    searchTagList: [{
      text: '',
      uuid: ''
    }],
    search_tag_list: []
  },

  onLoad(options) {
    console.log(options, 111);
    let search_tag_list = JSON.parse(options.search_tag_list)

    let searchTagList = []
    if (search_tag_list) {
      search_tag_list.forEach((item, index) => {
        searchTagList.push({
          text: item,
          uuid: index.toString(),
        })
      });
    }

    this.setData({
      search_tag_list: search_tag_list,
      searchTagList: searchTagList
    })
  },

  addTag() {
    let searchList = this.data.searchTagList
    let item = {
      text: '',
      uuid: this.guid()
    }
    searchList.push(item)
    this.setData({
      searchTagList: searchList
    })
  },
  /**
   * 生成guid
   * @param {number} count
   * @return {string}
   * */
  guid(count = 16) {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
      var r = (Math.random() * count) | 0,
        v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(count);
    });
  },

  inputSearch(e) {
    let v = e.detail.value.replace(/\s+/g, '')
    let i = e.currentTarget.dataset.index
    let list = this.data.searchTagList
    list.forEach((item, index) => {
      if (index == i) {
        item.text = v
      }
    })

    this.setData({
      searchTagList: list
    })
  },

  blurSearch(e) {
    let v = e.detail.value.replace(/\s+/g, '')
    if (v === '') {
      Toast("内容不能为空")
    }
    let i = e.currentTarget.dataset.index
    let list = this.data.searchTagList
    list.forEach((item, index) => {
      if (index == i) {
        item.text = v
      }
    })

    this.setData({
      searchTagList: list
    })
  },

  check(list) {
    let numSet = new Set()
    list.forEach(item => {
      numSet.add(item.text)
    })
    if (numSet.size != list.length) {
      Toast('内容不能相同')
      return false
    }

    const value = list.every(ele => (ele.text !== ''))
    if (!value) {
      Toast('请填写内容')
      return false
    }
    return true
  },

  save() {
    //  保存
    let list = this.data.searchTagList
    let f = this.check(list)
    if (!f) {
      return
    }

    let search_tag_list = []
    list.forEach(item => {
      search_tag_list.push(item.text)
    });

    // 上一级页面参数
    let pages = getCurrentPages()
    let prevPage = pages[pages.length - 2];
    prevPage.setData({
      'formData.search_tag_list': search_tag_list
    })
    wx.navigateBack()
  },

  // 删除
  delete(e) {
    let i = e.currentTarget.dataset.index
    let list = this.data.searchTagList
    list = list.filter((item, index) => {
      return index !== i
    })

    this.setData({
      searchTagList: list,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})