<view class="container">
  <view style="display: flex;align-items: center;margin: 20rpx 0;" wx:for="{{searchTagList}}" wx:key="index">
    <input class="input" bindinput="inputSearch" bindblur="blurSearch" data-index="{{index}}" value="{{ item.text}}" maxlength="4" placeholder="请输入" />
    <view class="del" data-index="{{index}}" bind:tap="delete">删除</view>
  </view>
  <view wx:if="{{searchTagList.length < 6}}" class="add" bind:tap="addTag">添加搜索标签</view>


  <view style="margin-top: 160rpx;">
    <view style="font-weight: bold;">
      注:
    </view>
    <view style="font-size: 26rpx;">
      <view>
        1. 标签长度最多4个字符
      </view>
      <view>
        2. 标签不能包含空格
      </view>
    </view>
  </view>
</view>


<view class="bottom">
  <view class="ok" bind:tap="save">
    完成
  </view>
</view>

<van-toast id="van-toast" />