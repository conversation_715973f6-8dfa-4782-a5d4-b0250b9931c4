/* pages/supplier/productManagement/productAddition/search/index.wxss */
.edit-input {
  border: 1rpx solid #d6d4d4;
  margin-top: 6rpx;
  border-radius: 10rpx;
  padding: 10rpx;
  color: #5f5f5f;
}

.container {
  padding: 20rpx;
}

.input {
  border: 1rpx solid #d6d4d4;
  border-radius: 10rpx;
  padding: 10rpx;
  color: #5f5f5f;
  margin-right: 20rpx;
}

.add {
  border: 1px solid #e6f6f6;
  padding: 0 15rpx;
  text-align: center;
  line-height: 60rpx;
  border-radius: 20rpx;
  width: 200rpx;
  font-size: 26rpx;
  margin-top: 30rpx;
}

.del {
  width: 100rpx;
  border: 1px solid #7e7d7d;
  text-align: center;
  border-radius: 20rpx;
  height: 50rpx;
}

.bottom {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20rpx 20rpx 60rpx 20rpx;
  box-sizing: border-box;
  z-index: 2;
}

.ok {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
  /* margin:  0 20rpx; */
  box-sizing: border-box;
  width: 100%;
}