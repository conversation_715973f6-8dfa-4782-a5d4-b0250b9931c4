import Toast from '@vant/weapp/toast/toast';
const app = getApp()
import {
  sys_code,
  upload_sign
} from '../../../../../utils/api';
const util = require('../../../../../utils/util');
const uploadFile = require('../../../../../utils/uploadFile');

import regeneratorRuntime from 'regenerator-runtime'

Page({
  data: {
    showSpecifications: false,
    valueName: '', // 规格名称
    valueIndex: '',
    valueEdit: false,
    navBarHeight: app.globalData.navBarHeight,
    imageUrl: app.globalData.imageUrl,
    list: [],
    code_id: '',
    draggingIndex: -1,
    dragOffsetX: 0,
    dragOffsetY: 0,
    startX: 0,
    startY: 0,
    itemWidth: 240,  // 每个规格项的宽度
    itemHeight: 120, // 每个规格项的高度
    columnsPerRow: 3, // 每行3列
  },

  onLoad(options) {
    let list = JSON.parse(options.listStr)
    list.forEach(ele => {
      ele.is_stock = ele.stock == 0 ? true : false
      if (ele.cover) {
        ele.cover_list = [{
          url: this.data.imageUrl + ele.cover
        }]
      }
    })
    this.setData({
      list: list
    })
  },

  editName(e) {
    let info = e.currentTarget.dataset.info
    let valueName = info.name
    let valueIndex = info.id_code

    this.setData({
      valueEdit: true,
      showSpecifications: true,
      valueName,
      valueIndex
    })
  },


  // 上传图片
  uploadImgs(e) {
    let id = e.currentTarget.dataset.id
    let src = e.detail.file.tempFilePath
    wx.cropImage({
      cropScale: "1:1",
      src: src,
      success: (res) => {
        this.doUpload(res.tempFilePath, id)
      },
      fail: (res) => {
        console.log("fail");
      }
    })
  },

  // 图片裁剪后上传
  doUpload(img, id) {
    let that = this
    let tempFilePaths = img
    let type = "product"
    upload_sign(type).then(res => {
      if (res.data.code == 0) {
        let uploadData = res.data.data;
        let newPath = uploadData.dir + "/" + uploadData.file_name_prefix + '.' + util.substrImgType(util.siding(tempFilePaths))
        uploadFile(uploadData.host, tempFilePaths, newPath, uploadData.policy, uploadData.access_key_id, uploadData.signature).then(data => {
          if (data.statusCode == 200) {
            let cover_list = []
            cover_list.push({
              url: that.data.imageUrl + newPath
            });
            let cover = newPath
            let list = this.data.list
            list.forEach(ele => {
              if (ele.id_code == id) {
                ele.cover = cover
                ele.cover_list = cover_list
              }
            })

            that.setData({
              list: list
            });
          }
        })
      }
    })
  },

  deletes(e) {
    let id = e.currentTarget.dataset.id
    let list = this.data.list
    list.forEach(ele => {
      if (ele.id_code == id) {
        ele.cover = '',
          ele.cover_list = []
      }
    })
    this.setData({
      list
    })
  },


  deleteSku() {
    let valueIndex = this.data.valueIndex
    let list = this.data.list
    //删除list里id_code = valueIndex的元素
    list.forEach((item, index) => {
      if (valueIndex == item.id_code) {
        list.splice(index, 1)
      }
    })
    this.setData({
      list,
      showSpecifications: false,
    })
  },

  addSpecifications() {
    this.setData({
      valueEdit: false,
      showSpecifications: true,
      valueName: '',
      cover_list: []
    })
  },

  inputTitle(e) {
    let title = e.detail.value.replace(/\s+/g, '')
    if (title !== '') {
      this.setData({
        valueName: title
      })
    }
  },


  // 生成UUID的函数
  generateUUID() {
    return new Promise((callback) => {
      sys_code().then(res => {
        if (res.data.code == 0) {
          this.setData({
            code_id: res.data.data
          })

        }
        callback()
      })
    })

  },

  async saveAdd() {

    let list = this.data.list
    let valueName = this.data.valueName

    if (valueName === '') {
      Toast("请输入名称")
      return
    }

    if (this.data.valueEdit) {
      // 编辑
      let index = this.data.valueIndex
      list.forEach((item) => {
        if (index === item.id_code) {
          item.name = valueName
        }
      });
    } else {
      await this.generateUUID()
      // 添加
      let item = {
        id_code: this.data.code_id,
        is_stock: false,
        stock: 99,
        cover: '',
        description: '',
        cover_list: [],
        name: valueName,
        price: 0,
        market_wholesale_price: 0, //市场批发价
        estimate_purchase_price: 0, //采购价
        price_fmt: 0,
        market_wholesale_price_fmt: 0,
        estimate_purchase_price_fmt: 0,
        rough_weight_fmt: 0,
        rough_weight: 0, //毛重
        out_weight_fmt: 0,
        out_weight: 0, //皮重
        net_weight_fmt: 0,
        net_weight: 0, //净重
      }
      //  格式化
      list.push(item)
    }
    this.setData({
      list,
      showSpecifications: false
    })
  },


  switch1Change(e) {
    let id = e.currentTarget.dataset.id
    let list = this.data.list
    list.forEach(ele => {
      if (ele.id_code == id) {
        ele.stock = ele.is_stock ? 99 : 0
        ele.is_stock = !ele.is_stock
        
      }
    })

    this.setData({
      list,
    })
  },

  onClose() {
    this.setData({
      showSpecifications: false
    })
  },

  // 价格
  editInputPrice(e) {
    let v = e.detail.value
    let i = e.currentTarget.dataset.index
    let type = e.currentTarget.dataset.type
    if (v === '') {
      return
    }

    //  格式化
    let input_priceFmt = this.handleInput(e)
    let input_price = parseInt(input_priceFmt * 100)
    let list = this.data.list

    list.forEach((item, index) => {
      if (index == i) {

        if (type == 'price') {
          item.price = input_price
          item.price_fmt = input_priceFmt
        }
        if (type == 'market') {
          item.market_wholesale_price = input_price
          item.market_wholesale_price_fmt = input_priceFmt
        }

        if (type == 'estimate') {
          item.estimate_purchase_price = input_price
          item.estimate_purchase_price_fmt = input_priceFmt
        }

        if (type == 'desc') {
          item.description = v
        }

      }
    })
    this.setData({
      list
    })
  },

  // 重量
  editInputWeight(e) {
    let v = e.detail.value
    if (v === '') {
      return
    }
    let index = e.currentTarget.dataset.index
    let type = e.currentTarget.dataset.type

    let weight_fmt = this.handleInputOne(e)
    let weight = parseInt(weight_fmt * 1000)

    let list = this.data.list

    if (type == "rought") {
      list[index].rough_weight = weight
      list[index].rough_weight_fmt = weight_fmt
    }
    if (type == "out") {
      list[index].out_weight = weight
      list[index].out_weight_fmt = weight_fmt
    }
    // 计算净重
    let net = list[index].rough_weight - list[index].out_weight
    if (net <= 0) {
      net = 0
    }
    list[index].net_weight = net
    list[index].net_weight_fmt = parseFloat((net / 1000).toFixed(1))

    this.setData({
      list: list,
    })
  },

  handleInput(e) {
    let price = e.detail.value.replace(/\s+/g, '')
    price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
    price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    price = price.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      price = parseFloat(price);
    }
    return price
  },
  handleInputOne(e) {
    let price = e.detail.value.replace(/\s+/g, '')
    price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
    price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    price = price.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3'); //只能输入一个小数
    if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      price = parseFloat(price);
    }
    return price
  },



  check(skuList) {
    // 是否所有价格都大于0
    const isAllGreaterThanZero = skuList.every(ele => ele.price > 0)
    const isMarket = skuList.every(ele => ele.market_wholesale_price > 0)
    const isEstimate = skuList.every(ele => ele.estimate_purchase_price > 0)
    // 是否所有都有描述
    const isAllDesc = skuList.every(ele => ele.description)
    // 是否所有图片都上传
    const isAllCover = skuList.every(ele => ele.cover)
    // 重量
    const roughWeight = skuList.every(ele => ele.rough_weight > 0)
    // 皮重不能大于净重
    const diff = skuList.every(ele => ele.out_weight <= ele.rough_weight)
    if (!isAllCover) {
      Toast("请上传图片")
      return false
    }

    if (!isAllDesc) {
      Toast("请填写描述")
      return false
    }
    if (!isAllGreaterThanZero) {
      Toast("销售价必须大于0")
      return false
    }

    if (!isMarket) {
      Toast("批发价必须大于0")
      return false
    }

    if (!isEstimate) {
      Toast("采购价必须大于0")
      return false
    }


    if (!roughWeight) {
      Toast("重量必须大于0")
      return false
    }


    if (!diff) {
      Toast("皮重必须小于毛重")
      return false
    }
    return true
  },


  save() {
    //  保存
    let skuList = this.data.list
    let f = this.check(skuList)
    if (!f) {
      return
    }

    //上一级页面参数
    let pages = getCurrentPages()
    let prevPage = pages[pages.length - 2];
    prevPage.setData({
      "sku_list_str": JSON.stringify(skuList),
    })
    wx.navigateBack()
  },

  touchStart(e) {
    const index = e.currentTarget.dataset.index;
    const touch = e.touches[0];
    this.setData({
      draggingIndex: index,
      startX: touch.clientX,
      startY: touch.clientY,
      dragOffsetX: 0,
      dragOffsetY: 0
    });
  },

  touchMove(e) {
    if (this.data.draggingIndex === -1) return;
    
    const touch = e.touches[0];
    const offsetX = touch.clientX - this.data.startX;
    const offsetY = touch.clientY - this.data.startY;
    this.setData({
      dragOffsetX: offsetX,
      dragOffsetY: offsetY
    });
  },

  touchEnd(e) {
    if (this.data.draggingIndex === -1) return;
    
    const dragIndex = this.data.draggingIndex;
    const { dragOffsetX, dragOffsetY, itemWidth, itemHeight, columnsPerRow } = this.data;
    
    // 计算移动的步数
    const moveX = Math.round(dragOffsetX / itemWidth);
    const moveY = Math.round(dragOffsetY / itemHeight);
    
    // 计算当前位置
    const currentRow = Math.floor(dragIndex / columnsPerRow);
    const currentCol = dragIndex % columnsPerRow;
    
    // 计算目标位置
    const targetCol = Math.max(0, Math.min(columnsPerRow - 1, currentCol + moveX));
    const targetRow = Math.max(0, currentRow + moveY);
    const targetIndex = Math.min(this.data.list.length - 1, targetRow * columnsPerRow + targetCol);
    
    if (targetIndex !== dragIndex && targetIndex >= 0) {
      // 重新排序数组
      const newList = [...this.data.list];
      const dragItem = newList.splice(dragIndex, 1)[0];
      newList.splice(targetIndex, 0, dragItem);
      
      this.setData({
        list: newList
      });
    }
    
    // 重置拖拽状态
    this.setData({
      draggingIndex: -1,
      dragOffsetX: 0,
      dragOffsetY: 0
    });
  },

})
