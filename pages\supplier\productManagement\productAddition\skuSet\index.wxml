<view style="height: {{navBarHeight-10}}px;display: flex;justify-content: center;align-items: flex-end;">多规格设置</view>
<view class="container">

  <view class="partOne">
    <view class="name-tip" style="display: flex;align-items: center;justify-content: space-between;">
      <view class="title">多规格图片</view>
      <view style="font-size: 24rpx;color: #7a7a7a;">点击名称可删除规格</view>
    </view>
    <view class="partProduct">
      <view wx:for="{{list}}" wx:key="id" class="up-cover" 
            bindtouchstart="touchStart" 
            bindtouchmove="touchMove" 
            bindtouchend="touchEnd" 
            data-index="{{index}}"
            style="transform: translate({{index === draggingIndex ? dragOffsetX : 0}}px, {{index === draggingIndex ? dragOffsetY : 0}}px); z-index: {{index === draggingIndex ? 999 : 1}}; transition: {{index === draggingIndex ? 'none' : 'transform 0.3s ease'}};">
        <van-uploader bind:delete="deletes" file-list="{{ item.cover_list }}" max-count="1" data-id="{{item.id_code}}" accept="image" bind:after-read="uploadImgs" />
        <view class="name" bind:tap="editName" data-info="{{item}}">{{item.name}} </view>
      </view>
    </view>
    <view class="add-btn">

      <view class="add" bind:tap="addSpecifications">
        <van-icon name="plus" color="#fff" />
        <text style="font-size: 22rpx;margin-left: 6rpx;">添加规格</text>
      </view>

    </view>


  </view>

  <view class="partOne" wx:if="{{list.length > 0}}">

    <view class="title" style=" border-bottom: 1px solid #dfdbdb;">多规格参数</view>

    <view wx:for="{{list}}" wx:key="id" style="margin-top: 50rpx;">
      <view style="display: flex;justify-content: space-between;">
        <view class="listTitle">{{item.name}}</view>
        <view>
          <text style="color:{{item.is_stock?'red':''}}" wx:if="{{item.is_stock}}">缺货 </text>
          <switch checked="{{item.is_stock}}" color="red"  data-id="{{item.id_code}}" bindchange="switch1Change" />
        </view>
      </view>

      <view class="price-new" style="align-items: flex-start;">
        <text style="white-space: nowrap;">描述:</text>
        <textarea class="desc-input" auto-height data-index="{{index}}" data-type="desc" bindinput="editInputPrice" value="{{ item.description}}" style="width: 100%;"></textarea>
      </view>

      <view style="display: flex;gap: 8rpx;justify-content: space-between;">
        <view class="price-new" style="font-weight: bold;">
          <text>毛重(kg):</text>
          <input class="edit-input" type="digit" data-index="{{index}}" data-type="rought" bindinput="editInputWeight" value="{{ item.rough_weight_fmt}}" style="width: 90rpx;" />
        </view>

        <view class="price-new" style="font-weight: bold;">
          <text>皮重(kg):</text>
          <input class="edit-input" type="digit" data-index="{{index}}" data-type="out" bindinput="editInputWeight" value="{{ item.out_weight_fmt}}" style="width: 90rpx;" />
        </view>

        <view class="price-new" style="font-weight: bold;">
          <text>净重(kg):</text>
          <input class="edit-input" type="digit" disabled value="{{ item.net_weight_fmt}}" style="width: 90rpx;" />
        </view>
      </view>

      <view style="display: flex;gap: 20rpx;margin-bottom: 10rpx;justify-content: space-between;">

        <view class="price-new" style="font-weight: bold;">
          <text>批发价:</text>
          <input class="edit-input" type="digit" data-index="{{index}}" data-type="market" bindinput="editInputPrice" value="{{ item.market_wholesale_price_fmt}}" style="width: 100rpx;" />
        </view>

        <view class="price-new" style="font-weight: bold;">
          <text>采购价:</text>
          <input class="edit-input" type="digit" data-index="{{index}}" data-type="estimate" bindinput="editInputPrice" value="{{ item.estimate_purchase_price_fmt}}" style="width: 100rpx;" />
        </view>

        <view class="price-new" style="font-weight: bold;">
          <text>销售价:</text>
          <input class="edit-input" type="digit" data-index="{{index}}" data-type="price" bindinput="editInputPrice" value="{{ item.price_fmt}}" style="width: 100rpx;" />
        </view>

      </view>




      <view>

      </view>
    </view>
  </view>


  <view style="height: 180rpx;">
  </view>
</view>
<view class="bottom" wx:if="{{!enableCropper}}">
  <view class="ok" bind:tap="save">完成</view>
</view>


<van-dialog use-slot title="编辑规格" show="{{ showSpecifications}}" show-confirm-button="{{false}}" zIndex="99">
  <view style="display: flex; align-items: center; margin: 20rpx;">
    <text>名称</text>
    <input model:value="{{ valueName }}" maxlength="20" bind:input="inputTitle" placeholder="请输入名称" class="sku-name" />
  </view>
  <view>
    <!-- 事件 -->
    <view style="width: 100%;display: inline-flex;justify-content: space-around;margin: 30rpx 0;">
      <view class='deleteBtn' bindtap='deleteSku'>删除</view>
      <view class='cancelbnt' bindtap='onClose'>取消</view>
      <view class='wishbnt' bindtap='saveAdd'>确定</view>
    </view>
  </view>
</van-dialog>

<van-toast id="van-toast" />
