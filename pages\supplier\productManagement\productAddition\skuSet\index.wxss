.container {
  padding: 20rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
  background-color: #eee;
  height: 100%;
}

.partOne {
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 20rpx;
  padding: 10rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}
.name-tip{
  border-bottom: 1px solid #dfdbdb;
}

.title {
  padding: 20rpx;
  box-sizing: border-box;

}
.add-btn{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.add {
  margin-top: 20rpx;
  width: 150rpx;
  text-align: center;
  border-radius: 10rpx;
  font-size: 28rpx;
  padding: 10rpx;
  background-color: #07c060;
  color: #fff;
  display: flex;
  align-items: center;
}


.cancelbnt {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: #07c060;
  margin-right: 20rpx;
  width: 100rpx
}

.deleteBtn {
  background-color: #ededed;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: red;
  margin-right: 20rpx;
  width: 100rpx
}

.wishbnt {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
  width: 150rpx;
}

.inputStyle {
  border: 1px solid #dad7d7;
  border-radius: 10rpx;
  padding-left: 10rpx;
}

.name {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  text-align: center;
  color: #696969;
  background-color: #b7f1c580;
  width: 160rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  padding: 4rpx;
  margin-top: 10rpx;
}

.up-cover {
  margin-top: 10rpx;
  width: 200rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.van-uploader__preview {
  margin: 0 !important;
}
.van-uploader__upload{
  margin: 0 !important;
}
.price {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 10rpx;
  color: #bbb8b8;
  font-size: 28rpx;
  margin: 20rpx 0;
}

.partProduct {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.van-stepper__input {
  width: 100% !important;
}


/*  编辑-输入框 */
.edit-input {
  border-bottom: 1rpx solid #d6d4d4;
  padding: 0 6rpx;
  color: #5f5f5f;
}
.desc-input{
  border-bottom: 1rpx solid #d6d4d4;
  padding: 0 6rpx;
  padding-bottom: 6rpx;
  color: #5f5f5f;
}
.listTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  text-align: left;
  width: 50%;
  font-size: 34rpx;
  /* color: #07c060; */
  font-weight: bold;
  margin-bottom: 10rpx;
}

.sku-name {
  border-bottom: 1px solid #e0dfdf;
  margin-left: 20rpx;
  padding-bottom: 4rpx;
  font-weight: bold;
}

.price-new {
  font-size: 24rpx;
  display: flex;
  gap: 10rpx;
  align-items: flex-end;
  margin-bottom: 20rpx;
  
}

.ok {
  background-color: #07c060;
  text-align: center;
  border-radius: 20rpx;
  padding: 25rpx;
  color: white;
  /* margin:  0 20rpx; */
  box-sizing: border-box;
  width: 100%;
}

.bottom {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20rpx 20rpx 60rpx 20rpx;
  box-sizing: border-box;
  z-index: 2;
}