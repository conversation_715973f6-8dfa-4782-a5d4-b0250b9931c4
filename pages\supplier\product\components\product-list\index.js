import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
import {
  sale_product,
  audit_delete
} from '../../../../../apis/supplier/goodsManage';
import {
  categoryCoverProcess,
} from '../../../../../utils/dict';
import {
  product_user_type,
  purchase_note_update
} from '../../../../../apis/goods'

const app = getApp()
Component({
  properties: {
    list: {
      type: Array,
      value: []
    },
    saleValue: {
      type: Number,
      value: 0,
    }
  },

  data: {
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,
    searchContent: "", //搜索框内容
    showEditDesc: false, // 编辑描述
    descInfo: {
      product_id: '',
      desc: '',
    }, // 编辑描述内容 
    titleInfo: {
      product_id: '',
      ttle: '',
    }, //标题编辑内容
    showEditStock: false,
    index: 0, // 
    otherActions: [{
      id: '652210d71e1d890a1fa47694',
      name: '团好货专区',
      color: '#ee0a24',
    },],
    // 团好货专区ID
    part_id: '652210d71e1d890a1fa47694',
    selectInfo: {}, // 选中的商品 
    selectInfoPriceList: [], //  
    partPriceList: [], //  
    showAction: false,
    activeID: 1,
    sale: false,
    auditStatus: 0,
    actions: [],
    off_show: false,
    reason: '商品过季', //下架理由
    show_search: false,
    product_name: '',
    is_sure: false,
    type_show: false,
    radio_type: 'normal',
    radio_reason: '1'
  },
  methods: {
    // 采购备注
    handleNote(e) {
      let index = e.currentTarget.dataset.index
      let info = e.currentTarget.dataset.info
      this.setData({
        index: index,
        product_id: info.id,
        note_show: true,
        purchase_note: info.purchase_note
      })
    },
    onChangeEditNote(e) {
      this.setData({
        purchase_note: e.detail
      })
    },
    onCloseNote() {
      this.setData({
        note_show: false,
      })
    },

    saveNote() {
      let index = this.data.index
      let data = {
        product_id: this.data.product_id,
        purchase_note: this.data.purchase_note,

      }
      purchase_note_update(data).then(res => {
        if (res.data.code == 0) {
          wx.showToast({
            title: '保存成功',
          })
          let key = `list[${index}].purchase_note`
          this.setData({
            [key]: data.purchase_note
          });
        }
      })
    },

    // 益禾堂选择
    handleType(e) {
      let index = e.currentTarget.dataset.index
      let info = e.currentTarget.dataset.info
      this.setData({
        index: index,
        product_id: info.id,
        type_show: true,
        radio_type: info.user_type
      })
    },
    closeType() {
      this.setData({
        type_show: false
      })
    },
    typeChange(e) {
      this.setData({
        radio_type: e.detail
      })
    },

    submitTypr() {
      let data = {
        product_id: this.data.product_id,
        user_type: this.data.radio_type,
        index: this.data.index
      }
      product_user_type(data).then(res => {
        if (res.data.code == 0) {
          wx.showToast({
            title: '保存成功',
          })
          let key = `list[${data.index}].user_type`
          this.setData({
            [key]: data.user_type
          });
        }
      })
      this.setData({
        type_show: false
      })
    },

    // 跳转详情
    jumpProductDetail(e) {
      let info = e.currentTarget.dataset.info
      let id = info.id
      let saleValue = this.properties.saleValue
      wx.navigateTo({
        url: '/pages/supplier/product/info/index?id=' + id + `&from=list` + `&saleValue=` + saleValue,
      })
    },

    edit() {
      let info = this.data.selectInfo
      let id = info.id
      let operate = 'edit'
      let param = `?id=${id}`
      param += `&operate=${operate}&from=list`
      wx.navigateTo({
        url: '/pages/supplier/productManagement/productAddition/index' + param,
      })

    },

    delAudit(e){
      let info = e.currentTarget.dataset.info
      let that = this
      wx.showModal({
        title: '删除',
        content: '确认删除？',
        success (res) {
          if (res.confirm) {
            let data = {
              id: info.id
            }
            audit_delete(data).then(res=>{
              if(res.data.code == 0){
                Toast('删除成功')
                that.triggerEvent("hideProduct", info.id)
              }

            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })

    },

    editAudit(e){
      let info = e.currentTarget.dataset.info
      let id = info.id
      let operate = 'edit'
      let param = `?id=${id}`
      param += `&operate=${operate}&from=audit`
      wx.navigateTo({
        url: '/pages/supplier/productManagement/productAddition/index' + param,
      })
    },

    // 下架选择
    onChange(e) {
      let reason = ''
      if (e.detail == "1") {
        reason = '商品过季'
      }

      if (e.detail == "2") {
        reason = '缺货'
      }
      if (e.detail == "3") {
        reason = '其他'
      }

      this.setData({
        reason,
      })
    },
    closeOff() {
      this.setData({
        off_show: false,
        reason: '商品过季',
        radio_reason: "1"
      })
    },
    handleOffReason(e) {
      this.setData({
        reason: e.detail.value
      })
    },
    submitOff() {
      let reason = this.data.reason
      if (reason == "") {
        Toast('请输入下架原因');
        return
      }

      this.offShelf()
    },
    offShelf() {
      this.setData({
        off_show: false
      })
      let info = this.data.selectInfo
      let id = info.id
      let sale = info.sale
      let data = {
        product_id: id,
        sale: !sale,
        reason: this.data.reason
      }

      Dialog.confirm({
        title: '提示',
        message: '确认下架，审核通过后才能下架',
      })
        .then(() => {
          sale_product(data).then(res => {
            if (res.data.code == 0) {
              this.setData({
                off_show: false
              })
              if (sale) {
                Toast('申请成功');
              } else {
                Toast('上架成功');
              }
            }
          }).catch(err => {
            Toast(err.data.message);
          })
        })
        .catch(() => {
          // on cancel
        });

    },
    //  上架
    onShelf() {
      let info = this.data.selectInfo
      if (info.audit_status !== 2) {
        Toast('商品正在审核中');
        return
      }
      let id = info.id
      let sale = info.sale
      let data = {
        product_id: id,
        sale: !sale,
      }
      Dialog.confirm({
        title: '提示',
        message: '确认上架',
      })
        .then(() => {
          sale_product(data).then(res => {
            if (res.data.code == 0) {
              Toast('上架成功');
              this.triggerEvent("hideProduct", id)
            }
          })
        })
        .catch(() => {
          // on cancel
        });

    },

    // 点击打开动作面板
    handleOpenAction(e) {
      let account = wx.getStorageSync('x_supplier').account_status
      if (account !== 1) {
        Toast('当前账号不可用')
        return
      }
      let actions = []
      let info = e.currentTarget.dataset.info

      if (this.properties.saleValue === 0) {
        actions = []
       
        actions.push({
          name: '编辑',
          id: 1
        })
        actions.push({
          name: '下架',
          color: '#ee0a24',
          id: 2
        })

      } else {
        actions = []
        if (info.audit_status === 2) {
          actions.push({
            name: '上架',
            id: 3
          })
        }
        actions.push({
          name: '编辑',
          id: 1,
        })
      }

      // 当拿到title时 需要展示在页面上前五个字
      let title = info.title
      let name = title.replace(/[()]/g, '');
      name = name.slice(0, 5)

      this.setData({
        showAction: true,
        actions: actions,
        selectInfo: info,
        product_name: name,
      })
    },

    handleCloseAction() {
      this.setData({
        showAction: false
      })
    },

    async hanldeSelect(e) {
      let selectID = e.detail.id
      this.setData({
        showAction: false,
      })
      if (selectID == 1) {
        this.edit()
      }
      if (selectID == 2) {
        this.setData({
          off_show: true
        })

      }
      if (selectID == 3) {
        this.onShelf()
      }
      if (selectID == 400) {
        this.editBuyLimit()
      }
    },

    splitDotTwo(e) {
      let price = e.toString().replace(/\s+/g, '')
      if (price == '') {
        return 0
      }
      price = price.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
      price = price.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
      price = price.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      price = price.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
      if (price.indexOf(".") < 0 && price != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        price = parseFloat(price);
      }
      return price
    },

    isValueNaN(value) {
      return typeof value === 'number' && isNaN(value)
    },
  }

})