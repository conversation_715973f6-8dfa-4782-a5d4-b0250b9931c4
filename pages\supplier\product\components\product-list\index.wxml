<!-- pages/supplier/productManagement/components/sale-product-list/index.wxml -->
<view class="container">
  <view>
    <view wx:if="{{saleValue!==2}}">
      <view class="product_list_wrap" wx:for="{{list}}" wx:key="key">
        <view class="product_list" wx:if="{{item.show}}" style="background-color: {{(item.difference_time >=3 && saleValue == 0) ? '#f1eae7':''}};">
          <view class="product_content">
            <image bind:tap="jumpProductDetail" data-info="{{item}}" data-id="{{item.id}}" class="product_cover" lazy-load="true" mode="aspectFit" src="{{imageUrl+categoryCoverProcess+item.cover_img.name}}" mode="" />
            <view class="content_right">
              <view class="product_title" data-info="{{item}}" data-id="{{item.id}}">{{index+1}}. {{item.title}}</view>
              <view class="product_desc">{{item.desc}}</view>
              <view class="product_param">
                <view style="display: flex;gap: 14rpx;align-items: flex-start;">
                  <view class="check_weight_tip">
                    <view wx:if="{{item.is_check_weight}}" style="display: flex;align-items: center;">
                      <text class="word">称重销售</text>
                    </view>
                    <view wx:else class="word ">按件销售</view>
                    <view wx:if="{{item.user_type == 'YHT'}}" class="icon-YHT">益禾堂</view>
                    <view wx:if="{{item.product_origin_type == 'domestic'}}" class="word">国产</view>
                    <view wx:if="{{item.product_origin_type == 'foreign'}}" class="word">进口</view>
                  </view>
                </view>
                <view class="line-third">
                  <view class="per stock">
                    <view style="display: flex; font-size: 24rpx;">
                      <view>开放类型：</view>
                      <view>
                        <text wx:if="{{item.user_type == 'normal'}}">普通用户</text>
                        <text wx:if="{{item.user_type == 'YHT'}}">益禾堂</text>
                        <van-icon name="edit" data-index="{{index}}" data-info="{{item}}" bind:tap="handleType" />
                      </view>
                    </view>

                  </view>
                </view>
              </view>
            </view>
          </view>
          <view style="display: flex;justify-content: space-between;align-items: center;">
            <view style="font-size: 28rpx;margin-top: 8rpx;color: gray;">
              <text>采购备注：</text>
              <text>{{item.purchase_note}}</text>
              <van-icon wx:if="{{saleValue !==2}}" name="edit" data-index="{{index}}" data-info="{{item}}" bind:tap="handleNote" />
            </view>
            <view class="right" style="display: flex;align-items: flex-end;" wx:if="{{saleValue !== 2}}">
              <van-icon name="ellipsis" size="25px" data-info="{{item}}" bind:click="handleOpenAction" />
            </view>
          </view>
          <view>
            <!-- 规格 -->
            <view>
              <view wx:for="{{item.sku_list}}" wx:key="index" class="list-item">
                <view style="display: flex;gap: 10rpx;margin-bottom: 10rpx;">
                  <view style="width: 120rpx;">
                    <image src="{{imageUrl +categoryCoverProcess+ item.cover}}" mode="widthFix" style="width: 120rpx;height: auto;border-radius: 10rpx;white-space: nowrap;" />
                  </view>

                  <view>
                    <view style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 10rpx;">
                      <view style="font-size: 26rpx;font-weight: bold;">{{item.name}}</view>
                      <view class="stock-num" wx:if="{{item.stock == 0}}">缺货</view>
                    </view>
                    <view style="font-size: 20rpx;">
                      <text class="product_desc">{{item.description}}</text>
                    </view>
                  </view>
                </view>
                <view class="sku-item">
                  <view class="sku-text">
                    <view>毛重: {{item.rough_weight_fmt}}kg</view>
                    <view style="font-weight: bold;">批发价: {{item.market_wholesale_price_fmt}}</view>
                  </view>
                  <view class="sku-text">
                    <view>皮重: {{item.out_weight_fmt}}kg</view>
                    <view style="font-weight: bold;">采购价: {{item.estimate_purchase_price_fmt}}</view>
                  </view>
                  <view class="sku-text">
                    <view>净重: {{item.net_weight_fmt}}kg</view>
                    <view style="font-weight: bold;">销售价: {{item.price_fmt}}</view>
                  </view>
                </view>

              </view>
            </view>

          </view>
          <!--  -->
          <view wx:if="{{item.difference_time >= 3 && saleValue == 0}}" class="tag-tip">
            超时未更新信息，请及时更新！
          </view>
        </view>


      </view>
    </view>
    <!-- 审核商品 -->
    <view wx:if="{{saleValue==2}}">
      <view class="product_list_wrap" wx:for="{{list}}" wx:key="key">
        <view class="product_list" wx:if="{{item.show}}" style="background-color: {{(item.difference_time >=3 && saleValue == 0) ? '#f1eae7':''}};">
          <view class="product_content">
            <image bind:tap="jumpProductDetail" data-info="{{item}}" data-id="{{item.id}}" class="product_cover" lazy-load="true" mode="aspectFit" src="{{imageUrl+categoryCoverProcess+item.product.cover_img.name}}" mode="" />
            <view class="content_right">
              <view class="product_title" data-info="{{item}}" data-id="{{item.id}}">
                {{index+1}}. {{item.product.title}}
              </view>
              <view class="product_desc">{{item.product.desc}}</view>
              <view class="product_param">
                <view style="display: flex;gap: 14rpx;align-items: flex-start;">
                  <view class="check_weight_tip">
                    <view wx:if="{{item.product.is_check_weight}}" style="display: flex;align-items: center;">
                      <text class="word">称重销售</text>
                    </view>
                    <view wx:else class="word">按件销售</view>
                    <view wx:if="{{item.product.user_type == 'YHT'}}" class="icon-YHT">益禾堂</view>
                    <view wx:if="{{item.product.product_origin_type == 'domestic'}}" class="word">
                      国产
                    </view>
                    <view wx:if="{{item.product.product_origin_type == 'foreign'}}" class="word">
                      进口
                    </view>
                  </view>

                  <view class="audit_type">
                    {{item.is_new?"新增商品": "编辑商品"}}
                  </view>
                </view>

                <view>
                  <view class="reason" wx:if="{{item.edit_audit_status == 1}}">审核中</view>
                  <view class="reason" wx:if="{{item.edit_audit_status == 3}}">
                    <text class="audit">不通过 </text>
                    <text>{{item.edit_fail_reason}}</text>
                  </view>
                </view>
                <view class="line-third">
                  <view class="per stock">
                    <view style="display: flex; font-size: 24rpx;">
                      <view>开放类型：</view>
                      <view>
                        <text wx:if="{{item.product.user_type == 'normal'}}">普通用户</text>
                        <text wx:if="{{item.product.user_type == 'YHT'}}">益禾堂</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view>
            <!-- 规格 -->
            <view>
              <view wx:for="{{item.product.sku_list}}" wx:key="index" wx:for-item="ele" class="list-item">
                <!-- <view>{{ele.name}}</view> -->
                <view style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 10rpx;">
                  <view style="font-size: 26rpx;font-weight: bold;">{{ele.name}}</view>
                  <view class="stock-num" wx:if="{{ele.stock == 0}}">缺货</view>
                </view>
                <view class="sku-item">
                  <view class="sku-text">
                    <view style="font-weight: bold;">销售价: ￥{{ele.price_fmt}}</view>
                    <view>毛重: {{ele.rough_weight_fmt}}kg</view>
                  </view>
                  <view class="sku-text">
                    <view>市场价: ￥{{ele.market_wholesale_price_fmt}}</view>
                    <view>皮重: {{ele.out_weight_fmt}}kg</view>
                  </view>
                  <view class="sku-text">
                    <view>采购价: ￥{{ele.estimate_purchase_price_fmt}}</view>
                    <view>净重: {{ele.net_weight_fmt}}kg</view>
                  </view>
                </view>

              </view>
            </view>
            <view style="display: flex;justify-content: space-between;align-items: center;margin-top: 10rpx;">
              <view style="font-size: 28rpx;color: gray;flex: 1;">
                <text>采购备注：</text>
                <text>{{item.product.purchase_note}}</text>
              </view>

              <view class="det-btn" wx:if="{{item.edit_audit_status == 3}}" data-info="{{item}}" catch:tap="delAudit">
                删除
              </view>
              <view class="edit-btn" wx:if="{{item.edit_audit_status == 3}}" data-info="{{item}}" catch:tap="editAudit">
                编辑
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <van-dialog use-slot title="采购备注" show="{{ note_show }}" show-cancel-button bind:close="onCloseNote" bind:confirm="saveNote">
    <view style="border: 1px solid #fcf6f6;margin: 20rpx;">
      <van-field value="{{purchase_note}}" placeholder="备注" maxlength="80" show-word-limit type="textarea" autosize bind:change="onChangeEditNote" />
    </view>
  </van-dialog>


  <van-action-sheet show="{{ showAction }}" actions="{{ actions }}" bind:cancel="handleCloseAction" bind:close="handleCloseAction" bind:select="hanldeSelect" cancel-text="取消" close-on-click-overlay />

  <van-dialog use-slot title="类型" show="{{ type_show  }}" show-cancel-button bind:close="closeType" bind:confirm="submitTypr" close-on-click-overlay>
    <view style="box-sizing: border-box;display: flex;align-items: center;gap: 20rpx;padding: 20rpx 30rpx 40rpx 30rpx;">
      <view>开放类型：</view>
      <van-radio-group direction="horizontal" value="{{ radio_type}}" bind:change="typeChange">
        <van-radio icon-size="30rpx" name="normal">普通用户</van-radio>
        <van-radio icon-size="30rpx" name="YHT">益禾堂</van-radio>
      </van-radio-group>
    </view>
  </van-dialog>
</view>
<van-dialog use-slot title="下架" show="{{ off_show  }}" zIndex="999" showConfirmButton="{{false}}" close-on-click-overlay>
  <view style="display: flex;justify-content: center;margin: 20rpx 0;">
    <van-radio-group value="{{ radio_reason }}" bind:change="onChange">
      <view style="display: flex;gap: 20rpx;">
        <van-radio name="1">商品过季</van-radio>
        <van-radio name="2">缺货</van-radio>
        <van-radio name="3">其他</van-radio>
      </view>
    </van-radio-group>
  </view>
  <view style="box-sizing: border-box;display: flex;align-items: flex-start;gap: 20rpx;padding: 20rpx 30rpx 40rpx 30rpx;">
    <view style="white-space: nowrap;">理由：</view>
    <textarea type="text" value="{{reason}}" bindinput="handleOffReason" class="inputClass" auto-height="{{true}}" maxlength="20" />
  </view>
  <view style="display: flex;justify-content: center;gap: 100rpx;margin-bottom: 20rpx;">
    <view bind:tap="closeOff" class="cancel">取消</view>
    <view bind:tap="submitOff" class="submit">确认</view>
  </view>
</van-dialog>



<van-toast id="van-toast" />
<van-dialog id="van-dialog" />