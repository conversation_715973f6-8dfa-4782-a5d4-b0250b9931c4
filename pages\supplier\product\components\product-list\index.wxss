/* pages/supplier/productManagement/components/sale-product-list/index.wxss */
.container {
  padding: 0 20rpx;
}

.product_list_wrap {
  box-sizing: border-box;
  position: relative;
  margin-bottom: 20rpx;
}

.product_list {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 10rpx;
  padding-bottom: 40rpx;
  box-sizing: border-box;
  position: relative;
  border: 1px solid #757575;
}
.stock-num{
  font-size: 24rpx;
  background-color: orange;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  color: #fff;
}
.product_cover {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
}

.product_content {
  display: flex;
  justify-content: space-between;
}

.content_right {
  width: calc(100% - 200rpx);
}

.product_param {
  margin: 6rpx 0;
}

.price_list {
  margin-top: 20rpx;
  font-size: 30rpx;
  color: #333333;
  display: flex;
  justify-content: space-between;
}

.product_title {
  font-size: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-weight: bold;
}

.product_desc {
  font-size: 24rpx;
  color: #aaaaaa;
}

.check_weight_tip {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.check_weight_tip .word {
  display: flex;
  white-space: nowrap;
  font-size: 20rpx;
  padding: 1rpx 10rpx;
  border-radius: 6rpx;
  border: 1px solid #757575;
}


.audit_type {
  display: flex;
  white-space: nowrap;
  font-size: 20rpx;
  padding: 1rpx 10rpx;
  border-radius: 6rpx;
  border: 1px solid #757575;
}

.det-btn {
  display: flex;
  color: #ffffff;
  font-size: 28rpx;
  padding: 1rpx 10rpx;
  border-radius: 6rpx;
  background-color: red;
  width: fit-content;
  margin-right: 10rpx;
}

.edit-btn {
  display: flex;
  color: #ffffff;
  font-size: 28rpx;
  padding: 1rpx 10rpx;
  border-radius: 6rpx;
  background-color: #07c160;
  width: fit-content;
}

.icon-YHT {
  background-color: #f08221;
  display: flex;
  white-space: nowrap;
  color: #ffffff;
  font-size: 20rpx;
  padding: 1rpx 4rpx;
  border-radius: 6rpx;
  margin-left: 20rpx;
}

.fee_tip {
  border: 1rpx solid #fc7032;
  display: inline-flex;
  white-space: nowrap;
  color: #fc7032;
  font-size: 20rpx;
  padding: 1rpx 4rpx;
  border-radius: 6rpx;
}

.edit-input {
  border-bottom: 1rpx solid #d6d4d4;
  padding: 10rpx;
  color: #5f5f5f;
}

/*  line 3 */

.line-third {
  font-size: 24rpx;

}

.inputClass {
  border: 1px solid #eee;
  padding: 10rpx;
}

.line-third .per {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  margin-top: 20rpx;
}

.line-third .price {
  align-items: flex-end;
}


.line-third .stock .value {
  width: 90rpx;
  text-align: center;
  border-bottom: 2rpx solid orange;
  color: orange;
}

.price-unit {
  padding-left: 10rpx;
}

.btn {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  /* border: 1px solid red; */
  padding: 10rpx;
  align-items: center;
}

.discount {
  border: 1px solid #f3a0a0;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: auto;
  height: 30rpx;
  margin-right: 10rpx;
  padding-right: 6rpx;
}

.text {
  background-color: red;
  border-radius: 10rpx 0 0 10rpx;
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx;
  box-sizing: border-box;
  line-height: 18rpx;
}


.child {
  display: flex;
  align-items: center;
}

.child-title {
  font-size: 26rpx;
  white-space: nowrap;
  margin-right: 6rpx;
}

.audit {
  background-color: #f85f5f;
  padding: 0 4rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.reason {
  color: red;
  font-size: 24rpx;
  padding: 0 6rpx;
  border-radius: 10rpx;
  margin-top: 10rpx;
  display: flex;
  align-items: flex-start;
  gap: 10rpx;
}

.tag-tip {
  background-color: #fa9576;
  font-size: 22rpx;
  color: #fff;
  padding: 6rpx 10rpx;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  font-weight: bold;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
}

.search {
  box-sizing: border-box;
}

.list {
  margin-bottom: 16rpx;
  background-color: #eee;
  padding: 10rpx;
  padding-top: 0;
  padding-right: 0;
  border-radius: 20rpx;
  box-sizing: border-box;
}

.search-list {
  display: flex;
  align-items: flex-start;
}

.product {
  margin-left: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 130rpx;
  width: 530rpx;
}

.tip {
  font-size: 24rpx;
  margin-bottom: 6rpx;
  display: flex;
  justify-content: flex-end;
}

.tip-title {
  display: flex;
  width: 150rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #049104;
  color: #fff;
  height: 40rpx;
  border-top-right-radius: 10rpx;
  border-bottom-left-radius: 20rpx;

}

.sure {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  padding-bottom: 20rpx;
}

.sure-btn {
  background-color: #0780d8;
  width: 500rpx;
  text-align: center;
  color: #fff;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50rpx;
}

.border {
  border-right: 10rpx solid #049104;
}

.scroll-x {
  margin-top: 8rpx;
  width: 100%;
  border-top: 1rpx solid #eee;
  box-sizing: border-box;
  padding: 10rpx;
  margin-bottom: 10rpx;
}

.scroll-x-item {
  display: flex;
  width: 400rpx;
  align-items: flex-start;
  justify-content: space-between;
  padding: 6rpx;
  margin-top: 10rpx;
  border-radius: 10rpx;
  gap: 10rpx;
  border: 1px solid #eee;
  position: relative;
}

.x-title {
  width: 250rpx;
  font-size: 20rpx;
  margin-top: 16rpx;
}

.del {
  width: 40rpx;
  height: auto;
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #fff;
}

.cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  background-color: #d1d1d1;
  color: #fff;
}

.submit {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 50rpx;
  border-radius: 10rpx;
  background-color: #3bbdf5;
  color: #fff;
}

.list-item {
  border: 1px solid #d1d0d0;
  padding: 10rpx;
  border-radius: 10rpx;
  margin-top: 10rpx;
}

.sku-item {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}

.sku-text {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}