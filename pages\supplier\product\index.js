import {
  search_product,
  product_audit,
  get_product_num,
  low_product_list,
  product_update,
} from "../../../apis/supplier/goodsManage";

import regeneratorRuntime from "regenerator-runtime";
import dayjs from "../../../libs/dayjs";
import Dialog from "@vant/weapp/dialog/dialog";
import Toast from "@vant/weapp/toast/toast";
const app = getApp();
Page({
  data: {
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
    safeArea: app.globalData.safeArea,
    productNumStats: {
      sale_true_count: 0, // 在售
      sale_false_count: 0,
    }, // 商品数
    list: [], // 商品列表
    lowListShow: false,
    is_update: false,
    page: 1,
    limit: 10,
    tabActive: 0,
    fresh: 1,
    option: "新增",
    saleValue: "0",
    value1: 0,
    product_title: "",
    show: false,
    level: "",
  },
  handleEvent() {
    if (this.data.saleValue == 0) {
      this.queryProductNum();
    }
  },
  onLoad(options) {
    this.queryProductList();
    let level_info = wx.getStorageSync("x_supplier");
    this.setData({
      level: level_info.level,
    });
  },

  onShow() {
    this.queryProductNum();
  },

  onReachBottom() {
    if (this.data.saleValue !== "2") {
      this.loadMore();
    }
  },

  toProductLibary() {
    wx.navigateTo({
      url: "/pages/supplier/product/pool/index",
    });
  },
  handleLocality() {
    wx.navigateTo({
      url: "/pages/supplier/product/locality/index",
    });
  },
  async changeSale(e) {
    let saleValue = e.detail.name;
    this.setData({
      saleValue,
      page: 1,
      list: [],
      is_update: false,
      lowListShow: false,
    });
    if (saleValue !== "2") {
      this.queryProductList();
    } else {
      this.queryProductAuditList();
    }
    this.queryProductNum();
  },
  //  查询列表
  queryProductList() {
    let page = this.data.page++;
    if (page > this.data.totalPage) {
      return;
    }

    let sale_type = parseInt(this.data.saleValue) + 1;
    let data = {
      supplier_id: wx.getStorageSync("supplierid"),
      product_title: this.data.product_title,
      sale_type: sale_type,
      page: page,
      limit: this.data.limit,
    };
    if (this.data.saleValue == "2") {
      data.audit_status = 1;
    }
    search_product(data).then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data.list;
        if (!list || list.length === 0) {
          this.setData({
            list: [],
          });
          return;
        }

        list.forEach((item) => {
          item.show = true;
          item.link_product_price_fmt = (item.link_product_price / 100).toFixed(
            2
          );
          item.supply_price_fmt = (item.supply_price / 100).toFixed(2);
          if (item.sku_list) {
            item.sku_list.forEach((ele) => {
              ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(2);
              ele.out_weight_fmt = (ele.out_weight / 1000).toFixed(2);
              ele.net_weight_fmt = (ele.net_weight / 1000).toFixed(2);
              ele.price_fmt = (ele.price / 100).toFixed(2);
              ele.market_wholesale_price_fmt = (
                ele.market_wholesale_price / 100
              ).toFixed(2);
              ele.estimate_purchase_price_fmt = (
                ele.estimate_purchase_price / 100
              ).toFixed(2);

              ele.price_unit = ((ele.price / ele.rough_weight) * 10).toFixed(
                2
              );
              ele.wholesale_unit = (
                (ele.market_wholesale_price / ele.rough_weight) *
                10
              ).toFixed(2);
              ele.purchase_unit = (
                (ele.estimate_purchase_price / ele.rough_weight) *
                10
              ).toFixed(2);
              ele.profit_fmt = (
                ((ele.price - ele.estimate_purchase_price) / ele.price) *
                100
              ).toFixed(2);
            });
          }

          let pUnit = 0;
          let rPUnit = 0;
          if (item.is_check_weight) {
            let p = item.price;
            let r = item.supply_price;
            let w = item.weight.rough_weight / 1000;
            pUnit = (p / 100 / w).toFixed(2);
            rPUnit = (r / 100 / w).toFixed(2);
          }

          if (this.data.saleValue == "0") {
            let a = dayjs().valueOf();
            let time = a - item.version;
            item.difference_time = Math.floor(time / (1000 * 60 * 60 * 24));
          }
        });
        let newList = [...this.data.list, ...list];
        let count = res.data.data.count;
        this.data.totalPage = Math.ceil(count / this.data.limit);
        this.setData({
          list: newList,
        });

        console.log(123,newList)

      }
    });
  },

  //  查审核中的
  queryProductAuditList() {
    let data = {
      supplier_id: wx.getStorageSync("supplierid"),
    };
    product_audit(data).then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data;
        if (!list || list.length === 0) {
          this.setData({
            list: [],
          });
          return;
        }
        list.forEach((item) => {
          item.show = true;
          item.product.link_product_price_fmt = (
            item.product.link_product_price / 100
          ).toFixed(2);
          item.product.supply_price_fmt = (
            item.product.supply_price / 100
          ).toFixed(2);

          if (item.product.sku_list) {
            item.product.sku_list.forEach((item) => {
              item.rough_weight_fmt = (item.rough_weight / 1000).toFixed(2);
              item.out_weight_fmt = (item.out_weight / 1000).toFixed(2);
              item.net_weight_fmt = (item.net_weight / 1000).toFixed(2);
              item.price_fmt = (item.price / 100).toFixed(2);
              item.market_wholesale_price_fmt = (
                item.market_wholesale_price / 100
              ).toFixed(2);
              item.estimate_purchase_price_fmt = (
                item.estimate_purchase_price / 100
              ).toFixed(2);

              item.price_unit = ((item.price / item.rough_weight) * 10).toFixed(
                2
              );
              item.wholesale_unit = (
                (item.market_wholesale_price / item.rough_weight) *
                10
              ).toFixed(2);
              item.purchase_unit = (
                (item.estimate_purchase_price / item.rough_weight) *
                10
              ).toFixed(2);
              item.profit_fmt = (
                ((item.price - item.estimate_purchase_price) / item.price) *
                100
              ).toFixed(2);
            });
          }

          let pUnit = 0;
          let rPUnit = 0;
          if (item.is_check_weight) {
            let p = item.product.price;
            let r = item.product.supply_price;
            let w = item.product.weight.rough_weight / 1000;
            pUnit = (p / 100 / w).toFixed(2);
            rPUnit = (r / 100 / w).toFixed(2);
          }

          if (this.data.saleValue == "0") {
            let a = dayjs().valueOf();
            let time = a - item.product.version;
            item.product.difference_time = Math.floor(
              time / (1000 * 60 * 60 * 24)
            );
          }
        });
        this.setData({
          list: list,
        });
      }
    });
  },

  dealMoney(fen) {
    return Math.round(fen) / 100;
  },

  queryProductNum() {
    //  商品数
    let supplier_id = wx.getStorageSync("supplierid");
    let data = {
      supplier_id: supplier_id,
    };
    get_product_num(data).then((res) => {
      if (res.data.code == 0) {
        let d = res.data.data;
        this.setData({
          productNumStats: d,
        });
      }
    });
  },

  //  选择分类
  onClick() {
    this.setData({
      show: true,
    });
  },

  onSearch(e) {
    let title = e.detail;
    if (title == "") {
      return;
    }
    this.setData({
      product_title: title,
      page: 1,
      totalPage: 99,
      list: [],
    });
    this.queryProductList();
  },

  onCancelSearch(e) {
    this.setData({
      product_title: "",
      page: 1,
      totalPage: 99,
      list: [],
    });
    this.queryProductList();
  },

  //  加载商品
  loadMore() {
    if (!this.data.is_update && !this.data.lowListShow) {
      this.queryProductList();
    }
  },

  refresh(e) {
    this.setData({
      product_title: "",
      page: 1,
      totalPage: 99,
      list: [],
    });
    this.queryProductList();
  },

  hideProduct(e) {
    let id = e.detail;
    if (this.data.list) {
      this.properties.list.forEach((item) => {
        if (item.id === id) {
          item.show = false;
        }
      });
    }
    this.setData({
      list: this.properties.list,
    });
  },

  //商品新增
  createProduct(e) {
    let account = wx.getStorageSync("x_supplier").account_status;
    if (account !== 1) {
      Toast("当前账号不可用");
      return;
    }
    let param = `?id=''&operate=create`;
    wx.navigateTo({
      url: "/pages/supplier/productManagement/productAddition/index" + param,
    });
  },

  toPart() {
    //  专区管理
    wx.navigateTo({
      url: "/pages/supplier/product/specialProduct/index",
    });
  },

  lowListData() {
    this.setData({
      lowListShow: true,
    });
    let data = {
      supplier_id: wx.getStorageSync("supplierid"),
    };
    low_product_list(data).then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data;
        if (!list || list.length === 0) {
          this.setData({
            list: [],
          });
          return;
        }

        list.forEach((item) => {
          item.show = true;
          item.price_fmt = (item.price / 100).toFixed(2);
          let pUnit = 0;
          if (item.is_check_weight) {
            let p = item.price;
            let w = item.weight.rough_weight / 1000;
            pUnit = (p / 100 / w).toFixed(2);
          }
          item.priceUnit = pUnit;
          if (this.data.saleValue == "0") {
            let a = dayjs().valueOf();
            let time = a - item.version;
            item.difference_time = Math.floor(time / (1000 * 60 * 60 * 24));
          }
          if (item.discount_price_list && item.discount_price_list.length > 0) {
            item.discount_price_list.forEach((per) => {
              per.price_fmt = this.dealMoney(per.price);
            });
          }
        });

        this.setData({
          list: list,
        });
      }
    });
  },

  handleUpdate() {
    this.setData({
      is_update: true,
    });
    let data = {
      supplier_id: wx.getStorageSync("supplierid"),
    };
    product_update(data).then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data;
        if (!list || list.length === 0) {
          this.setData({
            list: [],
          });
          return;
        }
        list.forEach((item) => {
          item.show = true;

          if (item.sku_list) {
            item.sku_list.forEach((ele) => {
              ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(2);
              ele.out_weight_fmt = (ele.out_weight / 1000).toFixed(2);
              ele.net_weight_fmt = (ele.net_weight / 1000).toFixed(2);
              ele.price_fmt = (ele.price / 100).toFixed(2);
              ele.market_wholesale_price_fmt = (
                ele.market_wholesale_price / 100
              ).toFixed(2);
              ele.estimate_purchase_price_fmt = (
                ele.estimate_purchase_price / 100
              ).toFixed(2);

              ele.price_unit = ((ele.price / ele.rough_weight) * 10).toFixed(
                2
              );
              ele.wholesale_unit = (
                (ele.market_wholesale_price / ele.rough_weight) *
                10
              ).toFixed(2);
              ele.purchase_unit = (
                (ele.estimate_purchase_price / ele.rough_weight) *
                10
              ).toFixed(2);
              ele.profit_fmt = (
                ((ele.price - ele.estimate_purchase_price) / ele.price) *
                100
              ).toFixed(2);
            });
          }
         
          if (this.data.saleValue == "0") {
            let a = dayjs().valueOf();
            let time = a - item.version;
            item.difference_time = Math.floor(time / (1000 * 60 * 60 * 24));
          }
          if (item.discount_price_list && item.discount_price_list.length > 0) {
            item.discount_price_list.forEach((per) => {
              per.price_fmt = this.dealMoney(per.price);
            });
          }
        });

        this.setData({
          list: list,
        });
      }
    });
  },

  handleBackList() {
    this.setData({
      page: 1,
      list: [],
      lowListShow: false,
      is_update: false,
    });
    this.queryProductList();
  },
});
