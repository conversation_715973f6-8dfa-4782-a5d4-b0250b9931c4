<view class="nav" style="height:{{navBarHeight}}px;z-index: 999;">
  <view class="capsule-box" style="position: relative;height: {{menuHeight}}px;top:{{navBarHeight-menuHeight}}px;">
    <van-search value="{{ product_title }}" placeholder="请输入搜索关键词" show-action bind:search="onSearch" bind:cancel="onCancelSearch" />
  </view>
</view>

<view class="container" style="position: relative;top: {{navBarHeight}}px;">
  <view>
    <view style="position: fixed;width: 100%;z-index: 999;">
   

      <view class="tabs">
        <van-tabs type="card" bind:change="changeSale" color=" {{saleValue == 0 ? '#f7c569':'#f57583'}}" style="flex: 1;">
          <van-tab title="上架({{productNumStats.sale_true_count}})" name="0"></van-tab>
          <van-tab title="审核中" name="2"></van-tab>
          <van-tab title="下架({{productNumStats.sale_false_count}})" name="1"></van-tab>
        </van-tabs>
        <view style="display: flex;justify-content: space-around;">
          <view bind:tap="createProduct" class="add-new">新增</view>
        </view>
      </view>
    </view>

    <view style="padding-top: 80rpx;">
      <view style="margin:10rpx 30rpx;display: flex;gap: 20rpx;" wx:if="{{saleValue == '0'}}">
        <view class="low" style="background-color: #fc6173;" bind:tap="handleUpdate" wx:if="{{productNumStats.total_to_update_count > 0 && is_update == false}}">
          <text style="margin-right: 10rpx;">未更新商品</text>
          <text>({{productNumStats.total_to_update_count}})</text>
          <van-icon name="arrow" />
        </view>

        <view class="low" style="background-color: #fc6173;" bind:tap="handleLocality" wx:if="{{level == 'station'}}">
          <text style="margin-right: 10rpx;">本地模块</text>
          <van-icon name="arrow" />
        </view>


        <view class="low" style="background-color: #ee0a24;" wx:if="{{(productNumStats.total_to_update_count >0) && (lowListShow==true || is_update==true)}}" bind:tap="handleBackList">返回</view>
      </view>

      <product-list list="{{list}}" saleValue="{{saleValue}}" bind:refresh="refresh" bind:hideProduct="hideProduct" bind:handleEvent="handleEvent"></product-list>
    </view>

  </view>
  <view style="width: 100%;height: 200rpx;">
  </view>
</view>


<van-toast id="van-toast" />
<van-dialog id="van-dialog" />