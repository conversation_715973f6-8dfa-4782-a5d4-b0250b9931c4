/* pages/warehouse/delivery/index.wxss */
.nav {
  position: fixed;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
}

.tabs {
  width: 100vw;
  background-color: #fff;
  padding: 6rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.capsule-box {
  margin-left: 20rpx;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  align-items: center;
}

.custom-class-menu {
  border: none !important;
  box-shadow: none !important;

}

.van-tabs__scroll--card {
  margin: 0 !important;
  width: 100% !important;
}

.add-new {
  width: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #18aff5;
  color: #fff;
  margin-left: 10rpx;
  border-radius: 10rpx;
}

.delivery_title {
  margin-left: 30rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.order_list {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  border-bottom: 2rpx solid #e5e5e5;
}


.order_list_content {
  font-size: 28rpx;
  color: #666666;
}

.delivery_button_wrap {
  display: flex;
  justify-content: center;
  padding: 30rpx;
  box-sizing: border-box;
}


.delivery_button {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 8rpx;
  color: #ffffff;
  background-color: #FC4840;

}

.copy_button {
  background-color: #999999;
  color: #ffffff;
  padding: 0 10rpx;
  box-sizing: border-box;
  margin-left: 10rpx;
}

.low {
  line-height: 58rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  background-color: #1989fa;
  color: #fff;
  font-size: small;
  margin-right: 20rpx;
}

.productPool {
  background-color: orange;
  color: #fff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
}

.productUpdate {
  background-color: #07c160;
  color: #fff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
}