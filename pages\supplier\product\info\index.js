const app = getApp();
import { desc_tips_img } from "../../../../apis/goods";

import { productPureInfo, productAuditInfo } from "../../../../utils/api";
import Toast from "@vant/weapp/toast/toast";

import { displayProcess } from "../../../../utils/dict";
import regeneratorRuntime from "regenerator-runtime";
Page({
  data: {
    info: {}, // 商品
    interval: 2000,
    duration: 1000,
    imgUrl: app.globalData.imageUrl,
    navInfo: app.globalData.navInfo,
    navHeights: app.globalData.navHeight, //导航栏总高度
    show: false,
    options: [
      {
        name: "微信",
        icon: "wechat",
        openType: "share",
      },
      {
        name: "复制链接",
        icon: "link",
      },
    ],

    imageProcess: displayProcess,
    notRefreshCart: true, // 刷新购物车
    swiperHeight: 0,
    product_star: 0.5,
    image: {},
    id: "",
    showWeight: false,
    showAfterSale: false,
    topOpacity: 0,
    autoplay: true, // 视频
    showProductInfo: false,
    loading_show: false,
    phoneParam: app.globalData.phoneParam,
    saleValue: "",

    info_sku: null,
  },

  async onLoad(options) {
    let id = options.id;
    let saleValue = options.saleValue;
    this.setData({
      id: id,
      saleValue: saleValue,
    });
    await this.goodsDetail(id);
  },

  back() {
    wx.switchTab({
      url: "/pages/home/<USER>",
    });
  },
  calcImageHeight(e) {
    var winWid = wx.getSystemInfoSync().windowWidth; //获取当前屏幕的宽度
    var imgh = e.detail.height; //图片高度
    var imgw = e.detail.width;
    var swiperH = (winWid * imgh) / imgw; //等比设置swiper的高度。
    //即 屏幕宽度 / swiper高度 = 图片宽度 / 图片高度  -->  swiper高度 = 屏幕宽度 * 图片高度 / 图片宽度
    this.setData({
      swiperHeight: swiperH, //设置swiper高度
    });
  },

  //轮播图切换下标
  swiperChange(e) {
    let index = e.detail.current + 1;
    this.setData({
      dot: index,
    });
  },

  closeAddPop() {
    this.setData({
      show: false,
    });
  },
  //加入进货单
  onClickButton() {
    if (wx.getStorageSync(tokenKey)) {
      if (wx.getStorageSync(buyerIDKey)) {
        this.setData({
          show: true,
        });
      } else {
        Toast("暂未认证");
      }
    } else {
      this.setData({
        isLogin: true,
      });
    }
  },

  toCommentList() {
    wx.navigateTo({
      url: "/pages/classify/goodsDetail/CommentList/index?id=" + this.data.id,
    });
  },

  //商品详情
  goodsDetail(id) {
    return new Promise((callback) => {
      let saleValue = this.data.saleValue;
      let url = saleValue == "2" ? productAuditInfo : productPureInfo;
      let that = this;
      let data = {
        id: id,
      };

      url(data)
        .then((res) => {
          if (res.data.code == 0) {
            let p = saleValue == "2" ? res.data.data.product : res.data.data;
            if (p.video_file.name) {
              p.display_file.unshift(p.video_file);
            }
            if (p.sku_list) {
              p.sku_list.forEach((ele) => {
                ele.price_fmt = (ele.price / 100).toFixed(2);
                ele.estimate_purchase_price_fmt = (
                  ele.estimate_purchase_price / 100
                ).toFixed(2); //采购价
                ele.market_wholesale_price_fmt = (
                  ele.market_wholesale_price / 100
                ).toFixed(2);

                ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(1);
                ele.net_weight_fmt = (ele.net_weight / 1000).toFixed(1);
                ele.out_weight_fmt = (ele.out_weight / 1000).toFixed(1);

                ele.unit_price_fmt = (
                  (ele.price / ele.rough_weight) *
                  10
                ).toFixed(1);
                ele.net_unit_price_fmt = (
                  (ele.price / ele.net_weight) *
                  10
                ).toFixed(1);
              });
            }

            let n = p.sold_count;
            let m = p.sold_count + p.stock;
            if (m == 0 || n == 0) {
              p.percent = 0;
            }
            p.percent = (n / m).toFixed(2) * 100;
            if (!p.attr_info) {
              p.attr_info = [];
            }
            let arr = [];
            let j = 4;
            arr = arr.concat(p.attr_info.slice(0, j));
            p.arr = arr;

            let sku;
            if (p.sku_list && p.sku_list.length > 0) {
              sku = p.sku_list[0];
            }

            that.setData({
              info: p,
              info_sku: sku,
            });
          }
          callback();
        })
        .catch((err) => {
          wx.navigateBack();
        });
    });
  },

  // 选择
  handleSku(e) {
    let item = e.currentTarget.dataset.item;
    this.setData({
      info_sku: item,
    });
  },

  descImg() {
    desc_tips_img().then((res) => {
      if (res.data.code == 0) {
        let list = res.data.data.img_list;
        this.setData({
          descImg: list,
        });
      }
    });
  },

  // 称重提示
  toShowWeight() {
    this.setData({
      showWeight: true,
    });
  },

  toCloseWeight() {
    this.setData({
      showWeight: false,
    });
  },

  // 售后提示
  toShowAfterSale() {
    this.setData({
      showAfterSale: true,
    });
  },

  toCloseAfterSale() {
    this.setData({
      showAfterSale: false,
    });
  },

  onReady() {},

  onShow() {},

  // backRefresh(f) {
  //   let pages = getCurrentPages()
  //   let prevPage = pages[pages.length - 2];
  //   prevPage.setData({
  //     notRefreshCart: f,
  //   })
  // },

  onPageScroll(t) {
    let topOpacity = this.data.topOpacity;
    if (t.scrollTop < 40) {
      topOpacity = 0;
      if (this.data.topOpacity != 0) {
        this.setData({
          topOpacity: 0,
        });
      }
    } else if (t.scrollTop >= 40) {
      if (t.scrollTop > 200) {
        topOpacity = 1;
      } else {
        if (topOpacity <= 1) {
          topOpacity = t.scrollTop / 100 - 0.7;
        } else {
          topOpacity = 1;
        }
      }
      if (topOpacity <= 1) {
        this.setData({
          topOpacity: topOpacity,
        });
      }
    }
  },

  videoPlay(e) {
    let type = e.type;
    if (type == "play") {
      this.setData({
        autoplay: false,
      });
    }
  },

  videoPause(e) {},

  videoEnd(e) {
    let type = e.type;
    if (type == "ended") {
      this.setData({
        autoplay: true,
      });
    }
  },

  handleActive(e) {
    this.setData({
      showProductInfo: true,
    });
  },

  toClose() {
    this.setData({
      showProductInfo: false,
    });
  },

  onUnload() {},

  async onPullDownRefresh() {
    await this.goodsDetail(this.data.id);
    wx.stopPullDownRefresh();
  },
  onReachBottom() {},

  onShareAppMessage() {},
});
