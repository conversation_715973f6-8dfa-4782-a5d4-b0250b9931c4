<view class="container">
  <view class="product">
    <view class="carousel">
      <swiper circular="{{true}}" current="{{currentSwiper}}" autoplay="{{autoplay}}" indicator-dots="{{true}}" interval="2000" duration="1000" bindchange="swiperChange" style="height: 100%;">
        <block wx:for="{{info.display_file}}" wx:key="name">
          <swiper-item>
            <image webp="{{true}}" show-menu-by-longpress="true" wx:if="{{item.type=='image'}}" src="{{item.name?imgUrl+imageProcess+item.name:''}}" mode="widthFix" data-info="{{item}}" style="width:100%;height: auto;" bindload="calcImageHeight"></image>
            <video enable-play-gesture="{{true}}" object-fit="contain" show-mute-btn="{{true}}" show-fullscreen-btn="{{true}}" show-center-play-btn="{{true}}" controls="{{true}}" style="width:100%;height: {{swiperHeight}}px;" bindplay="videoPlay" bindpause="videoPause" bindended="videoEnd" wx:if="{{item.type=='video'&&item.name}}" src="{{item.name?imgUrl+item.name:''}}"></video>
          </swiper-item>
        </block>
      </swiper>
    </view>

    <view class="normal">
      <!-- 价格 -->
      <view class="price-part">
        <view>￥{{info_sku.price_fmt}}/{{info.product_unit_type_name}}</view>
        <view style="font-size: 26rpx;font-weight: normal;">(￥{{info_sku.unit_price_fmt}}/kg)</view>
      </view>
      <view style="background-color: #fff;border-radius: 20rpx; padding-bottom: 16rpx;">
        <view class="product-info">
          <view class="title">{{info.title}}</view>
          <view class="desc">{{info.desc}}</view>
        </view>

        <view class="product-info">
          <view class="sku-title">{{info_sku.name}}</view>
          <view class="desc">{{info_sku.description}}</view>
        </view>

        <view class="platform-tag">
          <view class="per" bind:tap="toShowWeight" wx:if="{{info.is_check_weight}}">
            <image src="{{imgUrl+'icon/weight.png'}}" mode="widthFix" style="height: auto;width: 22rpx;"></image>
            <text style="color: #707070;font-size: 22rpx;">称重退差价</text>
            <image src="{{imgUrl+'icon/right-slim.png'}}" mode="widthFix" style="height: auto;width: 22rpx;"></image>
          </view>
          <view class="per" bind:tap="toShowAfterSale">
            <image src="{{imgUrl+'icon/afterSale.png'}}" mode="widthFix" style="height: auto;width: 22rpx;"></image>
            <text style="color: #707070;font-size: 22rpx;">售后保障</text>
            <image src="{{imgUrl+'icon/right-slim.png'}}" mode="widthFix" style="height: auto;width: 22rpx;"></image>
          </view>
        </view>

      </view>
    </view>

    <view class="title-name">
      <!-- 规格 -->
      <view>规格</view>
      <view wx:for="{{info.sku_list}}" wx:key="index" class="{{info_sku.id_code == item.id_code?'id-sku':'sku'}}" data-item="{{item}}" bind:tap="handleSku">
        <view style="display: flex;align-items: center;justify-content: space-between;font-weight: bold;">
          <view style="display: flex;gap: 10rpx;">
            <image src="{{imgUrl + item.cover}}" mode="widthFix" style="width: 50rpx;height: auto;border-radius: 10rpx;" />
            <view>{{item.name}}</view>
          </view>
          <view style="color: {{info_sku.id_code == item.id_code?'red':'#474747'}}">￥{{item.price_fmt}}/{{info.product_unit_type_name}}</view>
        </view>
        <view style="display: flex;align-items: center;margin-top: 10rpx;font-size: 24rpx;gap: 20rpx;">
          <view class="backGround" style="background-color: {{info_sku.id_code == item.id_code?'#fff':'#f5f5f5'}};">
            <view style="text-align: center;">
              <view>毛重</view>
              <view>{{item.rough_weight_fmt}}kg</view>
            </view>

            <view style="text-align: center;">
              <view>毛单价</view>
              <view>{{item.unit_price_fmt}}/kg</view>
            </view>

          </view>

          <view class="backGround" style="background-color: {{info_sku.id_code == item.id_code?'#fff':'#f5f5f5'}};">
            <view style="text-align: center;">
              <view>净重</view>
              <view>{{item.net_weight_fmt}}kg</view>
            </view>
            <view style="text-align: center;">
              <view>净单价</view>
              <view>{{item.net_unit_price_fmt}}/kg</view>
            </view>

          </view>



        </view>
      </view>
    </view>
    <view class="title-name">
      <view style="display: flex; align-items: center;">
        <view>供应商： {{info.supplier_simple_name}}</view>
      </view>
    </view>
    <view class="title-name">
      <view style="display: flex; align-items: center;">
        <view>采购备注：</view>
        <view> {{info.purchase_note}}</view>
      </view>
    </view>

  </view>



  <view class="allProductInfo" data-info="{{info.attr_info}}" bind:tap="handleActive">
    <view style="display: flex; align-items: center; width: calc(100% - 40rpx); justify-content: space-around;">
      <block wx:for="{{info.arr}}" wx:key="index">
        <view style="display: flex; align-items: center; justify-content: space-around;  width: 150rpx;">
          <view style="text-align: center;line-height: 50rpx; width: 150rpx; text-align: center;">
            <view style="color: #b2b2b2; font-size: 28rpx;">{{item.field}}</view>
            <view style="font-size: 28rpx; font-weight: bold;">{{item.value}}</view>
          </view>
        </view>
        <view style="border-left: 2rpx solid #dfdcdc; height: 80rpx;" wx:if="{{index<3}}"></view>
      </block>
    </view>
    <view>
      <image src="{{imgUrl+'icon/right-slim.png'}}" style="width: 40rpx; height: auto;" mode="widthFix" />
    </view>
  </view>

  <view class="detail" id="detailPart">
    <view class="param">
      <view class="info_title" style="font-size: 30rpx;">详情</view>
    </view>

    <view wx:for="{{info.desc_img}}" wx:key="key">
      <image style="width:100%;" lazy-load="{{true}}" wx:if="{{item.name}}" src="{{item.name?imgUrl+item.name:''}}" mode="widthFix" />
    </view>

  </view>
</view>
<van-action-sheet show="{{ showWeight }}" close-on-click-overlay bind:close="toCloseWeight" title="称重退差价">
  <view style="font-size: 28rpx;padding: 0 30rpx;">
    <view>该商品为称重销售商品</view>
    <view style="padding: 10rpx 0;">
      如向您配送的商品重量小于标示重量，将按商品实付金额以及实际重量计价结算并退还差价，退款将于发货后到账，请注意查收。
    </view>
    <view>
      退款信息：通过“我的-订单-订单详情-品控退款”。
    </view>
  </view>
  <view style="height: 100rpx;">
  </view>
</van-action-sheet>

<van-action-sheet show="{{ showAfterSale }}" close-on-click-overlay bind:close="toCloseAfterSale" title="售后保障">
  <view style="font-size: 28rpx;padding: 0 30rpx;">
    <view>该商品属于【优质售后保障计划】品类</view>
    <view style="padding: 10rpx 0;">
      订单签收后，若商品若存在超出不良率的质量问题，您可在售后有效期内，通过“我的-订单-订单详情-申请售后”平台充分提供保障售后处理及退款。
    </view>
  </view>
  <view style="height: 100rpx;">
  </view>
</van-action-sheet>

<van-share-sheet show="{{ showShare }}" title="立即分享给好友" options="{{ options }}" bind:select="onSelect" bind:close="onClose" />
<login-pop show="{{isLogin}}"></login-pop>

<van-toast id="van-toast" />

<van-action-sheet show="{{ showProductInfo }}" bind:cancel="toClose" close-on-click-overlay bind:close="toClose">
  <view style="height: 100%; background-color: #f4f4f4; padding: 16rpx; padding-bottom: 0;">
    <view style="text-align: center;  font-size: 40rpx; padding: 10rpx 0 30rpx 0;">商品参数</view>
    <view class="allInfo">
      <view class="everyInfo" wx:for="{{info.attr_info}}" wx:key="index">
        <view style="color: #cfd4d4; width: 200rpx;">
          {{item.field}}
        </view>
        <view>
          {{item.value}}
        </view>
      </view>
      <view style="text-align: center; padding: 20rpx; color: #8b8d8d; font-size: 24rpx;">具体信息仅供参考，以实际收货为准</view>
    </view>

  </view>

  <view style=" background-color: #f4f4f4; padding: 16rpx; padding-bottom: 0;">
    <van-button color="#ef0e25" block round bind:tap="toClose">我知道了</van-button>
  </view>
</van-action-sheet>
<van-overlay show="{{ loading_show }}" z-index="9999" custom-style="background-color:#00000095;">
  <view style="display: flex;justify-content: center;margin-top: calc({{phoneParam.safeArea.height}}rpx + 100rpx);">
    <van-loading size="24px" color="#ffffff" vertical><text style="color: #ffffff;">加载中...</text></van-loading>
  </view>
</van-overlay>