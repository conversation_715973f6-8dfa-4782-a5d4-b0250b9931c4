.container {
  background-color: #f6f6f6;
  width: 100%;
}

.carousel {
  height: 750rpx;
  width: 100%;
}

.title-name {
  background-color: #fff;
  margin: 20rpx;
  padding: 20rpx;
  border-radius: 20rpx;
}

.price {
  align-items: flex-end;
}

.price .icon {
  font-size: 22rpx;
}

.price .value {
  font-weight: bolder;
  font-size: 40rpx;
}

.price .unit-price {
  margin-top: 6rpx;
  font-size: 24rpx;
}

/* 商品 */
.product-info {
  padding-top: 10rpx;
}

.product-info .title {
  font-weight: bold;
  font-size: 40rpx;
  margin: 10rpx 16rpx;
}
.product-info .sku-title {
  font-weight: bold;
  font-size: 30rpx;
  margin: 10rpx 16rpx;
}

/*  平台tag */
.platform-tag {
  display: flex;
  gap: 30rpx;
  padding: 0 16rpx;
  margin: 16rpx 0;
}

.platform-tag .per {
  display: flex;
  align-items: center;
  border: 1rpx solid #e5e5e5;
  padding: 4rpx;
  border-radius: 6rpx;
}

.sale_progress {
  background-color: #e8e8e8;
  border-radius: 44rpx;
  position: relative;
}

.sale_progress_size {
  height: 32rpx;
  line-height: 32rpx;
  position: absolute;
  top: 0;
  left: 30rpx;
  color: #ffffff;
  font-size: 22rpx;
}

.sku {
  border: 1rpx solid #d6d5d5;
  padding: 10rpx;
  margin-top: 10rpx;
  border-radius: 10rpx;
}

.id-sku {
  border: 1rpx solid #fae1e1;
  background-color: #fae1e1;
  padding: 10rpx;
  margin-top: 10rpx;
  border-radius: 10rpx;
}

.backGround {
  display: flex;
  flex: 1;
  justify-content: space-around;
  padding: 10rpx 0;
  border-radius: 10rpx;
}

.allProductInfo {
  background-color: #fff;
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 16rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail {
  background-color: #FFFFFF;
  padding-top: 20rpx;
  border-radius: 22rpx;
  margin: 0 16rpx;
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
}

.detail .param {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.showTopOne {
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 16rpx;
}

.allInfo {
  background-color: #fff;
  border-radius: 16rpx;
  align-items: center;
  justify-content: space-around;
  margin-top: 15rpx;
  padding: 16rpx;
  padding-bottom: 100rpx;
}

.everyInfo {
  border-bottom: 1px solid #f7f5f5;
  padding: 20rpx;
  display: flex;
  font-size: 26rpx;
}

.van-action-sheet {
  background-color: var(--van-action-sheet-item-background, #f4f4f4) !important;
}

.product .normal {
  background-color: #fff;
  border-radius: 20rpx;
  box-sizing: border-box;
  padding: 0 16rpx;
  background-image: linear-gradient(#F7415A, #F96D58, #F87759, #FBD4C1, #FBD4C1, #F6EFEE, #f6f6f6);
}

.price-part {
  height: 100rpx;
  display: flex;
  margin: 20rpx 20rpx;
  align-items: flex-end;
  box-sizing: border-box;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  gap: 20rpx;
}

.product .normal .desc {
  font-size: 24rpx;
  color: #9c9c9c;
}


/* 自定义tag */
.normal .custom-tag {
  background-color: #f7f8fa;
  display: flex;
  gap: 30rpx;
  margin: 16rpx 16rpx;
  box-sizing: border-box;
  padding: 16rpx;
  border-radius: 20rpx;
  align-items: center;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
}

.normal .custom-tag::-webkit-scrollbar {
  display: none;
}

.normal .custom-tag .per .key {
  font-size: 22rpx;
  color: #a6a8aa;
  white-space: nowrap;
  padding: 6rpx 0;
}

.normal .custom-tag .per .value {
  font-weight: bolder;
  font-size: 26rpx;
  white-space: nowrap;
  /* color: #b98b59; */
}

.normal .line {
  height: 50rpx;
  border: 1rpx solid #e2e2e2;
}

.bottom .normal .add-cart {
  padding: 14rpx;
  border-radius: 40rpx;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #ff2e39;
}

.bottom .normal .operate-cart {
  background-color: #ff2e39;
  border-radius: 40rpx;
  height: 100%;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.normal .count {
  display: block;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  background: #ff4611;
  padding: 0 6px;
  border-radius: 10px;
  color: #fff;
  position: absolute;
  left: 34px;
  top: 0;
}

.normal .product-info .desc {
  padding: 0 16rpx;
}