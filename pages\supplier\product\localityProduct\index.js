import {
  search_product,
} from '../../../../apis/supplier/goodsManage';
import {
  displayProcess,
} from '../../../../utils/dict';
import Toast from '@vant/weapp/toast/toast'
const app = getApp()
Page({

  data: {
    product_list: [],
    imgUrl: app.globalData.imageUrl,
    imageProcess: displayProcess,
    phoneParam: app.globalData.phoneParam,
    list:[],
    loading: true
  },

  onLoad(options) {
    let list = JSON.parse(options.list)
    this.setData({
      list,
    })
   
    this.getList()
  },

  getList() {
    let data = {
      category_id: '',
      page: 1,
      limit: 100,
      sale_type: 1,
      supplier_id: wx.getStorageSync('supplierid')
    }
    search_product(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        let options_list = this.data.list
        if (!list) {
          list = []
        }
        list.forEach((item, index) => {
          if (item.has_param && item.product_param_type === 1) {
            //  水果
            if (!item.custom_tag_list) {
              item.custom_tag_list = []
            }
            //  果径
            if (item.non_standard_attr.width > 0) {
              item.custom_tag_list.unshift({
                key: '果径',
                obj: 'width',
                value: item.non_standard_attr.width + 'mm'
              })
            }
            item.custom_tag_list.unshift({
              key: '等级',
              obj: 'level',
              value: item.non_standard_attr.fruit_class_name
            })
          }
          item.price_fmt = (item.price / 100)
          item.origin_price_fmt = (item.origin_price / 100)
          item.count = ((item.sold_count / (item.sold_count + item.stock)) * 100)
          item.is_select = false

          options_list.forEach(ele=>{
            if(ele.id == item.id){
              item.is_select = true
            }
          })
          
        })

        this.setData({
          product_list: list
        })
      }

    }).catch(err => {}).finally(()=>{
     this.setData({
       loading: false
     })
    })
  },

  jampInfo(e) {
    let id = e.currentTarget.dataset.info.id
    let list = this.data.product_list
    list.forEach(ele => {
      if (ele.id == id) {
        ele.is_select = !ele.is_select
      }
    })
    this.setData({
      product_list: list
    })
  },

  handleAddList() {
    let product_list = this.data.product_list
    let list = []
    product_list.forEach(ele => {
      if (ele.is_select) {
        list.push(ele)
      }
    })
    if(list.length == 0){
      Toast('请选择添加的商品');
      return
    }
    list = JSON.stringify(list)
    wx.navigateTo({
      url: '/pages/supplier/product/locality/index?list=' + list,
    })
  },

  onReady() {

  },

  onShow() {

  },


})