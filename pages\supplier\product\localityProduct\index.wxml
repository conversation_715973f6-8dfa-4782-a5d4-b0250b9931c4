<view class="content">
  <view wx:for="{{product_list}}" wx:key="id" class="list {{item.is_select?'border':''}}" bind:tap="jampInfo" data-info="{{item}}">
    <view>
      <image src="{{item.cover_img.name?imgUrl +imageProcess+ item.cover_img.name:''}}" mode="widthFix" style="width: 200rpx;height: auto;border-radius: 10rpx;" />
    </view>
    <view style="height: 195rpx;display: flex;flex-direction: column;justify-content: space-between;flex: 1;">
      <view>
        <view style="font-size: 28rpx;">
          <text class="supplier-name">{{item.supplier_simple_name}}</text>
          <text>{{item.title}}</text>
        </view>
        <!--标签  -->
        <view class="custom-tag" wx:if="{{item.custom_tag_list!==null}}">
          <block wx:for="{{item.custom_tag_list}}" wx:key="key" wx:for-item="Pitem">
            <view class="per">
              <view class="value" wx:if="{{Pitem.obj=='width'}}" style="color: #3a97ca;">{{Pitem.value}}</view>
              <view class="value" wx:if="{{Pitem.obj=='level'}}" style="color: #46a468;background-color: #e9faf1;border-radius: 6rpx;">{{Pitem.value}}</view>
              <view class="value" wx:if="{{!Pitem.obj}}">{{Pitem.value}}</view>
            </view>
            <view wx:if="{{index<item.custom_tag_list.length-1}}" class="line">
            </view>
          </block>
        </view>
        <view class="sale_progress_wrap">
          <view class="sale_progress">
            <progress percent="{{item.sold_count==0?0:item.count}}" stroke-width="12" border-radius="22" color="#FEA805" />
            <view class="sale_progress_size">累计已售{{item.sold_count}}</view>
          </view>
          <view class="stock">库存 {{item.stock}}</view>
        </view>
      </view>

      <!-- 非标品 -->
      <view class="price_wrap" wx:if="{{item.price>0}}">
        <view style="display:flex;align-items: flex-end;width: calc(100% - 60rpx);gap: 16rpx;">
          <block wx:if="{{item.origin_price > 0}}">
            <view class="price">
              <text style="font-size: 24rpx;color: rgb(158, 158, 158);">会员</text>
              <text style="font-size:34rpx;">{{item.price_fmt}}</text>
              <text style="color: red; font-size: 22rpx; font-weight: normal;">/{{item.product_unit_type_name}}</text>
            </view>
            <!-- 市场价 -->
            <view class="origin-price">
              <text>{{item.origin_price_fmt}}</text>
              <text style="font-size: 20rpx; font-weight: normal;">/{{item.product_unit_type_name}}</text>
            </view>
          </block>
          <block wx:else>
            <view class="price">
              <text style="font-size: 24rpx;">￥</text>
              <text>{{item.price_fmt}}</text>
              <text style="color: #858585; font-size: 28rpx; font-weight: normal;">/{{item.product_unit_type_name}}</text>
            </view>
          </block>
        </view>
      </view>

    </view>
  </view>
  <view class="more">^_^ 没有更多商品了</view>
</view>
<view class="bottom">
  <view class="add" bind:tap="handleAddList">添加</view>
</view>

<van-overlay show="{{ loading }}" z-index="9999" custom-style="background-color:rgba(0, 0, 0, 0.5);" bind:click="onClickHide">
  <view style="display: flex;justify-content: center;margin-top: calc({{phoneParam.safeArea.height / 2}}rpx + 100rpx);">
    <van-loading size="24px" color="#ffffff" vertical><text style="color: #ffffff;">加载中...</text></van-loading>
  </view>
</van-overlay>

<van-toast id="van-toast" />