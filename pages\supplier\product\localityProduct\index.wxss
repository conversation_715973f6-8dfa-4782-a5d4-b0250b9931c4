.cover_img {
  width: 100%;
  height: auto;
}

.content {
  padding: 20rpx;
  background-color: #eee;
  padding-bottom: 150rpx;
}

.list {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  background-color: #fff;
  margin-bottom: 16rpx;
  border-radius: 20rpx;
  padding: 10rpx;
}

.border {
  border-right: 10rpx solid green;
}

.supplier-name {
  background-color: #feebda;
  padding: 4rpx 6rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  color: #be8970;
  margin-right: 8rpx;
}

/* 自定义tag */
.custom-tag {
  display: flex;
  gap: 8rpx;
  box-sizing: border-box;
  align-items: center;
  font-size: 24rpx;
  color: #5f5f5f;
  overflow: hidden;
  margin-top: 10rpx;
}

.value {
  white-space: nowrap;
}

.price_wrap {
  margin-top: 10rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  font-size: 24rpx;
}

.price {
  color: red;
  font-weight: bolder;
  font-size: 34rpx !important;
}

.price-fmt {
  color: #858585;
  font-size: 20rpx;
  text-decoration: line-through;
  margin-left: 6rpx;
}

.origin-price {
  color: #858585;
  font-size: 28rpx;
  text-decoration: line-through;
  margin-left: 6rpx;
}

.cart-img {
  background-color: #fd8e49;
  border-radius: 50%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sale_progress_wrap {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 450rpx;
}

.sale_progress {
  width: calc(100% - 120rpx);
  background-color: #e8e8e8;
  border-radius: 44rpx;
  position: relative;
}

.sale_progress_size {
  height: 24rpx;
  line-height: 24rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 16rpx;
  color: #ffffff;
  font-size: 16rpx;
}

.stock {
  font-size: 20rpx;
  color: #999999;
}

.more {
  text-align: center;
  padding-bottom: 50rpx;
  margin-top: 30rpx;
  color: #a6a6a6;
  font-size: 26rpx;
}

.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 20rpx;
  box-sizing: border-box;
}

.add {
  text-align: center;
  padding: 16rpx 140rpx;
  background-color: green;
  color: #fff;
  border-radius: 20rpx;
}