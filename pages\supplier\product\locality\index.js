import {
  displayProcess,
} from '../../../../utils/dict';
import {
  local_submit,
  local_list
} from '../../../../apis/supplier/locality'
import Toast from '@vant/weapp/toast/toast'
const app = getApp()
Page({

  data: {
    product_list: [],
    imgUrl: app.globalData.imageUrl,
    imageProcess: displayProcess,
    draggingIndex: 0,
    dragStartY: 0,
    is_submit: false,
    is_btn: false,
    navBarHeight: app.globalData.navBarHeight,
    menuHeight: app.globalData.menuHeight,
    menuBottom: app.globalData.menuBottom,
  },

  onLoad(options) {
    if (Object.keys(options).length !== 0) {
      let list = JSON.parse(options.list)
      list.forEach(ele => {
        if (ele.title.length > 10) {
          ele.title_1 = ele.title.substring(0, 15) + '...';
        }
        ele.price_fmt = (ele.price / 100).toFixed(1)
        ele.unit_price_fmt = ((ele.price / ele.weight.rough_weight) * 10).toFixed(1)
      });
      this.setData({
        product_list: list,
        is_submit: true
      })
    }
    this.getList()
  },
  back(){
    wx.switchTab({
      url: '/pages/supplier/product/index',
    })
  },

  getList() {
    let data = {
      station_id: '66d7fee0e6aa983280068840',
      supplier_id: wx.getStorageSync('supplierid'),
    }
    local_list(data).then(res => {
      let product_list = this.data.product_list
      if (res.data.code == 0) {
        let list = res.data.data
        if (!list) {
          list = []
        }
        list.forEach(ele => {
          if (ele.title.length > 10) {
            ele.title_1 = ele.title.substring(0, 15) + '...';
          }
          ele.price_fmt = (ele.price / 100).toFixed(1)
          ele.unit_price_fmt = ((ele.price / ele.weight.rough_weight) * 10).toFixed(1)
          ele.is_del = false
          product_list.push(ele)
        });
        product_list = Array.from(
          new Map(product_list.map(item => [item.id, item])).values()
        );
        this.setData({
          product_list,
        })
      }
    }).catch(err => {

    })
  },
  handleToDel() {
    this.setData({
      is_btn: true
    })
  },
  handleDel(e) {
    let product_list = this.data.product_list
    let id = e.currentTarget.dataset.item.id
    product_list.forEach(ele => {
      if (ele.id == id) {
        ele.is_del = !ele.is_del
      }
    })
    this.setData({
      product_list,
    })
  },

  handleToAdd() {
    let list = JSON.stringify(this.data.product_list)
    wx.navigateTo({
      url: '/pages/supplier/product/localityProduct/index?list=' + list,
    })
  },
  // 保存
  handleSubmit() {
    this.setData({
      is_submit: false,
      is_btn: false,
    })
    let product_list = this.data.product_list
    let ids_list = []
    product_list.forEach(ele => {
      if (!ele.is_del) {
        ids_list.push(ele.id)
      }

    })
    let data = {
      station_id: '66d7fee0e6aa983280068840',
      supplier_id: wx.getStorageSync('supplierid'),
      product_id_list: ids_list
    }

    local_submit(data).then(res => {
      if (res.data.code == 0) {
        Toast('保存成功');
        this.setData({
          product_list: []
        })
        setTimeout(() => {
          this.getList()
        }, 500)

      }
    }).catch(err => {
      Toast(err.data.message);
      this.setData({
        is_submit: true,
      })
    })
  },

  onReady() {

  },

  onShow() {

  },

})