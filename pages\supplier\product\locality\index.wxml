<view class="nav" style="height:{{navBarHeight}}px;z-index: 999;">
  <view class="capsule-box" style="position: relative;height: {{menuHeight}}px;top:{{navBarHeight-menuHeight-4}}px;">
    <image src="/static/point/left.png" mode="widthFix" style="width: 40rpx;height: auto;" bind:tap="back" />
    <view>本地模块</view>
    <view style="width: 90rpx;"></view>
  </view>
</view>

<view class="container" style="padding-top: {{navBarHeight+10}}px;">
  <view style="display: flex;align-items: center;gap: 10rpx;">
    <view class="add" bind:tap="handleToAdd">添加</view>
    <view class="del" bind:tap="handleToDel" wx:if="{{!is_btn}}">删除</view>
  </view>

  <view class="content">
    <view wx:for="{{product_list}}" wx:key="id" class="list" bind:tap="handleDel" data-item="{{item}}">
      <view class="img" wx:if="{{is_btn}}">
        <image wx:if="{{!item.is_del}}" src="/static/point/select_.png" mode="widthFix" style="width: 40rpx;height: auto;" />
        <image wx:if="{{item.is_del}}" src="/static/point/select.png" mode="widthFix" style="width: 40rpx;height: auto;" />
      </view>
      <image lazy-load="{{true}}" src="{{item.cover_img.name?imgUrl+imageProcess+item.cover_img.name:''}}" class="goods_cover" mode="aspectFill" />
      <view class="title">{{item.title_1}}</view>
      <view class="text-style">
        <view>{{item.price_fmt}}/件</view>
        <view class="uni-style">{{item.unit_price_fmt}}/kg</view>
      </view>
    </view>
  </view>

</view>

<view class="bottom" wx:if="{{is_submit || is_btn}}">
  <view class="submit" bind:tap="handleSubmit">保存</view>
</view>

<van-toast id="van-toast" />