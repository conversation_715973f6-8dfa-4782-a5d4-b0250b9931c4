.nav {
  position: fixed;
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
}

.capsule-box {
  margin-left: 20rpx;
  width: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
}

.container{
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #eee;
  min-height: 100vh;
  padding-bottom: 150rpx;
}
.add{
  width: 100rpx;
  text-align: center;
  padding: 6rpx 0;
  border-radius: 10rpx;
  box-sizing: border-box;
  background-color: #128ddd;
  color: #fff;
}
.del{
  width: 100rpx;
  text-align: center;
  padding: 6rpx 0;
  border-radius: 10rpx;
  box-sizing: border-box;
  background-color: red;
  color: #fff;
}
.content {
  display: grid;
  align-items: center;
  color: #747272;
  font-size: 38rpx;
  z-index: 99;
  grid-template-columns: 32% 32% 32%;
  padding: 10rpx;
  padding-bottom: 40rpx;
  gap: 14rpx;
  box-sizing: border-box;
}

.text-style {
  display: flex;
  margin-top: 16rpx;
  color: #000;
  font-size: 22rpx;
  align-items: flex-end;
  font-weight: bold;
}

.uni-style {
  font-size: 18rpx;
  color: #eb691a;
  margin-left: 10rpx;
  font-weight: bold;
}

.title {
  font-size: 24rpx;
  text-align: left;
}
.list {
  background-color: #fff;
  padding: 10rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
  text-align: center;
  position: relative;
}
.img{
  position: absolute;
  top: -10rpx;
  right: 0;
  z-index: 99;
  background-color: #fff;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}
.goods_cover {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  position: relative;
}


.bottom {
  position: fixed;
  bottom: 0rpx;
  left: 0rpx;
  width: 100%;
  height: 150rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 20rpx;
  box-sizing: border-box;
}

.submit {
  text-align: center;
  padding: 16rpx 140rpx;
  background-color: green;
  color: #fff;
  border-radius: 20rpx;
}

.delete {
  text-align: center;
  padding: 16rpx 140rpx;
  background-color: red;
  color: #fff;
  border-radius: 20rpx;
}