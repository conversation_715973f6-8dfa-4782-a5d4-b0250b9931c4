// pages/classify/classify.js
import {
  class_list,
  next_class,
  goods_list,
  fruit_grade
} from '../../../../apis/pool'

import {
  categoryCoverProcess,
} from '../../../../utils/dict'

import {
  product_link,
} from '../../../../apis/productPool'


import regeneratorRuntime from 'regenerator-runtime'

const app = getApp()
Page({
  data: {
    classList: "", //一级分类列表
    active: 1,
    showMinus: false,
    showNumber: false,
    goodsNumber: 0, //
    activeKey: 0,
    navHeights: app.globalData.navBarHeight, //导航栏总高度
    imgUrl: app.globalData.imageUrl,
    nextClasss: [],
    is_special: false,
    fruitGrade: "", //水果等级
    selectFruitId: "", //水果等级id
    goodsList: [],
    page: 0, //页数
    goodsListLoadMore: true,
    productTop: 280, // 商品距离三级分类
    categoryCoverP: categoryCoverProcess, // 分类封面处理
    product_link_list: [],
    from: '',
    poster_list: [],
    morning_show: false,
    morning_info: {}
  },

  async onLoad(options) {
    if (options.from == 'poster') {
      this.setData({
        from: options.from,
        poster_list: JSON.parse(options.list)
      })
    }
    if (options.from == 'morning') {
      this.setData({
        from: options.from,
      })
    }

    // await this.linkProduct()
    this.classList()
  },

  linkProduct() {
    return new Promise((resolve) => {
      product_link().then(res => {
        if (res.data.code == 0) {
          // 已关联的商品列表
          let list = res.data.data
          if (!list) {
            list = []
          }
          this.setData({
            product_link_list: list,
          })
        }
      }).catch(err => { }).finally(() => {
        resolve()
      })
    })
  },


  onShow() { },

  //跳转搜索
  jumpSearch() {
    wx.navigateTo({
      url: '/pages/search/index',
    })
  },
  handleProduct(e) {
    if (this.data.from == 'poster') {
      this.handleSelect(e)
    }
    if (this.data.from == 'morning') {
      let info = e.currentTarget.dataset.info
      this.setData({
        morning_show: true,
        morning_info: info
      })
      this.handleSelect(e)
    }
    if (this.data.from !== 'poster' && this.data.from !== 'morning') {
      this.jumpGoodsDetail(e)
    }
  },
  //跳转商品详情
  jumpGoodsDetail(e) {
    let info = e.currentTarget.dataset.info
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + info.id,
    })
  },

  handleSelect(e) {
    let id = e.currentTarget.dataset.id
    let goodsList = this.data.goodsList
    if (this.data.from == 'poster') {
      let list = this.data.poster_list
      goodsList.forEach(ele => {
        if (id == ele.id) {
          ele.is_poster = !ele.is_poster
          if (ele.is_poster) {
            list.push(ele)
          } else {
            let indexToDelete = list.findIndex(item => item.id === id);
            if (indexToDelete !== -1) {
              list.splice(indexToDelete, 1);
            }
          }
        }
      })

      this.setData({
        goodsList,
        poster_list: list
      })
    }

    if (this.data.from == 'morning') {
      goodsList.forEach(ele => {
        if (id == ele.id) {
          ele.is_morning = !ele.is_morning
        } else {
          ele.is_morning = false
        }
      })

      this.setData({
        goodsList,
      })
    }
  },

  madePoster() {
    let list = JSON.stringify(this.data.poster_list)
    wx.navigateTo({
      url: '/pages/servicePoint/poster/index?list=' + list,
    })
  },

  toPromoteNew() {
    let info = JSON.stringify(this.data.morning_info)
    wx.navigateTo({
      url: '/pages/servicePoint/promoteNew/index?info=' + info,
    })
  },


  // 切换一级分类
  switchClass(e) {
    this.data.page = 0
    let id = e.currentTarget.dataset.id
    let pTop = this.data.productTop
    if (id === "6450d00498426603acf0a074") {
      pTop = 280
    } else {
      pTop = 180
    }

    this.setData({
      classId: id,
      goodsListLoadMore: true,
      activeKey: 0,
      selectFruitId: "",
      fruitClassId: "",
      productTop: pTop
    })
    this.secondClass()
  },
  //二级分类切换
  nextClassSWitch(e) {
    this.data.page = 0
    this.setData({
      nextClasss: [],
      classIds: e.currentTarget.dataset.info.id,
      is_special: e.currentTarget.dataset.info.is_special,
      className: e.currentTarget.dataset.info.name,
      level: e.currentTarget.dataset.info.level + 1,
      goodsListLoadMore: true,
      goodsList: [],
    })

    this.setData({
      selectFruitId: "",
      fruitClassId: ""
    })
    // let level = e.currentTarget.dataset.info.level+1
    // this.shoppingList(this.data.classIds,level)
    this.nextClasss()
    this.fruitGrade(this.data.classIds)
  },

  // 下一级分类切换
  nextClassSWitchs(e) {
    this.data.page = 0
    this.setData({
      classIds: e.currentTarget.dataset.info.id,
      level: e.currentTarget.dataset.info.level
    })
    this.data.goodsList = []
    this.data.goodsListLoadMore = true
    let level = e.currentTarget.dataset.info.level
    this.shoppingList(this.data.classIds, level)
  },




  //选择水果等级
  selectFruitGrade(e) {
    this.data.page = 0
    if (this.data.selectFruitId == e.currentTarget.dataset.info.id) {
      this.setData({
        selectFruitId: "",
        fruitClassId: "",
        goodsListLoadMore: true,
        goodsList: []
      })
      this.shoppingList(this.data.classIds, this.data.level)
    } else {
      this.setData({
        selectFruitId: e.currentTarget.dataset.info.id,
        goodsListLoadMore: true,
        goodsList: []
      })
      this.setData({
        fruitClassId: e.currentTarget.dataset.info.id,
      })

      this.shoppingList(this.data.classIds, this.data.level)
    }

  },

  //分类列表——一级
  classList() {
    let that = this
    class_list().then(res => {
      if (res.data.code == 0) {
        this.setData({
          classList: res.data.data,
          classId: res.data.data[0].id
        })
        that.secondClass()
      }
    })

  },
  //二级分类
  secondClass() {
    let that = this
    next_class(this.data.classId).then(res => {
      if (res.data.code == 0 && res.data.data.length > 0) {
        this.setData({
          nextClass: res.data.data,
          className: res.data.data[0].name,
          classIds: res.data.data[0].id,
          is_special: res.data.data[0].is_special,
          goodsList: []
        })

        // that.nextClasss()

        this.shoppingList(this.data.classIds, 2)

      }
    })


  },
  //下一级分类
  nextClasss() {
    let that = this
    next_class(that.data.classIds).then(res => {
      if (res.data.code == 0) {
        let newList = res.data.data
        if (that.data.is_special == true) {

        } else {
          newList.unshift({
            name: that.data.className,
            level: 2,
            id: that.data.classIds,
          })
        }
        this.setData({
          nextClasss: newList,
          level: 2,
        })

        this.shoppingList(this.data.classIds, 2)
      }
    })
  },

  shoppingList(categoryid, level) {
    if (!this.data.goodsListLoadMore) return
    this.data.page++
    let data = {
      category_id: categoryid,
      fruit_class_id: this.data.fruitClassId,
      level: level,
      page: this.data.page,
      limit: 5,
    }
    goods_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        if (!list) {
          list = []
        }
        list.forEach(item => {
          item = this.dealProduct(item)
          // 早市
          item.is_morning = false
          //海报参数
          let poster_list = this.data.poster_list
          poster_list.map(ele => {
            if (ele.id == item.id) {
              item.is_poster = ele.is_poster
            }
          })
        })

        let product_link_list = this.data.product_link_list

        list.forEach(e => {
          product_link_list.forEach(link_id => {
            if (e.id == link_id) {
              e.is_link_product = true
            }
          });
        });

        let newList = [...this.data.goodsList, ...list]
        this.setData({
          goodsList: newList,
          goodsListLoadMore: this.data.goodsList.length < res.data.data.count,
        })
      }
    })
  },

  dealProduct(info) {
    info.price_fmt = (info.start_price / 100).toFixed(0)
    if (info.is_check_weight && info.sku_list) {
      let index = info.sku_list.findIndex(e => {
        return e.price === info.start_price
      })
      if (index >= 0) {
        info.price_per_fmt = ((info.start_price / info.sku_list[index].rough_weight) * 10).toFixed(2)
      }

    }
    return info
  },


  //水果等级
  fruitGrade(id) {
    let data = {
      category_id: id
    }
    fruit_grade(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          fruitGrade: res.data.data
        })
      }
    })
  },


  onReady() {

  },

  change(e) {
    this.setData({
      activeKey: e.detail
    })
  },


  // onReachBottom() {
  //   if (this.data.goodsListLoadMore) {
  //     this.shoppingList(this.data.classIds, 2)
  //   }
  // },

  scrollToLower() {
    if (this.data.goodsListLoadMore) {
      this.shoppingList(this.data.classIds, 2)
    }
  }


  /**
   * 用户点击右上角分享
   */
})