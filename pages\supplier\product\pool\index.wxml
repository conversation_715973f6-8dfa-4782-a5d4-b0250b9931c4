<view class="container">
  <van-toast id="van-toast" />

  <view style="width: 100%;z-index:9999;background: linear-gradient(#FEE1CD, #FEF2E8);position:fixed;top: 0;">
    <view class="big_class">
      <view class="big_class_list" wx:for="{{classList}}" wx:key="key" data-id="{{item.id}}" bindtap="switchClass">
        <view class="title">{{item.name}}</view>
        <view class="big_class_line" style="{{item.id==classId?'background-color: #FD6905':'background-color: #ffffff;opacity: 0;'}}"></view>
      </view>
    </view>
  </view>

  <!-- </van-sticky> -->
  <view style="display: flex;height: calc(100vh - {{navHeights * 2}}rpx - 140rpx);" class="">
    <view style="height: calc(100vh - {{navHeights * 2}}rpx - 130rpx);position: fixed;top:calc({{navHeights * 2}}rpx );" class="sidebar">
      <van-sidebar active-key="{{ activeKey }}" bind:change="change">
        <view wx:for="{{nextClass}}" wx:key="key" data-info="{{item}}" bindtap="nextClassSWitch">
          <van-sidebar-item title="{{item.name}}" />
        </view>
      </van-sidebar>
    </view>
    <view class="content_right">
      <view class="goods_list_wrap" style="padding-top: calc({{navHeights * 2}}rpx);">
        <scroll-view bindscrolltolower="scrollToLower" 	scroll-y="{{true}}"  show-scrollbar="{{false}}" enhanced  style="height: 120%;">

          <view class="goods_list {{item.is_poster?'poster':item.is_morning?'morning':''}}" wx:for="{{goodsList}}" wx:key="key" data-id="{{item.id}}" data-info="{{item}}" bindtap="handleProduct">

            <view class="goods_cover">
              <image lazy-load="{{true}}" src="{{item.cover_img.name?imgUrl+categoryCoverP+item.cover_img.name:''}}" class="goods_cover" mode="aspectFill" />
            </view>
            <view class="goods_content">
              <view class="goods_content_title">
                <text class="title_icon">{{item.supplier_simple_name}}</text>
                <text style="font-size: 26rpx;">{{item.title}}</text>
              </view>

              <view class="price_wrap" wx:if="{{item.start_price>0}}">
                <view style="width: 100%;">
                  <block>
                    <view class="price">
                      <view>
                        <text style="font-size: 24rpx;">￥</text>
                        <text style="font-size: 32rpx;">{{item.price_fmt}}</text>
                        <text style="color: #858585; font-size: 28rpx; font-weight: normal;">/{{item.product_unit_type_name}}</text>
                      </view>
                      <view wx:if="{{item.is_check_weight}}">
                        <text style="font-size: 24rpx;">￥</text>
                        <text style="font-size: 24rpx;">{{item.price_per_fmt}}</text>
                        <text style="color: #858585; font-size: 24rpx; font-weight: normal;">/kg</text>
                      </view>

                    </view>
                  </block>
                </view>

                <!-- <view class="image">
                  <image src="{{imgUrl+'icon/cart2.png'}}" mode="widthFix" style="height: auto;width: 32rpx;padding: 2rpx;" />
                </view> -->
              </view>

            </view>
          </view>
          <view wx:if="{{!goodsListLoadMore && goodsList.length !== 0}}" class="tip">该品类没有更多商品了</view>
          <van-empty wx:if="{{goodsList.length==0}}" description="暂无内容" />

        </scroll-view>
      </view>


    </view>
  </view>

</view>

<view class="made-poster" wx:if="{{poster_list.length > 0}}">
  <view class="poster-content" bind:tap="madePoster">已选品【{{poster_list.length}}】-生成海报</view>
</view>

<view class="made-poster" wx:if="{{morning_show}}">
  <view class="promote-content" bind:tap="toPromoteNew">已选好 去推广</view>
</view>
