.big_class {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: scroll;
  border-bottom: 2rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 120rpx;
  box-sizing: border-box;

}

.big_class_line {
  border-radius: 8rpx;
}

.big_class::-webkit-scrollbar,
.multilevel_class::-webkit-scrollbar,
.sidebar::-webkit-scrollbar,
.goods_list_wrap::-webkit-scrollbar {
  display: none;
}

.search_input_wrap {
  display: flex;
  align-items: center;
}

.search_title {
  font-size: 36rpx;
  font-weight: bold;
  margin-left: 30rpx;
}

.search_input {
  height: 100%;
  width: 100%;
  border-radius: 44rpx;
  border: 2rpx solid #666666;
  margin: 0 30rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #999999;
}

.search_icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.big_class_list {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 30rpx;
}

.big_class_list .title {
  padding: 0 30rpx;
}

.big_class_list view:nth-child(2) {
  width: 40rpx;
  height: 8rpx;
  /* background-color: #FC4840; */
  margin-top: 4rpx;
}

.sidebar {
  width: 160rpx;
  /* height: calc(100vh - 222rpx); */
  overflow-y: scroll;
  background-color: #F8F8F8;
}

.cart_icon,
.cart_icons {
  width: 80rpx;
  height: 80rpx;
}

.cart_icon {
  position: relative;
}

.Subscript {
  position: absolute;
  right: 0;
  top: 0;
  width: 40rpx;
  text-align: center;
  height: 40rpx;
  line-height: 40rpx;
  background-color: #FC4840;
  border-radius: 50%;
  color: #ffffff;
  font-size: 22rpx;
}

.content_right {
  width: calc(100% - 160rpx);
  margin-left: 160rpx;

}

.multilevel_class {
  width: calc(100% - 170rpx);
  display: flex;
  align-items: center;
  overflow: scroll;
  max-height: 120rpx;
  background-color: #ffffff;
  padding: 10rpx 30rpx;
  box-sizing: border-box;
  align-content: flex-start;
}

.multiLine {
  flex-flow: column wrap;
}

.multilevel_class .title {
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: 2rpx 30rpx;
  font-size: 24rpx;
  height: 46rpx;
  color: #333333;
  border-radius: 44rpx;
}

.selected-third {
  background-color: #fdb551;
}

.select-third {
  background-color: #fff;
}

.fruit_grade {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  height: 60rpx;
}

.fruit_grade_list {
  padding: 8rpx 50rpx;
  background-color: #fdb551;
  font-size: 22rpx;
  border-radius: 44rpx;
  margin-right: 30rpx;
}

.fruit_grade_list_ {
  margin-right: 30rpx;
  padding: 8rpx 50rpx;
  background-color: #f6f6f6;
  font-size: 22rpx;
  border-radius: 44rpx;
}

.goods_list_wrap {
  padding: 0 20rpx 30rpx 20rpx;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  /* height: calc(100vh - 322rpx); */
  /* overflow-y: scroll; */
}

.goods_list {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
  padding-bottom: 10rpx;
  box-sizing: border-box;
}

.poster {
  border-right: 10rpx solid orange;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
}

.morning {
  border-right: 10rpx solid #0780d8;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
}

.goods_cover {
  width: 220rpx;
  height: 220rpx;
  border-radius: 8rpx;
  /* background-color: red; */
  position: relative;
}

.goods_tips_icon {
  width: 100rpx;
  height: auto;
  position: absolute;
  left: 0rpx;
  top: 0rpx;
}

.sold_out_icon {
  width: 100rpx;
  position: absolute;
  left: 0rpx;
  top: 0rpx;
}

.goods_content {
  flex: 1;
  margin-left: 10rpx;
  display: flex;
  flex-direction: column;
  height: 220rpx;
  justify-content: space-between;
}

.image {
  background-color: #fd8e49;
  border-radius: 50%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}


.goods_content_title {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.title_icon {
  background-color: #feebda;
  display: inline-flex;
  border-radius: 10rpx;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  position: relative;
  top: -7rpx;
  text-align: center;
  color: #be8970;
}

.goods_content_title text:nth-child(2) {
  font-size: 30rpx;
}


.sale_progress_wrap {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sale_progress {
  width: calc(100% - 90rpx);
  /* height: 28rpx; */
  background-color: #e8e8e8;
  border-radius: 44rpx;
  position: relative;
}

.sale_progress_size {
  height: 24rpx;
  line-height: 24rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 16rpx;
  color: #ffffff;
  font-size: 16rpx;
}

.stock {
  font-size: 20rpx;
  color: #999999;
}


.current_progress {
  padding: 0 15rpx;
  font-size: 20rpx;
  background-color: #FEA805;
  height: 30rpx;
  line-height: 30rpx;
  color: #ffffff;
  border-radius: 44rpx 0 0 44rpx;
}

.tag_wrap {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
}

.shop_vote_icon {
  width: 40rpx;
}

.tag_icon {
  margin-right: 6rpx;
  font-size: 16rpx;
  background-color: red;
  color: #ffffff;
  border-radius: 8rpx;
  padding: 4rpx 6rpx;
  box-sizing: border-box;
}


.no_price {
  color: red;
}


.price_ {
  font-size: 20rpx;
}

.piece_ {
  font-size: 30rpx !important;
  font-weight: bold;
  color: red;
}


.cart_icon {
  width: 40rpx;
}


/* 自定义tag */
.custom-tag {
  display: flex;
  gap: 8rpx;
  /* margin: 10rpx 0; */
  box-sizing: border-box;
  align-items: center;
  font-size: 24rpx;
  color: #5f5f5f;
  overflow: hidden;
  margin: 6rpx 0;
}


.line {
  height: 20rpx;
  border-left: 1rpx solid #b9b9b9;
}

.value {
  white-space: nowrap;
}


.price {
  color: red;
  font-weight: bolder;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
}


.price_wrap {
  margin-top: 10rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  font-size: 24rpx;
  margin-bottom: 20rpx;
}


.no_price {
  margin-top: 20rpx;
}

.no_price text:nth-child(1) {
  color: red;
  font-size: 22rpx;
}

.no_price text:nth-child(2) {
  color: red;
  font-size: 22rpx;
}

.no_price text:nth-child(3) {
  color: red;
  font-size: 30rpx;
}


.discount {
  border: 1px solid #f3a0a0;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: auto;
  height: 30rpx;
  margin-right: 10rpx;
  padding-right: 6rpx;
}

.text {
  background-color: red;
  border-radius: 10rpx 0 0 10rpx;
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx;
  box-sizing: border-box;
  line-height: 18rpx;
}

.buy {
  display: flex;
  align-items: center;
  /* border: 1px solid red; */
  height: 30rpx;
  color: #8d8b8b;
  margin-top: 10rpx;
}

.textStyle {
  background-color: #f7e1e5;
  padding: 0 10rpx;
  color: red;
  box-sizing: border-box;
  border-radius: 8rpx 0 0 8rpx;
}

.right {
  display: flex;
  font-size: 26rpx;
  align-items: center;
  padding: 10rpx 0;
}

.price-fmt {
  color: #858585;
  font-size: 20rpx;
  text-decoration: line-through;
  margin-left: 6rpx;
}

.origin-price {
  color: #afaaaa;
  font-size: 20rpx;
  text-decoration: line-through;
  margin-left: 6rpx;
}

.tip {
  color: #868484;
  padding: 30rpx;
  font-size: 28rpx;
  text-align: center;
  padding-bottom: 150rpx;
}

.made-poster {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #fff;
  width: 100%;
  padding: 10rpx 0 50rpx 0;
  display: flex;
  justify-content: center;
  box-shadow: 5rpx 5rpx 10rpx #777777;
}

.poster-content {
  width: 500rpx;
  text-align: center;
  background-color: orange;
  padding: 10rpx 0rpx;
  color: #fff;
  border-radius: 40rpx;
}

.promote-content {
  width: 500rpx;
  text-align: center;
  background-color: #0780d8;
  padding: 10rpx 0rpx;
  color: #fff;
  border-radius: 40rpx;
}