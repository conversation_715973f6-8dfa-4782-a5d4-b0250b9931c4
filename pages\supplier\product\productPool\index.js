import {
  deal<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  user<PERSON><PERSON><PERSON>,
  supplier<PERSON>ey,
  supplierIDKey
} from '../../../../utils/dict';

import {
  product_list_external,
  category_list_id,
  product_link,
  cart_update,
  cart_list,
  order_calc,
  purchase_create
} from '../../../../apis/productPool'
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import regeneratorRuntime from 'regenerator-runtime'

const app = getApp()
Page({

  data: {
    service_id: '647d77ef1db1e622b23c3339', // 昆明服务仓id
    product_list: [],
    allList: [],
    crossAxisCount: 2,
    crossAxisGap: 10,
    mainAxisGap: 10,
    imgUrl: app.globalData.imageUrl,
    activeKey: 0,
    loading: true,
    phoneParam: app.globalData.phoneParam,
    category_id_list: [],
    is_inner_supplier: false, // 服务仓内部供应商

    show_dialog: false,
    show_action: false,
    num: 1,
    action_cart_list: [],
    product_id: '',
    all_count: 0,
    product_info: {},
    order_product_list: [],
    total_product_amount: 0,
    total_weight: '',
    submit_list: [],
    is_submit: true,
    is_sure: true
  },

  async onLoad(options) {
    let serviceInfo = wx.getStorageSync(supplierKey)
    let supplier_point_id = serviceInfo.service_point_id
    let supplier_id = serviceInfo.id //供应商id
    let is_inner_supplier = false
    if (supplier_point_id != this.data.service_id) {
      supplier_id = ''
      await this.cartList()
      this.orderCalc()
    } else {
      is_inner_supplier = true
    }

    this.setData({
      loading: true,
      is_inner_supplier: is_inner_supplier,
    })
    await this.productList(supplier_id)
    await this.secondList()
    await this.linkProduct()
    this.setData({
      loading: false,
    })
  },

  onShow() {},


  // 购物车区块
  // 购物车列表
  cartList() {
    return new Promise((resolve) => {
      let data = {
        supplier_id: wx.getStorageSync(supplierIDKey)
      }
      cart_list(data).then(res => {
        if (res.data.code == 0) {
          let list = res.data.data
          if (!list) {
            list = []
          }
          let count = 0
          let order_list = []
          let submit_list = []
          list.forEach(ele => {
            ele.price_fmt = (ele.price / 100).toFixed(1)
            count += ele.count
            order_list.push({
                product_id: ele.product_id,
                num: ele.count,
                price: ele.price,
                rough_weight: ele.rough_weight
              }),
              submit_list.push({
                product_id: ele.product_id,
                num: ele.count,
              })
          })
          this.setData({
            action_cart_list: list,
            all_count: count,
            order_product_list: order_list,
            submit_list: submit_list
          })
        }

      }).catch(err => {
        Toast(err.data.message);
      }).finally(() => {
        resolve()
      })
    })
  },

  // 计算价格和数值
  orderCalc() {
    if (this.data.order_product_list.length == 0) {
      this.setData({
        total_product_amount: 0,
        total_weight: 0
      })
      return
    }
    let data = {
      product_list: this.data.order_product_list
    }
    order_calc(data).then(res => {
      if (res.data.code == 0) {
        let state = res.data.data
        this.setData({
          total_product_amount: dealFenToYuan(state.total_product_amount),
          total_weight: (state.total_weight / 1000).toFixed(1)
        })
      }
    })
  },

  handleOpen(e) {
    let info = e.currentTarget.dataset.info
    if (!info.sale) {
      Toast('该商品已下架')
      return
    }
    if (info.stock == 0) {
      Toast('该商品库存不足')
      return
    }
    this.setData({
      num: info.cart_num == 0 ? 1 : info.cart_num,
      show_dialog: true,
      product_id: info.id,
      product_info: info
    })
  },
  handleEdit(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      num: info.cart_num == 0 ? 1 : info.cart_num,
      show_dialog: true,
      show_action: false,
      product_id: info.product_id,
      product_info: info
    })
  },

  inputNum(e) {
    let stock = this.data.product_info.stock
    let num = this.data.num
    num++
    if (num >= stock) {
      Toast('库存不足')
      num = stock
    }

    this.setData({
      num: parseInt(e.detail.value),
    })
  },

  onClose() {
    this.setData({
      num: 1,
      show_dialog: false
    })
  },

  onConfirm() {
    if ( this.data.num >= 0) {
    }else{
      Toast('请输入购买数量')
      return
    }

    this.setData({
      is_sure: false
    })
    
    let num = this.data.num
    let list = this.data.product_list
    let id = this.data.product_id
    list.forEach(ele => {
      if (ele.id == id) {
        ele.cart_num = num
      }
    })

    let data = {
      product_id: id,
      supplier_id: wx.getStorageSync(supplierIDKey),
      num: parseInt(num)
    }

    cart_update(data).then(async res => {
      if (res.data.code == 0) {
        Toast('更新成功')
        await this.cartList()
        this.orderCalc()
        this.setData({
          num: 1,
          show_dialog: false,
          is_sure: true,
          product_list: list
        })
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_sure: true
      })
    })
  },

  handleAction() {
    this.setData({
      show_action: !this.data.show_action
    })
  },
  handleCloseAction() {
    this.setData({
      show_action: false
    })
  },
  toPurchaseList(){
    wx.navigateTo({
      url: '/pages/supplier/purchase/index',
    })
  },
  submit() {
    if (this.data.submit_list.length == 0) {
      Toast('请选择商品')
      return
    }
    this.setData({
      is_submit: false
    })
    let data = {
      product_list: this.data.submit_list
    }
    purchase_create(data).then(res => {
      if (res.data.code == 0) {
        this.setData({
          is_submit: true,
        })
        Dialog.confirm({
            title: '状态',
            message: '提交成功',
            confirmButtonText: '查看订单',
            zIndex: 1000
          })
          .then(() => {
            this.setData({
              all_count: 0,
            })
            this.toPurchaseList()
          })
          .catch(() => {
            this.setData({
              is_submit: true,
              all_count: 0,
              total_product_amount: 0,
              total_weight: 0,
              submit_list: [],
              order_product_list: []
            })

          });
      }
    }).catch(err => {
      Toast(err.data.message)
      this.setData({
        is_submit: true
      })
    })
  },


  // 拉取商品库
  productList(id) {
    return new Promise((resolve) => {
      let data = {
        service_point_id: this.data.service_id, // 昆明服务仓|D
        supplier_id: id
      }
      product_list_external(data).then(res => {
        if (res.data.code == 0) {
          let category_id_list = []
          let product_list = res.data.data
          if (!product_list) {
            product_list = []
          }
          let cart_list = this.data.action_cart_list
          product_list.forEach(ele => {
            let category_id = ele.category_ids[1]
            if (category_id_list.indexOf(category_id) === -1) {
              category_id_list.push(category_id);
            }
            if (ele.has_param && ele.product_param_type === 1) {
              //  水果
              if (!ele.custom_tag_list) {
                ele.custom_tag_list = []
              }
              //  果径
              if (ele.non_standard_attr.width > 0) {
                ele.custom_tag_list.unshift({
                  key: '果径',
                  obj: 'width',
                  value: ele.non_standard_attr.width + 'mm'
                })
              }
              ele.custom_tag_list.unshift({
                key: '等级',
                obj: 'level',
                value: ele.non_standard_attr.fruit_class_name
              })
            }

            if (ele.has_param) {
              // 有参数
              // 单价
              if (ele.product_param_type == 1) {
                ele.price_per_fmt = ((ele.price / ele.weight.rough_weight) * 10).toFixed(2)
                if (ele.origin_price > 0) {
                  ele.origin_price_per_fmt = (ele.origin_price / ele.weight.rough_weight * 10).toFixed(2)
                  ele.origin_price_fmt = (ele.origin_price / 100).toFixed(2)
                }
              }
              if (ele.product_param_type == 2) {
                ele.price_per_fmt = ((ele.price / ele.standard_attr.included_num) / 100).toFixed(2)
                ele.price_per_fmt += '/' + ele.standard_attr.unit_name
                if (ele.origin_price > 0) {
                  ele.origin_price_per_fmt = ((ele.origin_price / ele.standard_attr.included_num) / 100).toFixed(2)
                  ele.origin_price_per_fmt += '/' + ele.standard_attr.unit_name
                  ele.origin_price_fmt = (ele.origin_price / 100).toFixed(2)
                }
              }
            }
            ele.price_unit = ((ele.price / 100) / (ele.weight.rough_weight / 1000)).toFixed(1)
            ele.weight.rough_weight_fmt = (ele.weight.rough_weight / 1000).toFixed(1)
            let p = ele.price
            ele.price_fmt = (p / 100)
            let origin_p = ele.origin_price
            ele.origin_price_fmt = (origin_p / 100).toFixed(2)


            // 在购物车的数量
            ele.cart_num = 0
            cart_list.forEach(item => {
              if (item.product_id == ele.id) {
                ele.cart_num = item.count
              }
            })
          })

          this.setData({
            category_id_list: category_id_list,
            product_list: product_list,
          })
        }
      }).catch(err => {
        Toast(err.data.message);
      }).finally(() => {
        resolve()
      })
    })
  },

  // 二级标题
  secondList() {
    return new Promise((resolve) => {
      let category_id_list = this.data.category_id_list
      let param = {
        category_id_list: category_id_list
      }
      category_list_id(param).then(res => {
        if (res.data.code == 0) {
          if (res.data.data !== null) {
            let data = res.data.data
            let product_list = this.data.product_list
            data.forEach(ele => {
              let item = product_list.filter(item => item.category_ids[1] == ele.id);
              ele.list = item
            })
            this.setData({
              allList: data,
              product_list: data[0].list,
            })
          }
        }
      }).catch(err => {
        Toast(err.data.message);
      }).finally(() => {
        resolve()
      })
    })
  },
  // 关联商品id
  linkProduct() {
    return new Promise((resolve) => {
      product_link().then(res => {
        if (res.data.code == 0) {
          let list = this.data.product_list
          let product_link_list = res.data.data
          if (!product_link_list) {
            product_link_list = []
          }
          list.forEach(ele => {
            ele.is_link_product = false
            product_link_list.forEach(item => {
              if (ele.id == item) {
                ele.is_link_product = true
              }
            })

          })

          this.setData({
            product_list: list,
          })
        }

      }).catch(err => {
        Toast(err.data.message);
      }).finally(() => {
        resolve()
      })
    })
  },

  change(e) {
    this.setData({
      activeKey: e.detail
    })
  },
  nextClassSWitch(e) {
    this.setData({
      product_list: e.currentTarget.dataset.info.list
    })
  },
  jumpGoodsDetail(e) {
    let id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  }

})