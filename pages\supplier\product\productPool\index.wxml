<!--pages/classify/classify.wxml-->
<view class="container">
  <view class="title-top" wx:if="{{is_inner_supplier}}">对外共享商品库</view>
  <view class="purchase-btn" wx:if="{{!is_inner_supplier}}" bind:tap="toPurchaseList">
    <text style="border-bottom: 1rpx solid #409eff;" >进货单 ></text>
  </view>

  <!-- </van-sticky> -->
  <view style="display: flex;">
    <view class="sidebar" style="position: fixed;top: 60rpx;height: 100%;">
      <van-sidebar active-key="{{ activeKey }}" bind:change="change">
        <view wx:for="{{allList}}" wx:key="key" data-info="{{item}}" bindtap="nextClassSWitch">
          <van-sidebar-item title="{{item.name}}" badge="{{item.list.length}}" />
        </view>
      </van-sidebar>
      <!-- 勿删 -->
      <view style="height: 250rpx;"></view>
    </view>
    <view class="content_right">
      <view class="goods_list_wrap">
        <view class="goods_list" wx:for="{{product_list}}" wx:key="key" data-id="{{item.id}}" data-info="{{item}}" bindtap="jumpGoodsDetail">
          <view class="goods_cover">
            <image lazy-load="{{true}}" src="{{item.cover_img.name?imgUrl+item.cover_img.name:''}}" class="goods_cover" mode="aspectFill" />
          </view>
          <view class="goods_content">
            <view class="goods_content_title">
              <van-tag wx:if="{{item.is_link_product}}" type="success">已关联</van-tag>
              <text>{{item.title}}</text>

            </view>
            <!--标签  -->
            <view class="custom-tag" wx:if="{{item.custom_tag_list!==null}}">
              <block wx:for="{{item.custom_tag_list}}" wx:key="key" wx:for-item="Pitem">
                <view class="per">
                  <view class="value" wx:if="{{Pitem.obj=='width'}}" style="color: #3a97ca;">{{Pitem.value}}</view>
                  <view class="value" wx:if="{{Pitem.obj=='level'}}" style="color: #46a468;background-color: #e9faf1;border-radius: 6rpx;">{{Pitem.value}}</view>
                  <view class="value" wx:if="{{!Pitem.obj}}">{{Pitem.value}}</view>
                </view>
                <view wx:if="{{index<item.custom_tag_list.length-1}}" class="line">
                </view>
              </block>
            </view>

            <view class="sale_progress_wrap">
              <view class="stock">库存 {{item.stock}}</view>
              <view class="stock">重量 {{item.weight.rough_weight_fmt}}kg</view>
            </view>

            <view class="price_wrap" wx:if="{{item.price>0}}">
              <view style="width: 100%;justify-content:space-between;">
                <view class="price" wx:if="{{item.has_param&&item.is_check_weight}}">
                  <text>￥{{item.price_fmt}}/{{item.product_unit_type_name}}</text>
                  <view style="color: #b3b0b0;font-size: 24rpx;font-weight: normal;">
                    <text>￥</text>
                    <text>{{item.price_per_fmt}}/kg</text>
                  </view>
                </view>

                <view class="price" wx:if="{{item.has_param&&!item.is_check_weight}}">
                  <text>￥</text>
                  <text>{{item.price_fmt}}</text>
                  <text style="color: #858585; font-size: 24rpx; font-weight: normal;">/{{item.product_unit_type_name}}</text>
                </view>
                <view class="price" wx:if="{{!item.has_param}}">
                  <text>￥</text>
                  <text>{{item.price_fmt}}</text>
                  <text style="color: #858585; font-size: 24rpx; font-weight: normal;">/{{item.product_unit_type_name}}</text>
                </view>
              </view>

              <view style="text-align: right;position: relative;" data-info="{{item}}" catch:tap="handleOpen" wx:if="{{!is_inner_supplier}}">
                <text class="cart-num">{{item.cart_num}}</text>
                <image src="/static/point/add.png" style="width: 50rpx; height: auto;" mode="widthFix" />
              </view>
            </view>
          </view>
        </view>

        <van-empty wx:if="{{allList.length==0}}" description="暂无内容" />
      </view>
    </view>
  </view>
</view>

<view class="bottom-box" wx:if="{{!is_inner_supplier}}">
  <view class="content-box">
    <view class="content-left">
      <view style="position: relative;margin:0 40rpx 0 20rpx;" bind:tap="handleAction">
        <view class="count">{{all_count}}</view>
        <image src="/static/point/cart.png" style="width: 60rpx;height: auto;" mode="widthFix" />
      </view>
      <view>
        <text>合计：</text>
        <text style="color: #fff;font-weight: bold;">￥</text>
        <text style="font-size: 40rpx;color: #fff;font-weight: bold;">{{total_product_amount}}</text>
        <view style="font-size: 24rpx;color: #b3b0b0;" wx:if="{{total_weight}}">重量：{{total_weight}}kg</view>
      </view>
    </view>
    <view class="submit" catch:tap="submit" wx:if="{{is_submit}}">提交</view>
    <view class="submit" wx:if="{{!is_submit}}">提交中...</view>
  </view>
</view>

<van-overlay show="{{ loading }}" z-index="9999" custom-style="background-color:rgba(0, 0, 0, 0.5);" bind:click="onClickHide">
  <view style="display: flex;justify-content: center;margin-top: calc({{phoneParam.safeArea.height / 2}}rpx + 100rpx);">
    <van-loading size="24px" color="#ffffff" vertical><text style="color: #ffffff;">加载中...</text></van-loading>
  </view>
</van-overlay>

<van-dialog use-slot z-index="{{1000}}" title="" show="{{ show_dialog }}" show-confirm-button="{{false}}" bind:close="onClose">
  <view class="modalDlg">
    <view class="input">
      <view class="view-contain-ti">
        <text class="text-ti">购买数量</text>
        <input class="input1" type="number" value="{{num}}" bindinput="inputNum" cursor-spacing="30"></input>
      </view>

      <view class="stock">
        库存 {{product_info.stock}}
      </view>
    </view>
    <view style="width: 100%;display: inline-flex;">
      <view style="width:100rpx" class='cancelbnt' bindtap='onClose'>取消</view>
      <view style="width:60%" class='wishbnt' bindtap='onConfirm' wx:if="{{is_sure}}">确定</view>
      <view style="width:60%" class='wishbnt' wx:else>确定...</view>
    </view>
  </view>
</van-dialog>



<van-action-sheet show="{{ show_action }}" title="商品" close-on-click-overlay bind:close="handleCloseAction">
  <view style="padding: 20rpx;max-height: 500rpx;overflow-y: scroll;">
    <view class="list-box" wx:for="{{action_cart_list}}" wx:key="id">
      <view>
        <image wx:if="{{item.product_cover.name}}" src="{{imgUrl + item.product_cover.name}}" style="width: 100rpx;height: auto;" mode="widthFix" />
      </view>
      <view class="title-box">
        <view>
          <text class="supplier-name">{{item.supplier_name}}</text>
          <text>{{item.product_title}}</text>

        </view>
        <view style="margin-top: 16rpx;display: flex;justify-content: space-between;">
          <text style="color: red;">￥{{item.price_fmt}}/{{item.product_unit_type_name}}</text>
          <view>
            <text style="font-size: 26rpx; color: #858585;margin-right: 20rpx;" data-info="{{item}}" catch:tap="handleEdit">编辑数量</text>
            <text>x {{item.count}}</text>
          </view>

        </view>
      </view>
    </view>
  </view>
  <view style="height: 180rpx;"></view>
</van-action-sheet>

<van-dialog id="van-dialog" />
<van-toast id="van-toast" />