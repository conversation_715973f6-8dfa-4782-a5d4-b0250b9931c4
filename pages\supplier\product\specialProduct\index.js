import {
  apply_list,
  supplier_list
} from '../../../../apis/zone';
import {
  categoryCoverProcess
} from '../../../../utils/dict';
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabNames: 'zone',
    applyList: [],
    imageUrls: app.globalData.imageUrl + categoryCoverProcess,
    imageUrl: app.globalData.imageUrl,
    statusText: '',
    supplierList: []
  },


  onLoad(options) {
    this.supplierLists()
  },
  // 团购列表

  supplierLists() {
    let supplier_id = wx.getStorageSync('supplierid')
    let data = {
      index_part_id: "652210d71e1d890a1fa47694",
      supplier_id: supplier_id
    }
    supplier_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        if (list != null && list != []) {
          list.forEach(item => {
           
         
            item.price_fmt = (item.price / 100).toFixed(2)
            item.origin_price_fmt = (item.origin_price / 100).toFixed(2)

            let pUnit = 0
            if (item.is_check_weight) {
              let p = item.price / 100
              let w = item.weight.rough_weight / 1000
              pUnit = (p / w).toFixed(2)
            }
            item.priceUnit = pUnit
          })
          this.setData({
            supplierList: list,
          })
        }
      }
    })
  },



  changTab(e) {
    this.setData({
      tabNames: e.detail.name
    })
    if (this.data.tabNames == 'apply') {
      this.applyList()
    }
  },
  applyList() {
    let supplier_id = wx.getStorageSync('supplierid')
    let data = {
      index_part_id: "652210d71e1d890a1fa47694",
      supplier_id: supplier_id
    }
    apply_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data
        list.forEach(item => {
          this.status(item.apply.audit_status)
          item.price_list.forEach(items => {
            items.price_fmt = (items.price / 100).toFixed(2)
          })
          item.apply.apply_price_list.forEach(items => {
            items.price_apply_fmt = (items.price / 100).toFixed(2)
          })
          let pUnit = 0
          if (item.is_check_weight) {
            let p = item.price_list[0].price / 100
            let w = item.weight.rough_weight / 1000
            pUnit = (p / w).toFixed(2)
          }
          item.priceUnit = pUnit
        })
        this.setData({
          applyList: list,
        })
      }

    })
  },

  status(status) {
    let s = ''
    switch (status) {
      case 1:
        s = '审核中'
        break;
      case 2:
        s = '通过'
        break;
      case 3:
        s = '不通过'
        break;
      default:
        break;
    }

    this.setData({
      statusText: s
    })
    console.log

  },

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  },

  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})