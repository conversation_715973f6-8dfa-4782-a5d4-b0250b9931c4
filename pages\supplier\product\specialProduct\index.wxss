/* pages/supplier/product/specialProduct/index.wxss */
page{
  background-color: #edeff1
}
.prompt{
  margin: 20rpx;
  font-size: 28rpx;
}
.list{
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
}
.textTitle{
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  white-space: normal;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 1.3em;
  max-height: 2.6em;
}
.listContent{
  display: flex;
  
}
.listRight{
  margin-left: 20rpx;
  width: calc(100vw - 300rpx);
}

.check_weight_tip {
  display: flex;
}

.check_weight_tip .word{
  background-color: #11aa66;
  display: flex;
  white-space: nowrap;
  color: #ffffff;
  font-size: 20rpx;
  padding: 1rpx 4rpx;
  border-radius: 6rpx;
}

.applyStatus{
  font-size: 30rpx;
  color: #f70909;
  /* text-align: center; */
  padding: 4rpx;
  box-sizing: border-box;
  margin-bottom: 10rpx;
}