import {
  purchase_get,
} from "../../../../apis/purchase"
import {
  dealTimeFormat1
} from "../../../../utils/dict"
import Toast from '@vant/weapp/toast/toast';
const app = getApp()
Page({

  data: {
    order_detail: {},
    imgUrl: app.globalData.imageUrl,
    order_status: '',
    steps: [{
        text: '',
        desc: '',
        inactiveIcon: 'clock-o',
        activeIcon: 'clock-o', 
      },
      {
        text: '',
        desc: '',
        inactiveIcon: 'circle',
        activeIcon: 'circle',
      },
    ],
    id: '',
  },

  onLoad(options) {
    let id = options.id
    this.getInfo(id)
    this.setData({
      id: id
    })
  },

  // 查询详情
  getInfo(id) {
    let data = {
      id: id
    }
    purchase_get(data).then(res => {
      if (res.data.code == 0) {
        let info = res.data.data
        info.total_product_amount_fmt = (info.total_product_amount / 100).toFixed(2)
        info.total_purchase_product_amount_fmt = (info.total_purchase_product_amount / 100).toFixed(2)
        
        info.created_at_fmt = dealTimeFormat1(info.created_at)
        info.order_status_fmt = this.backStatus(info.order_status,info.pay_status)
        if (info.product_list) {
          info.product_list.forEach(ele => {
            ele.price_fmt = (ele.price / 100).toFixed(2)
            if(ele.sort_status == 2){
              ele.purchase_product_amount_fmt = (ele.purchase_product_amount / 100).toFixed(2)
              if(ele.is_check_weight){
                ele.purchase_product_per = ((ele.purchase_product_amount / ele.sort_weight)*10).toFixed(2)
              }
            }
          })
        }

        this.setData({
          order_detail: info
        })
        this.dealStep()
      }
    }).catch(err => {

    })
  },

  dealStep() {
    let order = this.data.order_detail
    let steps = this.data.steps
    steps[1].desc = order.purchase_supplier_name
    let status = this.backStatus(order.order_status,order.pay_status)
    steps[0].text = status
    steps[0].desc = ''
    this.setData({
      steps: steps
    })
  },

  backStatus(order_status, pay_status) {
    let order_status_fmt = ''
    switch (order_status) {
      case 1:
        order_status_fmt = '已关闭';
        break
      case 2:
        order_status_fmt = '已取消';
        break
      case 31:
        order_status_fmt = '待确定';
        break
      case 41:
        order_status_fmt = '采购中';
        break
      case 51:
        order_status_fmt = '已发货';
        break
      case 61:
        order_status_fmt = '已完成';
        break
    }

    // if (order_status === 51 && (pay_status === 99 || pay_status === 1)) {
    //   order_status_fmt = '待付款'
    // }

    return order_status_fmt
  },

  

  jumpProductDetail(e) {
    let id = e.currentTarget.dataset.info.product_id
    wx.navigateTo({
      url: '/pages/supplier/product/info/index?id=' + id,
    })
  },

  //  复制订单编号
  copyOrderNum() {
    let v = this.data.order_detail.id_num
    wx.setClipboardData({
      data: v,
      success: function () {}
    })
  },

  onShow() {

  },

})