<view class="container">

  <view class="part">
    <van-steps custom-class="step-custom" desc-class="step-desc" steps="{{ steps }}" active="{{ 0 }}" direction="vertical" inactive-color="#fd5003" bind:click-step="step" />
  </view>

  <view class="pay_content_wrap part">
    <view style="display: flex;justify-content: space-between;align-items: centerc;">
      <view style="font-size: 28rpx;">{{order_detail.supplier_name}}</view>
      <van-tag type="warning">{{order_detail.order_status_fmt}}</van-tag>
    </view>
    <view class="goods_content" wx:for="{{order_detail.product_list}}" wx:for-item="items" wx:key="key">
      <image class="goods_cover" bindtap="jumpProductDetail" data-info="{{items}}" src="{{items.product_cover_img.name?imgUrl+items.product_cover_img.name:''}}" mode="" />
      <view class="goods_base_param">
        <view bindtap="jumpProductDetail" data-info="{{items}}">
          {{items.product_title}}
        </view>
        <view style="display: flex;justify-content: space-between;align-items: center;">
          <view class="goods_price" style="margin-right: 20rpx; color: #fd9b1a;">
            <text style="font-size: 28rpx;">￥</text>
            <text style="font-size: 28rpx;">{{items.price_fmt}}</text>
          </view>
          <view>x{{items.num}}</view>
        </view>

        <view style="display: flex;justify-content: space-between;align-items: center;" wx:if="{{items.sort_status == 2}}">
          <view class="goods_price" style="margin-right: 20rpx; color: #362002;">
            <text style="font-size: 24rpx;">采购金额: ￥{{items.purchase_product_amount_fmt}}</text>
            <view style="font-size: 24rpx;">采购单价：{{items.purchase_product_per}}/kg</view>
          </view>
          <view style="font-size: 24rpx;">x{{items.sort_num}}</view>
        </view>

      </view>
    </view>

    <view class="order_detail">
      <view class="title-line">
        <text class="leftTitle">商品总额：</text>
        <text style="margin-left: 10rpx; font-weight: bold;">￥{{order_detail.total_product_amount_fmt}}</text>
      </view>

      <view class="title-line">
      <text class="leftTitle">采购总金额：</text>
      <text style="margin-left: 10rpx; font-weight: bold;">￥{{order_detail.total_purchase_product_amount_fmt}}</text>
    </view>

      <view style="border-bottom: 2rpx solid  #f4f4f4;"></view>
      <view class="title-line">
        <text class="leftTitle">订单编号：</text>
        <text>{{order_detail.id_num}}<text bind:tap="copyOrderNum" style="font-size: 24rpx;color: #a8a8a8;margin-left: 10rpx;">复制</text></text>
      </view>
      <!-- <view class="title-line" wx:if="{{order_detail.pay_status===4}}">
        <text class="leftTitle">支付方式：</text>
        <text wx:if="{{order_detail.pay_method==='wechat'}}">微信支付</text>
        <text wx:if="{{order_detail.pay_method==='balance'}}">钱包支付</text>
      </view> -->

      <view class="title-line">
        <text class="leftTitle">订单时间：</text>
        <text>{{order_detail.created_at_fmt}}</text>
      </view>
    </view>
  </view>


  <view class="btn" wx:if="{{order_detail.order_status==31}}">
    <!-- TODO 待定 -->
    <view class="pay" catch:tap="handlePay" wx:if="{{is_pay}}">支付订单</view>
    <view class="pay" wx:if="{{is_pay}}">支付中...</view>
  </view>

</view>

<van-toast id="van-toast" />