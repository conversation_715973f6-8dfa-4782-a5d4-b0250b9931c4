.container{
  background-color: #f2f2f2;
  padding: 16rpx;
  box-sizing: border-box;
  height: 100vh;
}


.part{
  border-radius: 20rpx;
  background-color: #ffffff;
  padding: 16rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.pay_content_wrap {
  box-sizing: border-box;
}

.goods_cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}

.goods_content {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.goods_base_param {
  width: calc(100% - 180rpx);
}
.order_detail {
  margin-top: 50rpx;
  font-size: 24rpx;
}

.info {
  margin-top: 20rpx;
}

.title-line {
  font-size: 26rpx;
  margin-bottom: 12rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.leftTitle {
  width: 19%;
  display: inline-block;
  white-space: nowrap;
}
.btn{
  display: flex;
  justify-content: flex-end;
}


.pay{
  background-color: #059234;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  color: #fff;
  margin-left: 10rpx;
}