import {
  dealTimeFormat2,
  supplierIDKey
} from '../../../utils/dict';
import {
  purchase_list,
  order_delete,
  order_close
} from "../../../apis/purchase"
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
const app = getApp()
Page({

  data: {
    imgUrl: app.globalData.imageUrl,
    page: 1,
    is_end: false,
    list: [],
    show_more: false,
    actions: [],
    order_id: '',
    index: '',
    purchase_parent_order_id:''
  },

  onLoad(options) {

  },

  // 查询列表
  purchaseList() {
    if (this.data.is_end === true) {
      return
    }
    let page = this.data.page
    let data = {
      supplier_id: wx.getStorageSync(supplierIDKey),
      page: page,
      limit: 5,
    }

    purchase_list(data).then(res => {
      if (res.data.code == 0) {
        let list = res.data.data.list
        let count = res.data.data.count
        if (!list) {
          list = []
        }
        list.forEach(ele => {
          // wx:if="{{item.order_status == 1}}"
        
          ele.created_at_fmt = dealTimeFormat2(ele.created_at)
          ele.status = this.orderStatus(ele.order_status)
          ele.product_list.forEach(item => {
            item.price_fmt = (item.price / 100).toFixed(1)
            item.purchase_product_amount_fmt = (item.purchase_product_amount / 100).toFixed(1)
            if (item.is_check_weight) {
              item.price_per_fmt = ((item.price / item.rough_weight) * 10).toFixed(2)
              item.purchase_product_per = ((item.purchase_product_amount / item.sort_weight) * 10).toFixed(2)
            }
          })
        })
        let newList = [...this.data.list, ...list]

        let is_end = 5 * page >= count
        if (!is_end) {
          page++
        }
        this.setData({
          list: newList,
          is_end: is_end,
          page: page,
        })

      }
    })
  },

  orderStatus(status) {
    switch (status) {
      case 1:
        return '已关闭';
      case 2:
        return '已取消';
      case 31:
        return '待确定';
      case 41:
        return '采购中';
      case 51:
        return '已发货';
      case 61:
        return '已完成';
    }
  },


  // 去详情
  toDetail(e) {
    let id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: './detail/index?id=' + id,
    })
  },

  //删除
  handleMore(e) {
    let info = e.currentTarget.dataset.item
    let id = e.currentTarget.dataset.item.id
    let purchase_parent_order_id = e.currentTarget.dataset.item.purchase_parent_order_id
    let index = e.currentTarget.dataset.index
    if (info.order_status == 1) {
      this.setData({
        actions: [{
          name: '删除',
          subname: '删除后不可恢复',
          color: '#ee0a24'
        }]
      })
    }
    if (info.order_status == 31) {
      this.setData({
        actions: [{
          name: '关闭',
          color: '#ee0a24'
        }]
      })
    }

   
    this.setData({
      show_more: true,
      order_id: id,
      index: index,
      purchase_parent_order_id: purchase_parent_order_id
    })
  },

  handleSelect(e) {
    let data = {
      id: this.data.order_id
    }
    if (e.detail.name == '删除') {
      this.setData({
        show_more: false,
      })
      Dialog.confirm({
          title: '删除',
          message: '确定删除？',
        })
        .then(() => {
          order_delete(data).then(res => {
            if (res.data.code == 0) {
              Toast('删除成功')
              this.setData({
                page: 1,
                is_end: false,
                list: []
              })
              this.purchaseList()
            }
          }).catch(err => {
            Toast(err.data.message)
          })

        })
        .catch(() => {
          this.setData({
            show_more: false,
          })
        });
    }
    if (e.detail.name == '关闭') {
      this.setData({
        show_more: false,
      })
      this.handleClose()
    }

  },

   // 关闭订单
   handleClose() {
     let that = this
    let data = {
      purchase_parent_order_id: this.data.purchase_parent_order_id
    }
    wx.showModal({
      title: '提示',
      content: '确定关闭订单？',
      success(res) {
        if (res.confirm) {
          order_close(data).then(res => {
            if (res.data.code == 0) {
              Toast('关闭成功');
              let index = that.data.index
              let key = `list[${index}].order_status`
              let status = `list[${index}].status`
              that.setData({
                [key]: 1,
                [status]: '已关闭'
              })
            }
          }).catch(err => {
            Toast(err.data.message);
          })

        } else if (res.cancel) {
          that.setData({
            show_more: false,
          })
        }
      }
    })
  },

  handleCancle() {
    this.setData({
      show_more: false
    })
  },



  onShow() {
    this.setData({
      page: 1,
      is_end: false,
      list: []
    })
    this.purchaseList()
  },
  onReachBottom() {
    this.purchaseList()
  },
})