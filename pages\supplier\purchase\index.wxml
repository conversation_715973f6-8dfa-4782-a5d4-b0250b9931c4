<view class="order_list_wrap" wx:for="{{list}}" wx:key="key">
  <view class="order_list" wx:if="{{!item.hide}}" >
    <view style="font-size: 28rpx;display: flex;justify-content: space-between;">
      <view>{{item.supplier_name}}</view>
      <van-tag type="warning">{{item.status}}</van-tag>
    </view>
    <view class="goods_info" wx:for="{{item.product_list}}" data-info="{{item}}" wx:for-item="ele" wx:key="key" data-id="{{item.id}}" bind:tap="toDetail">
      <view class="goods_cover">
        <image class="goods_cover" src="{{ele.product_cover_img.name?imgUrl+ele.product_cover_img.name:''}}" mode="aspectFill" />
      </view>
      <view class="goods_param_content">
        <view>
          <text class="subsidy-tag">{{item.supplier_name}}</text>
          {{ele.product_title}}
        </view>
        <view class="price_and_amount">
          <view class="goods_price">
            <view>
              <text>￥</text>
              <text>{{ele.price_fmt}}</text>
            </view>
            <view style="font-size: 26rpx;color: #9b9c9c; margin-left: 15rpx;" wx:if="{{ele.is_check_weight}}">￥{{ele.price_per_fmt}}/kg</view>
          </view>
          <view class="goods_amount">x{{ele.num}}</view>
        </view>

        <view class="price_and_amount" style="font-size: 24rpx;margin-top: 0;" wx:if="{{ele.sort_status == 2}}">
          <view class="goods_price">
            <view>
              <text>采购金:￥{{ele.purchase_product_amount_fmt}}</text>
            </view>
            <view style="font-size: 24rpx;color: #9b9c9c; margin-left: 15rpx;" wx:if="{{ele.is_check_weight}}">￥{{ele.purchase_product_per}}/kg</view>
          </view>
          <view class="goods_amount">x{{ele.sort_num}}</view>
        </view>

      </view>
    </view>
    <view style="display: flex;align-items: center;justify-content: space-between;margin-top: 20rpx;">
      <view style="color: #9b9c9c;font-size: 26rpx;">{{item.created_at_fmt}}</view>
      <view data-index="{{index}}"  data-item="{{item}}" bind:tap="handleMore" wx:if="{{item.order_status == 1 || item.order_status == 31}}">
        <image src="{{imgUrl+ 'icon/ellipsis2.png'}}" style="width: 40rpx;height: auto;" mode="widthFix"></image>
      </view>
    </view>
  </view>
</view>

<view style="height: 50rpx;"></view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-action-sheet show="{{ show_more }}" actions="{{ actions }}" cancel-text="取消" bind:select="handleSelect" bind:cancel="handleCancle" bind:click-overlay="handleCancle" />