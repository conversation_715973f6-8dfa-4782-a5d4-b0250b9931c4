/* pages/warehouse/center/index.wxss */
.base_bg {
  /* width: 100%; */
  /* height: 400rpx; */
  /* background-color:#fd9b1a; */
  /* border-radius: 0 0 44rpx 44rpx; */
  /* margin-top: 160rpx; */
}

page {
  background-color: #f6f6f6;
}

.bg {
  width: 100%;
  position: fixed;
  top: 0;
  z-index: -1;
  height: 600rpx;
}

.base_info {
  display: flex;
  align-items: flex-start;
  padding: 50rpx;
  box-sizing: border-box;
  margin-bottom: 140rpx;
}

.store_cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.amount_module_wrap {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.amount_module {
  width: 100%;
  height: 160rpx;
  background-color: #e5e5e5;
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  border-radius: 16rpx;
  color: #ffffff;
  position: relative;
  bottom: -80rpx;
}

.amount_module_list {
  width: calc(100% / 2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.amount_module_list view:nth-child(1) {
  font-size: 32rpx;
}

.amount_module_list view:nth-child(2) {
  margin-top: 10rpx;
  font-size: 40rpx;
}


.function_list {
  padding: 0 20rpx;
}


.function {
  display: flex;
  padding: 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  font-size: 22rpx;
  box-sizing: border-box;
}

.functionBox {
  width: 100%;
  width: calc(100% / 5);
  text-align: center;
}

.detail_form_title_wrap {
  display: flex;
  height: 100rpx;
}

.detail_form_title_wrap view {
  border: 2rpx solid #e5e5e5;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  width: 20%;
}

