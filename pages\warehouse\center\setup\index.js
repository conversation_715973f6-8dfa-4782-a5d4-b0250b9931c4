// pages/supplier/center/setup/index.js
const app = getApp()
import {
  send_code,
  bind_phone
} from '../../../../apis/servicePoint/center'
import {
  upload_sign,
} from '../../../../utils/api';
const uploadFile = require('../../../../utils/uploadFile');
const util = require('../../../../utils/util');
import {
  update_avatar
} from '../../../../apis/supplier/center';
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    formData: {
      mobile: "",
      captcha: "",
    },
    payPhone: 0,
    currentTime: "获取验证码", //倒计时
    imgUrl: app.globalData.imageUrl,
    fileList:[]
  },


  // 手机验证码
  sms(e) {
    this.setData({
      'formData.captcha': e.detail
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this
    this.setData({
      'formData.mobile': options.payPhone,
      payPhone: options.payPhone,
      isbindphone: options.isbindphone,
      imgs:options.imgs,
    })
  },
  //发送验证码
  sendCode() {
    let that = this
    let data = {
      mobile: this.data.payPhone
    }
    send_code(data).then(res => {
      if (res.data.code == 0) {
        that.verificationCode()
        Toast('短信验证码已发送');
      }
    })
  },

  // 验证码倒计时
  verificationCode() {
    let that = this
    let currentTime = 60
    //设置一分钟的倒计时
    var interval = setInterval(function () {
      currentTime--;
      //每执行一次让倒计时秒数减一
      that.setData({
        currentTime: currentTime + 's', //按钮文字变成倒计时对应秒数
        disabled: true
      })
      //如果当秒数小于等于0时 停止计时器 且按钮文字变成重新发送 
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          currentTime: '重新发送',
          disabled: false,
          color: 'red'
        })
      }
    }, 1000);
  },
  //提交
  submit() {
    if (this.data.formData.captcha == '') {
      Toast('请输入验证码');
      return
    }
    bind_phone(this.data.formData).then(res => {
      if (res.data.code == 0) {
        Toast('保存成功');
        setTimeout(function () {
          wx.navigateBack()
        }, 2000)
      }
    }).catch(err => {
      Toast(err.data.message);
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})