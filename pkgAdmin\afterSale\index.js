import {
  order_refund_list,
} from '../../apis/vip'
const app = getApp()
import {
  dealTimeFormat1,
  categoryCoverProcess
} from '../../utils/dict';

Page({

  data: {
    page: 1,
    list: [],
    imageUrl: app.globalData.imageUrl,
    categoryCoverProcess: categoryCoverProcess,

    isEnd: true
  },

  onLoad(options) {
    this.refundList()
  },

  // 售后列表
  refundList() {
    if (!this.data.isEnd) {
      return
    }
    let page = this.data.page++
    let data = {
      audit_status: 1,
      Page: page,
      limit: 10,
      refund_type: 1,
      withdraw_status: 1,
      service_point_id: '647d77ef1db1e622b23c3339'
    }
    order_refund_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data.list !== null) {
          list = res.data.data.list
          list.forEach(ele => {
            ele.amount_fmt = (ele.amount / 100).toFixed(2)
            ele.refund_weight_fmt = (ele.refund_weight / 1000).toFixed(2)
            ele.create_at_fmt = dealTimeFormat1(ele.created_at)
          })
        }
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          isEnd: this.data.list.length < res.data.data.count
        })
      }
    })
  },

  toOrderDetail(e) {
    let product_id = e.currentTarget.dataset.info.product_id
    let order_id = e.currentTarget.dataset.info.order_id
    let from = 'platform'


    wx.navigateTo({
      url: '/pages/supplier/afterSale/info/index?id=' + product_id + '&order_id=' + order_id + '&from=' + from,
    })
  },



  onReady() {

  },

  onShow() {

  },

  onReachBottom() {
    this.refundList()
  },


})