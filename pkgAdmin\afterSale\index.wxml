<view class="con">
  <view wx:for="{{list}}" wx:key="key">
    <view class="order" data-info="{{item}}" bind:tap="toOrderDetail">
      <view wx:if="{{item.auditor_type=='platform'}}" class="audit">二次申诉</view>
      <view style="display: flex;justify-content: space-between;">
        <view class="shop_title" style="font-size: 30rpx;font-weight: bold;">{{item.buyer_name}}</view>

      </view>
      <view class="order-con">
        <view class="product-list">
          <view class="per">
            <image class="goods_cover" src="{{item.product_cover.name?imageUrl+categoryCoverProcess + item.product_cover.name:''}}" mode="widthFix" />
            <view class="left">
              <view class="titleName">{{item.product_title}}</view>
              <view style="display: flex;margin-top: 10rpx;justify-content: space-between;">
                <view style="font-size: 26rpx;margin-top: 10rpx;">{{item.sku_name}}</view>
                <text>x{{item.num}}</text>
              </view>

              <view style="color: red;font-size: 26rpx;display: flex;gap: 20rpx;">
                <text>售后重量: {{item.refund_weight_fmt}}kg</text>
                <text>售后金额: ￥{{item.amount_fmt}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view style="font-size: 26rpx;color: #666666;">售后原因：{{item.reason}}</view>
      <view style="font-size: 26rpx;color: #666666;">售后时间：{{item.create_at_fmt}}</view>
      <view style="font-size: 26rpx;color: #666666;margin-top: 10rpx;">供应商名：{{item.supplier_name}}</view>
    </view>
    <!-- </view> -->
  </view>
</view>
<view style="height: 50rpx;">
</view>