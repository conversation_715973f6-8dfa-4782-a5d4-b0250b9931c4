.con {
  box-sizing: border-box;
  padding: 20rpx;
  background-color: #ececec;
  min-height: 100vh;
}

.order {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.audit {
  font-size: 24rpx;
  color: #fff;
  background-color: red;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 10rpx;
  position: absolute;
  top: 0;
  right: 0;
  border-top-right-radius: 10rpx;
  border-bottom-left-radius: 10rpx;
}

.shop_title {
  display: flex;
  justify-content: space-between;
  padding-right: 20rpx;
  gap: 20rpx;
}

.product-list {
  width: 100%;
  overflow: scroll;
}

.per {
  display: flex;
  margin-top: 10rpx;
}

.goods_cover {
  width: 160rpx;
  height: auto;
  margin: 10rpx;
  margin-left: 0;
  margin-top: 0;
  border-radius: 22rpx;
}

.left {
  flex: 1;
  padding: 10rpx 0;
  box-sizing: border-box;
}

.titleName {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-size: 26rpx;
}


.info {
  background-color: #1989fa;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 6rpx 20rpx;
}

.order-con {
  display: flex;
}

.text-content {
  border: 1rpx solid #ececec;
  padding: 10rpx;
  box-sizing: border-box;
  height: 150rpx;
  border-radius: 10rpx;
}

.cancle {
  width: 30%;
  border: 1rpx solid #eee;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}

.sure {
  width: 40%;
  border: 1rpx solid #eee;
  background-color: #38abf8;
  color: #fff;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}