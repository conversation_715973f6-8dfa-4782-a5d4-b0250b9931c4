import {
  buyer_list,
  auditBuyer
} from '../../apis/vip'

import {
  dealTimeFormat1
} from '../../utils/dict';

import Dialog from '@vant/weapp/dialog/dialog';

Page({

  data: {
    tab: [{
        title: '审核中',
        name: 'review',
      },
      {
        title: '审核通过',
        name: 'approved',
      }, {
        title: '审核不通过',
        name: 'failed',
      }
    ],
    page: 1,
    audit_status: 1,
    isEnd: true,
    list: [],
    show: false,
    buyer_info: {},
    radio: '1'
  },

  onLoad(options) {

    this.buyerList()
  },
  onChangeActive(e) {
    console.log(e)
    let status = 0
    switch (e.detail.name) {
      case 'review':
        status = 1
        break;
      case 'approved':
        status = 2
        break;
      case 'failed':
        status = 3
        break;
    }


    this.setData({
      active: e.detail.name,
      audit_status: status,
      page: 1,
      list: []
    })

    this.buyerList()
  },

  // 审核列表

  buyerList() {
    if (!this.data.isEnd) {
      return
    }
    let page = this.data.page++
    let data = {
      audit_status: this.data.audit_status,
      Page: page,
      limit: 10
    }
    buyer_list(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data.list !== null) {
          list = res.data.data.list
          list.forEach(ele => {
            ele.create_at_fmt = dealTimeFormat1(ele.created_at)
          })
        }
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          isEnd: this.data.list.length < res.data.data.count
        })
      }
    })
  },

  handleToExamine(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      show: true,
      audit_fail_reason: '',
      radio: '1',
      buyer_info: info
    })
  },
  onChange(e) {
    let id = e.detail
    this.setData({
      radio: id
    })
  },
  inputReason(e) {
    let content = e.detail.value
    this.setData({
      audit_fail_reason: content
    })
  },
  onClose() {
    this.setData({
      show: false,

    })
  },
  handleConfirm() {
    let data = {
      
    }
    this.setData({
      show: false,
    })
  },

  onShow() {},
  onReachBottom() {
    this.buyerList()
  },

})