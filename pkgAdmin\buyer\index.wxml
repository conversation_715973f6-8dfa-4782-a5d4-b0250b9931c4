<view style="background-color: #f0f2f5;min-height: 100vh; ">
  <van-tabs active="{{active}}" bind:change="onChangeActive" sticky="true">
    <van-tab wx:for="{{tab}}" wx:key="name" title="{{item.title}}" name="{{item.name}}">
      <block wx:for="{{list}}" wx:key="id">
        <view class="list">
          <view>
            <text style="font-size: 28rpx;">采购商：</text>
            <text class="buyerName">{{item.buyer_name}}</text>
          </view>

          <view class="buyerInfo">
            <view>
              <text style="font-size: 28rpx;">联系人：</text>
              <text class="buyerName">{{item.contact_user}}</text>
            </view>
            <view>
              <text style="font-size: 28rpx;">类型：</text>
              <van-tag round type="success" wx:if="{{item.buyer_type ===1}}">水果店</van-tag>
              <van-tag round type="success" wx:if="{{item.buyer_type ===2}}">商超</van-tag>
              <van-tag round type="success" wx:if="{{item.buyer_type ===3}}">其他</van-tag>
            </view>
            <view>
              <text style="font-size: 28rpx;">线下实体：</text>
              <van-tag type="primary" wx:if="{{item.entity === 1 || item.entity === 0}}">有</van-tag>
              <van-tag type="danger" wx:if="{{item.entity ===2}}">无</van-tag>
            </view>
          </view>

          <view>
            <text style="font-size: 28rpx;">申请说明：</text>
            <text class="buyerName">{{item.apply_reason}}</text>
          </view>

          <view class="btn" wx:if="{{item.audit_status == 1}}">
            <view class="time">{{item.create_at_fmt}}</view>
            <view class="info" data-info="{{item}}" bind:tap="handleToExamine">审核</view>
          </view>

        </view>
      </block>
    </van-tab>
  </van-tabs>
</view>

<van-dialog use-slot title="审核" show="{{ show }}" show-cancel-button bind:cancel="onClose" bind:confirm="handleConfirm">
  <view style="padding: 20rpx;font-size: 28rpx;">
    <view>地址：{{buyer_info.location.address}}</view>
    <view style="margin: 20rpx 0;display: flex;align-items: center;">
      <view>状态：</view>
      <van-radio-group value="{{ radio }}" bind:change="onChange">
        <view style="display: flex;align-items: center;gap: 20rpx;">
          <van-radio name="1">通过</van-radio>
          <van-radio name="2">不通过</van-radio>
        </view>
      </van-radio-group>
    </view>
    <view wx:if="{{radio=='2'}}">
      <view style="white-space: nowrap;margin-bottom: 10rpx;">原因：</view>
      <textarea placeholder="请输入" value="{{audit_fail_reason}}" auto-focus class="text-content" bindinput="inputReason" />
    </view>
  </view>
</van-dialog>