.list {
  margin: 20rpx;
  background-color: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
}

.buyerName {
  font-size: 26rpx;
  color: #949494;
}

.buyerInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btn {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.time {
  font-size: 24rpx;
  color: #727272;
}

.info {
  background-color: #1989fa;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 6rpx 20rpx;
}

.text-content {
  border: 1rpx solid #ececec;
  padding: 10rpx;
  box-sizing: border-box;
  height: 150rpx;
  border-radius: 10rpx;
}