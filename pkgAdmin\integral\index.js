import {
  integral_order,
  integral_order_cancel,
  integral_order_ship
} from '../../apis/vip'
import {
  dealTimeFormat1,
} from '../../utils/dict';
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
const app = getApp()
Page({

  data: {
    isEnd: true,
    page: 1,
    list: [],
    imageUrl: app.globalData.imageUrl,
    integral_info: {},
    actions: [
      {
        name: '发货',
        id: 2
      },
    ]
  },

  onLoad(options) {
    this.integralList()
  },

  integralList() {
    if (!this.data.isEnd) {
      return
    }
    let page = this.data.page++
    let data = {
      query_type: 'toShip',
      Page: page,
      limit: 10
    }
    integral_order(data).then(res => {
      if (res.data.code == 0) {
        let list = []
        if (res.data.data.list !== null) {
          list = res.data.data.list
          list.forEach(ele => {
            ele.discount_price_fmt = (ele.discount_price / 100).toFixed(2)
            ele.price_fmt = (ele.price / 100).toFixed(2)
            ele.create_at_fmt = dealTimeFormat1(ele.created_at)
          })
        }
        let newList = [...this.data.list, ...list]
        this.setData({
          list: newList,
          isEnd: this.data.list.length < res.data.data.count
        })
      }
    })
  },

  handleMore(e) {
    let info = e.currentTarget.dataset.info
    this.setData({
      show: true,
      integral_info: info
    })
  },

  handleSelect(e) {
    let id = e.detail.id
    this.cancleOrder(id)
  },
  cancleOrder(ids) {
    let integral_order = ids == 1?integral_order_cancel:integral_order_ship
    Dialog.confirm({
        title: '订单',
        message: '确认修改该订单状态？',
      })
      .then(() => {
        let data = {
          id: this.data.integral_info.id
        }
        integral_order(data).then(res => {
          if (res.data.code == 0) {
            Toast('操作成功')
            this.setData({
              page: 1,
              list: [],
              show: false
            })
            this.integralList()
          }
        }).catch(err => {})
      })
      .catch(() => {
        this.setData({
          show: false
        })
      });
  },

  cancle(){
    this.setData({
      show: false
    })
  },

  onReachBottom() {
    this.integralList()
  },

})