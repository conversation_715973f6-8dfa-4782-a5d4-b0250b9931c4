<view class="con">
  <view wx:for="{{list}}" wx:key="key">
    <view class="order" data-info="{{item}}" bind:tap="toOrderDetail">
      <view class="shop_title" style="font-size: 30rpx;">{{item.buyer_name}}</view>
      <view class="order-con">
        <view class="product-list">
          <view class="per">
            <image class="goods_cover" src="{{item.image_cover.name?imageUrl + item.image_cover.name:''}}" mode="widthFix" />
            <view class="left">
              <view class="titleName">{{item.product_title}}</view>
              <view style="font-size: 24rpx;margin-top: 10rpx;">积分：{{item.cost_num}}</view>
              <view style="display: flex;margin-top: 10rpx;justify-content: space-between;">
                <view style="font-size: 24rpx;display: flex;gap: 20rpx;">
                  <text>兑换价: ￥{{item.discount_price_fmt}}</text>
                  <text>原价: ￥{{item.price_fmt}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="order-time">
        <view>下单时间: {{item.create_at_fmt}}</view>
        <view class="info" data-info="{{item}}" catch:tap="handleMore">更多</view>
      </view>
    </view>
    <!-- </view> -->
  </view>
</view>
<view style="height: 50rpx;">
</view>
<van-toast id="van-toast" style="position: fixed;top: 50%;z-index: 9999;" />
<van-dialog id="van-dialog" style="position: fixed;top: 50%;z-index: 8888;" />
<van-action-sheet show="{{ show }}" actions="{{ actions }}" cancel-text="取消" bind:select="handleSelect" bind:cancel="cancle" />