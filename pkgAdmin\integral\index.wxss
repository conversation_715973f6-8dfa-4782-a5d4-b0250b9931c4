.con {
  box-sizing: border-box;
  padding: 20rpx;
  background-color: #ececec;
  height: 100vh;
}

.order {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
}

.shop_title {
  display: flex;
  justify-content: space-between;
  padding-right: 20rpx;
  gap: 20rpx;
}

.product-list {
  width: 100%;
  overflow: scroll;
}

.per {
  display: flex;
  margin-top: 10rpx;
}

.goods_cover {
  width: 150rpx;
  height: auto;
  margin: 10rpx;
  border-radius: 22rpx;
}

.left {
  width: calc(100vw - 180rpx);
  padding: 10rpx 0;
  box-sizing: border-box;
}

.titleName {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-size: 28rpx;
}

.order-time {
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  color: #a6a6a6;
}

.info {
  font-size: 26rpx;
  color: #409eff;
}


.order-con {
  display: flex;
}

.text-content {
  border: 1rpx solid #ececec;
  padding: 10rpx;
  box-sizing: border-box;
  height: 150rpx;
  border-radius: 10rpx;
}

.cancle {
  width: 30%;
  border: 1rpx solid #eee;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}

.sure {
  width: 40%;
  border: 1rpx solid #eee;
  background-color: #38abf8;
  color: #fff;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}