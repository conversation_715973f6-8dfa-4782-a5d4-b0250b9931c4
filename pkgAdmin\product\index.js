import {
  product_audit_list,
  product_list_offline,
  product_audit_offline,
  product_audit,
  tag_list
} from "../../apis/vip";
import { dealTimeFormat1 } from "../../utils/dict";
import Toast from "@vant/weapp/toast/toast";
import Dialog from "@vant/weapp/dialog/dialog";
const app = getApp();
Page({
  data: {
    isEnd: true,
    active: 1,
    page: 1,
    list: [],
    imageUrl: app.globalData.imageUrl,
    navBarHeight: app.globalData.navBarHeight,
    product_info: {},
    radio: "2",
    TypeList: [
      {
        title: "商品审核",
        name: 1,
      },
      {
        title: "下架审核",
        name: 2,
      },
    ],
    id: "",

    old_show: false,
    contrast_info: null,
    sku_list: [],
    popup: false,
    list_tag: [],
    cover_tag_id: "", // 选中的封面标签ID
    word_tag_id_list: [] // 选中的文字标签ID列表
  },

  handlePopup() {
    this.getTagList()
    this.setData({
      popup: true
    })
  },
  getTagList() {
    tag_list().then(res => {
      if (res.data.code == 0) {
        this.setData({
          list_tag: res.data.data
        })
      }

    })
  },
  onClosePopup() {
    this.setData({
      popup: false
    })
  },

  // 选择封面标签
  selectCoverTag(e) {
    const tagId = e.currentTarget.dataset.id;
    this.setData({
      cover_tag_id: tagId
    });
  },

  // 选择文字标签
  selectWordTag(e) {
    const tagId = e.currentTarget.dataset.id;
    let wordTagList = [...this.data.word_tag_id_list];

    const index = wordTagList.indexOf(tagId);
    if (index > -1) {
      // 如果已选中，则取消选择
      wordTagList.splice(index, 1);
    } else {
      // 如果未选中且未达到最大数量，则添加
      if (wordTagList.length < 3) {
        wordTagList.push(tagId);
      } else {
        wx.showToast({
          title: '最多选择3个文字标签',
          icon: 'none'
        });
        return;
      }
    }

    this.setData({
      word_tag_id_list: wordTagList
    });
  },

  onLoad(options) {
    this.productList();
  },

  // 预览查看图片
  handleSeeImg(e) {
    let img = e.currentTarget.dataset.img
    let cover = this.data.imageUrl + img
    let sources = [{
      url: cover,
      type: 'image'
    },]
    let index = 0
    wx.previewMedia({
      sources: sources, // 需要预览的资源列表
      current: index, // 当前显示的资源序号
      url: cover // 当前预览资源的url链接
    })
  },

  // tab点击事件
  handleChangTab(e) {
    this.setData({
      list: [],
      page: 1,
      isEnd: true,
      active: e.detail.name,
    });
    if (e.detail.name == 1) {
      this.productList();
    }

    if (e.detail.name == 2) {
      this.productListDown();
    }
  },

  // 查看原商品信息
  handleSeeOldInfo(e) {
    let old_info = e.currentTarget.dataset.old.origin_product;
    let new_info = e.currentTarget.dataset.old.product;
    let info = {};
    // 标题
    if (old_info.title !== new_info.title) {
      info.title = old_info.title;
    }
    // 描述
    if (old_info.desc !== new_info.desc) {
      info.desc = old_info.desc;
    }
    // 封面
    if (old_info.cover_img.name !== new_info.cover_img.name) {
      info.cover_img = old_info.cover_img;
    }

    // 轮播图
    if (new_info.display_file.length !== old_info.display_file.length) {
      info.display_file = old_info.display_file;
    } else {
      // 判断new_info.display_file和old_info.display_file两个数组是否相等用for循环
      let is_equal = true;
      for (let i = 0; i < new_info.display_file.length; i++) {
        if (new_info.display_file[i].name !== old_info.display_file[i].name) {
          is_equal = false;
        }
      }
      if (!is_equal) {
        info.display_file = old_info.display_file;
      }
    }

    // 详情图
    if (new_info.desc_img.length !== old_info.desc_img.length) {
      info.desc_img = old_info.desc_img;
    } else {
      let is_equal = true;
      for (let i = 0; i < new_info.desc_img.length; i++) {
        if (new_info.desc_img[i].name !== old_info.desc_img[i].name) {
          is_equal = false;
        }
      }
      if (!is_equal) {
        info.desc_img = old_info.desc_img;
      }
    }
    // 封面
    if (old_info.cover_img.name !== new_info.cover_img.name) {
      info.cover_img = old_info.cover_img;
    }
    // 视频
    if (old_info.video_file.name !== new_info.video_file.name) {
      info.video_file = old_info.video_file;
    }

    let is_same = true;
    // 判断新旧规格是否一致
    if (old_info.sku_list.length !== new_info.sku_list.length) {
      info.sku_list = old_info.sku_list;
      is_same = false;
    } else {
      for (let i = 0; i < old_info.sku_list.length; i++) {
        if (old_info.sku_list[i].name !== new_info.sku_list[i].name) {
          is_same = false;
        }
        if (old_info.sku_list[i].price !== new_info.sku_list[i].price) {
          old_info.sku_list[i].price_difference = ((new_info.sku_list[i].price - old_info.sku_list[i].price) / 100).toFixed(2)
          old_info.sku_list[i].color = new_info.sku_list[i].price - old_info.sku_list[i].price > 0 ? 'red' : 'green'
          is_same = false;
        }
        if (
          old_info.sku_list[i].market_wholesale_price !==
          new_info.sku_list[i].market_wholesale_price
        ) {
          is_same = false;
        }
        if (
          old_info.sku_list[i].estimate_purchase_price !==
          new_info.sku_list[i].estimate_purchase_price
        ) {
          is_same = false;
        }
        if (
          old_info.sku_list[i].rough_weight !==
          new_info.sku_list[i].rough_weight
        ) {
          is_same = false;
        }
        if (
          old_info.sku_list[i].out_weight !== new_info.sku_list[i].out_weight
        ) {
          is_same = false;
        }
        if (
          old_info.sku_list[i].net_weight !== new_info.sku_list[i].net_weight
        ) {
          is_same = false;
        }

        if (
          old_info.sku_list[i].description !== new_info.sku_list[i].description
        ) {
          is_same = false;
        }
      }
      if (!is_same) {
        info.sku_list = old_info.sku_list;
      }
    }

    this.setData({
      old_show: true,
      contrast_info: info, //旧的信息
      is_same: is_same,
    });
  },

  handeSeeimg(e) {
    // 查看图片
    let img = e.currentTarget.dataset.img;
    let src = this.data.imageUrl;
    console.log(11);
    wx.previewImage({
      urls: [src + img],
    });
  },

  onCloseOld() {
    this.setData({
      old_show: false,
    });
  },

  // 商品审核
  productList() {
    if (!this.data.isEnd) {
      return;
    }
    let page = this.data.page++;
    let data = {
      service_point_id: "647d77ef1db1e622b23c3339",
      status: 1,
      Page: page,
      limit: 10,
    };
    product_audit_list(data).then((res) => {
      if (res.data.code == 0) {
        let list = [];
        if (res.data.data.list !== null) {
          list = res.data.data.list;
          list.forEach((ele) => {
            ele.product.supply_price_fmt = (
              ele.product.supply_price / 100
            ).toFixed(2);
            ele.product.deit_created_at_fmt = dealTimeFormat1(ele.product.deit_created_at);

            // 新的商品
            if (ele.product.sku_list) {
              ele.product.sku_list.forEach((item) => {
                item.rough_weight_fmt = (item.rough_weight / 1000).toFixed(2);
                item.out_weight_fmt = (item.out_weight / 1000).toFixed(2);
                item.net_weight_fmt = (item.net_weight / 1000).toFixed(2);
                item.price_fmt = (item.price / 100).toFixed(2);
                item.market_wholesale_price_fmt = (
                  item.market_wholesale_price / 100
                ).toFixed(2);
                item.estimate_purchase_price_fmt = (
                  item.estimate_purchase_price / 100
                ).toFixed(2);

                item.price_unit = (
                  (item.price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.wholesale_unit = (
                  (item.market_wholesale_price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.purchase_unit = (
                  (item.estimate_purchase_price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.profit_fmt = (
                  ((item.price - item.estimate_purchase_price) / item.price) *
                  100
                ).toFixed(2);
              });
            }

            // 旧的商品
            if (ele.origin_product.sku_list) {
              ele.origin_product.sku_list.forEach((item) => {
                item.rough_weight_fmt = (item.rough_weight / 1000).toFixed(2);
                item.out_weight_fmt = (item.out_weight / 1000).toFixed(2);
                item.net_weight_fmt = (item.net_weight / 1000).toFixed(2);
                item.price_fmt = (item.price / 100).toFixed(2);
                item.market_wholesale_price_fmt = (
                  item.market_wholesale_price / 100
                ).toFixed(2);
                item.estimate_purchase_price_fmt = (
                  item.estimate_purchase_price / 100
                ).toFixed(2);

                item.price_unit = (
                  (item.price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.wholesale_unit = (
                  (item.market_wholesale_price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.purchase_unit = (
                  (item.estimate_purchase_price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.profit_fmt = (
                  ((item.price - item.estimate_purchase_price) / item.price) *
                  100
                ).toFixed(2);
              });
            }
          });
        }
        let newList = [...this.data.list, ...list];
        this.setData({
          list: newList,
          isEnd: this.data.list.length < res.data.data.count,
        });
      }
    });
  },

  // 新的下架审核列表
  productListDown() {
    product_list_offline().then((res) => {
      if (res.data.code == 0) {
        let list = [];
        if (res.data.data !== null) {
          list = res.data.data;
          list.forEach((ele) => {
            ele.supply_price_fmt = (ele.supply_price / 100).toFixed(2);
            ele.create_at_fmt = dealTimeFormat1(ele.created_at);
            ele.apply_at_fmt = dealTimeFormat1(ele.apply_at);

            if (ele.sku_list) {
              ele.sku_list.forEach((item) => {
                item.rough_weight_fmt = (item.rough_weight / 1000).toFixed(2);
                item.out_weight_fmt = (item.out_weight / 1000).toFixed(2);
                item.net_weight_fmt = (item.net_weight / 1000).toFixed(2);
                item.price_fmt = (item.price / 100).toFixed(2);
                item.market_wholesale_price_fmt = (
                  item.market_wholesale_price / 100
                ).toFixed(2);
                item.estimate_purchase_price_fmt = (
                  item.estimate_purchase_price / 100
                ).toFixed(2);

                item.price_unit = (
                  (item.price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.wholesale_unit = (
                  (item.market_wholesale_price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.purchase_unit = (
                  (item.estimate_purchase_price / item.rough_weight) *
                  10
                ).toFixed(2);
                item.profit_fmt = (
                  ((item.price - item.estimate_purchase_price) / item.price) *
                  100
                ).toFixed(2);
              });
            }
          });
        }
        let newList = [...this.data.list, ...list];
        this.setData({
          list: newList,
        });
      }
    });
  },

  handleToExamine(e) {
    let info = e.currentTarget.dataset.info;
    let active = this.data.active;

    let product_info = active == 1 ? info.product : info;
    let sku_list = product_info.sku_list;

    sku_list.forEach((item) => {
      item.new_price = item.price;
      item.new_price_fmt = item.price_fmt;
      item.sure_per = ((item.new_price / item.net_weight) * 10).toFixed(2);

    });

    this.setData({
      show: true,
      fail_reason: "",
      radio: "2",
      product_info: product_info,
      price_fmt: product_info.price_fmt,
      price: product_info.price,
      id: info.id,
      sku_list: sku_list,
      cover_tag_id: "", // 重置封面标签选择
      word_tag_id_list: [] // 重置文字标签选择
    });
  },

  onChange(e) {
    let id = e.detail;
    this.setData({
      radio: id,
    });
  },
  inputReason(e) {
    let content = e.detail.value;
    this.setData({
      fail_reason: content,
    });
  },
  onClose() {
    this.setData({
      show: false,
    });
  },
  handleConfirm() {
    if (this.data.active == 1) {
      this.productAudit();
    }

    if (this.data.active == 2) {
      this.offlineAudit();
    }
  },

  // 商品审核
  productAudit() {
    // 收集新的销售价
    let sku_list = this.data.sku_list;

    let sku_price_list = sku_list.map((item) => {
      return {
        sku_id_code: item.id_code,
        price: item.new_price,
      };
    });
    let data = {
      audit_id: this.data.id,
      status: parseInt(this.data.radio),
      fail_reason: this.data.fail_reason,
      sku_price_list: sku_price_list,
      cover_tag_id: this.data.cover_tag_id,
      word_tag_id_list: this.data.word_tag_id_list
    };
    product_audit(data)
      .then((res) => {
        if (res.data.code == 0) {
          this.setData({
            show: false,
            page: 1,
            list: [],
          });
          this.productList();
        }
      })
      .catch((err) => {
        wx.showToast({
          title: err.data.message,
          icon: "error",
        });
      });
  },

  // 下架审核
  offlineAudit() {
    let data = {
      product_id: this.data.product_info.id,
      audit_status: parseInt(this.data.radio),
    };
    product_audit_offline(data)
      .then((res) => {
        if (res.data.code == 0) {
          this.setData({
            show: false,
            list: [],
          });
          this.productListDown();
        }
      })
      .catch((err) => {
        wx.showToast({
          title: err.data.message,
          icon: "error",
        });
      });
  },

  toOrderDetail(e) {
    let info = e.currentTarget.dataset.info;
    let id = info.id;
    let saleValue = this.data.active == 1 ? "2" : "1";
    wx.navigateTo({
      url:
        "/pages/supplier/product/info/index?id=" +
        id +
        `&from=list` +
        `&saleValue=` +
        saleValue,
    });
  },

  onReachBottom() {
    this.productList();
  },

  // 处理新销售价输入
  onNewPriceChange(e) {
    const { index } = e.currentTarget.dataset;
    const newPrice = e.detail;
    let sku_list = this.data.sku_list;
    sku_list[index].new_price_fmt = newPrice;
    sku_list[index].new_price = parseInt(newPrice * 100);
    sku_list[index].sure_per = ((sku_list[index].new_price / sku_list[index].net_weight) * 10).toFixed(2);
    sku_list[index].profit_fmt = (((sku_list[index].new_price - sku_list[index].estimate_purchase_price) / sku_list[index].new_price) * 100).toFixed(2);
    this.setData({
      sku_list: sku_list,
    });
  },
});
