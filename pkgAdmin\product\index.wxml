<view style="background-color: #f6f6f6;">
  <van-tabs active="{{active}}" bind:click="handleChangTab" sticky="{{true}}" offset-top="{{navBarHeight}}">
    <van-tab name="{{item.name}}" wx:for="{{TypeList}}" wx:key="key" title="{{item.title}}"></van-tab>
  </van-tabs>
  <view class="con" wx:if="{{active == 1}}">
    <view wx:for="{{list}}" wx:key="key">

      <view class="order" data-info="{{item}}" bind:tap="toOrderDetail">
        <view>{{item.product.supplier_simple_name}}</view>
        <view class="order-con">
          <view class="product-list">
            <view class="per">
              <image class="goods_cover" src="{{item.product.cover_img.name?imageUrl + item.product.cover_img.name:''}}" mode="widthFix" />
              <view class="left">
                <view class="titleName">{{item.product.title}}</view>
                <view wx:if="{{item.is_new}}" class="tip-new" style="color: green;border: 1px solid green;">新增商品</view>
                <view wx:if="{{!item.is_new}}" class="tip-new" style="color: #4d4c4c;">编辑商品</view>
                <view class="desc">描述: {{item.product.desc}}</view>
              </view>
            </view>
          </view>
        </view>
        <!-- 规格 -->
        <view>
          <view wx:for="{{item.product.sku_list}}" wx:key="index" class="list-item">
            <view class="boli" wx:if="{{item.stock == 0}}"></view>
            <view style="display: flex;gap: 10rpx;">
              <view style="width: 100rpx;">
                <image src="{{imageUrl + item.cover}}" data-img="{{item.cover}}" catch:tap="handleSeeImg" mode="widthFix" style="width: 100rpx;height: auto;border-radius: 10rpx;" />
              </view>

              <view style="flex: 1;">
                <view style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 10rpx;">
                  <view style="font-size: 26rpx;font-weight: bold;">{{item.name}}</view>
                  <view class="stock-num" wx:if="{{item.stock == 0}}">缺货</view>
                </view>
                <view style="font-size: 24rpx;">
                  <text style="white-space: nowrap;">描述：</text>
                  <text>{{item.description}}</text>
                </view>
              </view>
            </view>
            <view class="list-sku-item">
              <view class="list-weight-row">
                <view class="list-weight-item" style="width: 210rpx;">
                  <view class="list-weight-label">毛重:</view>
                  <view class="list-weight-value">{{item.rough_weight_fmt}}kg</view>
                </view>
                <view class="list-weight-item">
                  <view class="list-weight-label">净重:</view>
                  <view class="list-weight-value">{{item.net_weight_fmt}}kg</view>
                </view>
              </view>
              <view class="list-price-row">
                <view class="list-price-item">
                  <view class="list-price-label" style="font-weight: bold;">采购价:</view>
                  <view class="list-price-value">{{item.estimate_purchase_price_fmt}}</view>
                </view>
                <view class="list-price-item">
                  <view class="list-price-label" style="font-weight: bold;">市场价:</view>
                  <view class="list-price-value">{{item.market_wholesale_price_fmt}}</view>
                </view>

                <view class="list-price-item" style="font-weight: bold;">
                  <view class="list-price-label">销售价:</view>
                  <view class="list-price-value">{{item.price_fmt}}</view>
                </view>
              </view>

            </view>
          </view>
        </view>
        <view class="reason" wx:if="{{active == 2}}">下架原因： {{item.product.reason}}</view>
        <view class="order-time">
          <view wx:if="{{active == 1}}">创建时间: {{item.product.deit_created_at_fmt}}</view>
          <view wx:if="{{active == 2}}">申请时间: {{item.product.apply_at_fmt}}</view>
        </view>
        <view class="order-audit">
          <view class="seeOld" wx:if="{{!item.is_new}}" catch:tap="handleSeeOldInfo" data-old="{{item}}">
            查看原信息
          </view>
          <view class="info" data-info="{{item}}" catch:tap="handleToExamine">审核</view>
        </view>
      </view>
    </view>
  </view>
  <view class="con" wx:if="{{active ==2}}">
    <view wx:for="{{list}}" wx:key="key">
      <view class="order" data-info="{{item}}" bind:tap="toOrderDetail">
        <view class="shop_title" style="font-size: 30rpx;">
          <view>{{item.supplier_simple_name}}</view>
          <view style="color: #1989fa;font-size: 24rpx;">
            {{item.is_check_weight?'称重销售':'按件销售'}}
          </view>
        </view>
        <view class="order-con">
          <view class="product-list">
            <view class="per">
              <image class="goods_cover" src="{{item.cover_img.name?imageUrl + item.cover_img.name:''}}" mode="widthFix" />
              <view class="left">
                <view class="titleName">{{item.title}}</view>
                <view class="desc">描述: {{item.desc}}</view>
              </view>
            </view>
          </view>
        </view>
        <!-- 规格 -->
        <view>
          <view wx:for="{{item.sku_list}}" wx:key="index" class="list-item">
            <view class="stock-num" wx:if="{{item.stock == 0}}">缺货</view>
            <view style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 10rpx;">
              <view style="font-size: 26rpx;font-weight: bold;">{{item.name}}</view>
            </view>
            <view class="list-sku-item">
              <view class="list-price-row">
                <view class="list-price-item">
                  <view class="list-price-label">销售价:</view>
                  <view class="list-price-value">{{item.price_fmt}}</view>
                </view>
                <view class="list-price-item">
                  <view class="list-price-label">市场价:</view>
                  <view class="list-price-value">{{item.market_wholesale_price_fmt}}</view>
                </view>
                <view class="list-price-item">
                  <view class="list-price-label">采购价:</view>
                  <view class="list-price-value">{{item.estimate_purchase_price_fmt}}</view>
                </view>
              </view>
              <view class="list-weight-row">
                <view class="list-weight-item" style="width: 210rpx;">
                  <view class="list-weight-label">毛重:</view>
                  <view class="list-weight-value">{{item.rough_weight_fmt}}kg</view>
                </view>

                <view class="list-weight-item">
                  <view class="list-weight-label">净重:</view>
                  <view class="list-weight-value">{{item.net_weight_fmt}}kg</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="reason" wx:if="{{active == 2}}">下架原因： {{item.reason}}</view>
        <view class="order-time">
          <view wx:if="{{active == 1}}">创建时间: {{item.create_at_fmt}}</view>
          <view wx:if="{{active == 2}}">申请时间: {{item.apply_at_fmt}}</view>
          <view class="info" data-info="{{item}}" catch:tap="handleToExamine">审核</view>
        </view>
      </view>
    </view>
  </view>
</view>
<view style="height: 50rpx;"></view>
<van-toast id="van-toast" style="position: fixed;top: 50%;z-index: 9999;" />
<van-dialog id="van-dialog" style="position: fixed;top: 50%;z-index: 8888;" />
<van-dialog use-slot title="审核" show="{{ show }}" show-cancel-button bind:cancel="onClose" bind:confirm="handleConfirm">
  <view style="padding: 20rpx 10rpx;">
    <view>
      <view wx:for="{{sku_list}}" wx:key="index" class="list-item">
        <view class="stock-num" wx:if="{{item.stock == 0}}"> 缺货 </view>
        <view class="boli" wx:if="{{item.stock == 0}}"></view>
        <view class="skuname">
          <view style="display: flex;gap: 10rpx;">
            <view style="font-weight: bold;">{{item.name}}</view>
          </view>
          <view style="color: orange;font-size: 24rpx;">毛利率：{{item.profit_fmt}}%</view>
        </view>
        <view class="weight">
          <view>毛重: {{item.rough_weight_fmt}}kg</view>
          <view>净重: {{item.net_weight_fmt}}kg</view>
        </view>
        <view class="sku-item">
          <view class="horizontal">
            <view>市场: {{item.market_wholesale_price_fmt}}（{{item.wholesale_unit}}/kg）</view>
            <view>采购: {{item.estimate_purchase_price_fmt}}（{{item.purchase_unit}}/kg）</view>
          </view>
          <view style="display: flex;flex-direction: column;gap: 10rpx;">
            <view style="font-weight: bold;flex:1">
              销售: {{item.price_fmt}}（{{item.price_unit}}/kg）
            </view>
            <view style="display: flex; align-items: center; gap:4rpx;flex:1;color: red;">
              <text style="font-size: 24rpx;">定价:</text>
              <van-field class="edit-input" value="{{item.new_price_fmt}}" placeholder="请输入新销售价" type="digit" size="mini" bind:change="onNewPriceChange" data-index="{{index}}" />
              <text>({{item.sure_per}})/kg</text>
            </view>
          </view>

        </view>
      </view>
    </view>
    <view style="margin: 20rpx 0;display: flex;align-items: center;">
      <view>状态：</view>
      <van-radio-group value="{{ radio }}" bind:change="onChange">
        <view style="display: flex;align-items: center;gap: 20rpx;">
          <van-radio name="2">通过</van-radio>
          <van-radio name="3">不通过</van-radio>
        </view>
      </van-radio-group>
    </view>

    <view wx:if="{{active == 1 && radio=='2'}}">
      <view bind:tap="handlePopup" style="color: #1989fa; margin: 10rpx 0; text-decoration: underline;">选择标签</view>

      <!-- 显示已选择的标签 -->
      <view wx:if="{{cover_tag_id || word_tag_id_list.length > 0}}" style="margin: 10rpx 0; padding: 10rpx; background-color: #f5f5f5; border-radius: 8rpx;">
        <text style="font-size: 24rpx; color: #666;">已选择：</text>

        <!-- 显示选中的封面标签 -->
        <view wx:if="{{cover_tag_id}}" wx:for="{{list_tag}}" wx:key="id">
          <text wx:if="{{item.id == cover_tag_id}}" style="font-size: 24rpx; color: #1989fa;">封面标签「{{item.title}}」</text>
        </view>

        <!-- 显示选中的文字标签 -->
        <view wx:if="{{word_tag_id_list.length > 0}}" style="margin-top: 5rpx;">
          <text style="font-size: 24rpx; color: #1989fa;">文字标签：</text>
          <text wx:for="{{list_tag}}" wx:key="id" wx:if="{{word_tag_id_list.indexOf(item.id) > -1}}" style="font-size: 24rpx; color: #1989fa;">「{{item.title}}」</text>
        </view>
      </view>
    </view>
    <view wx:if="{{active == 1 && radio=='3'}}">
      <view style="white-space: nowrap;margin-bottom: 10rpx;">理由：</view>
      <textarea placeholder="请输入" adjust-position value="{{fail_reason}}" auto-focus class="text-content" bindinput="inputReason" />
    </view>
  </view>
</van-dialog>
<van-dialog use-slot title="变化内容" show="{{ old_show }}" bind:confirm="onCloseOld">
  <view class="oldProductInfo">
    <view wx:if="{{contrast_info.title}}">标题：{{contrast_info.title}}</view>
    <view wx:if="{{contrast_info.desc}}">描述：{{contrast_info.desc}}</view>
    <view wx:if="{{!is_same}}">
      <view wx:for="{{contrast_info.sku_list}}" wx:key="index" class="list-item">
        <view class="skuname">
          <view style="display: flex;gap: 10rpx;">
            <image src="{{imageUrl+item.cover}}" mode="widthFix" style="width: 100rpx;height: auto;border-radius: 10rpx;" />
            <view>{{item.name}}</view>
          </view>

          <view style="color: orange;font-size: 24rpx;">毛利率：{{item.profit_fmt}}%</view>
        </view>
        <view class="sku-item">
          <view style="display: flex;gap: 30rpx;">
            <view>净重: {{item.net_weight_fmt}}kg</view>
            <view>毛重: {{item.rough_weight_fmt}}kg</view>
          </view>

          <view class="text">
            <view>市场价: {{item.market_wholesale_price_fmt}}</view>
            <view>采购价: {{item.estimate_purchase_price_fmt}}</view>
            <view>销售价: {{item.price_fmt}}
              <text style="color: {{item.color}};" wx:if="{{item.price_difference}}">({{item.color=="red"?'+':''}}{{item.price_difference}})</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view wx:if="{{contrast_info.cover_img}}" style="display: flex;align-items: flex-start;gap: 6rpx;">
      <view>原封面：</view>
      <image class="info_goods_cover" catch:tap="handeSeeimg" data-img="{{contrast_info.cover_img.name}}" src="{{contrast_info.cover_img.name?imageUrl + contrast_info.cover_img.name:''}}" mode="widthFix" />
    </view>
    <view wx:if="{{contrast_info.display_file}}" style="display: flex;align-items: flex-start;gap: 6rpx;">
      轮播图：
      <view wx:for="{{contrast_info.display_file}}" wx:key="index">
        <image catch:tap="handeSeeimg" data-img="{{item.name}}" class="info_goods_cover" src="{{item.name?imageUrl + item.name:''}}" mode="widthFix" />
      </view>
    </view>
    <view wx:if="{{contrast_info.desc_img}}" style="display: flex;align-items: flex-start;">
      <view style="white-space: nowrap;">详情图：</view>
      <view style="display: flex;align-items: flex-start;gap: 6rpx;flex-wrap: wrap;">
        <image wx:for="{{contrast_info.desc_img}}" wx:key="index" class="info_goods_cover" catch:tap="handeSeeimg" data-img="{{item.name}}" src="{{item.name?imageUrl + item.name:''}}" mode="widthFix" />
      </view>
    </view>
    <view wx:if="{{contrast_info.video_file}}">
      视频：
      <video enable-play-gesture="{{true}}" object-fit="contain" show-mute-btn="{{true}}" show-fullscreen-btn="{{true}}" show-center-play-btn="{{true}}" controls="{{true}}" style="width:100%;height:150px;" bindplay="videoPlay" bindpause="videoPause" bindended="videoEnd" src="{{contrast_info.video_file.name?imageUrl+contrast_info.video_file.name:''}}"></video>
    </view>
  </view>
</van-dialog>

<van-popup
  show="{{ popup }}"
  round
  closeable
  z-index="100000"
  position="bottom"
  custom-style="height: 50%"
  close-on-click-overlay="{{false}}"
  bind:close="onClosePopup"
>
<view style="height: 60rpx;"></view>
<van-divider
  contentPosition="center"
  customStyle="color: #1989fa; border-color: #1989fa; font-size: 24rpx;"
>
封面标签
</van-divider>

<view class="cover-tags-container">
  <view
    wx:for="{{list_tag}}"
    wx:key="id"
    wx:if="{{item.tag_type == 1}}"
    class="cover-tag-item {{cover_tag_id == item.id ? 'selected' : ''}}"
    data-id="{{item.id}}"
    bind:tap="selectCoverTag"
  >
    <image
      src="{{item.img.name ? imageUrl + item.img.name : ''}}"
      class="cover-tag-image"
      mode="aspectFit"
    />
    <text class="cover-tag-title">{{item.title}}</text>
  </view>
</view>

<van-divider
  contentPosition="center"
  customStyle="color: #1989fa; border-color: #1989fa; font-size: 24rpx;"
>
文字标签
</van-divider>

<view class="word-tags-container">
  <view
    wx:for="{{list_tag}}"
    wx:key="id"
    wx:if="{{item.tag_type == 2}}"
    class="word-tag-item {{word_tag_id_list.indexOf(item.id) > -1 ? 'selected' : ''}}"
    style="background-color: {{item.color || '#f0f0f0'}};"
    data-id="{{item.id}}"
    bind:tap="selectWordTag"
  >
    <text class="word-tag-title">{{item.title}}</text>
  </view>
</view>

<view class="tag-tips">
  <text style="color: #666; font-size: 24rpx;">封面标签只能选择1个，文字标签最多选择3个</text>
</view>

<view class="tag-confirm-btn" bind:tap="confirmTagSelection">
  确认选择
</view>

</van-popup>

