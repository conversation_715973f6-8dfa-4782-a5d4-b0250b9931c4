.con {
  box-sizing: border-box;
  padding: 20rpx;
}

.order {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  font-size: 26rpx;
  margin-top: 20rpx;
}

.list-item {
  border-radius: 10rpx;
  border: 1px solid #d1d0d0;
  padding: 10rpx;
  margin-bottom: 10rpx;
  position: relative;
}

.boli {
  width: 100%;
  height: 100%;
  background-color: #eeeeee90;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.stock-num {
  font-size: 24rpx;
  background-color: red;
  padding: 4rpx 10rpx;
  color: #fff;
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  border-bottom-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  z-index: 100;
}

.shop_title {
  display: flex;
  justify-content: space-between;
  padding-right: 20rpx;
  gap: 20rpx;
}

.tip-new {
  font-size: 24rpx;
  padding: 6rpx;
  width: 100rpx;
  border: 1px solid #adacac;
  border-radius: 10rpx;
  color: #fff;
}

.oldProductInfo {
  padding: 20rpx;
  font-size: 28rpx;
  min-height: 200rpx;
  max-height: 800rpx;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.edit-input {
  border-bottom: 1rpx solid #9b9b9b;
  width: 180rpx;
}

.product-list {
  width: 100%;
  overflow: scroll;
}

.per {
  display: flex;
  margin-top: 10rpx;
  align-items: center;
}

.goods_cover {
  width: 150rpx;
  height: auto;
  margin: 10rpx;
  border-radius: 22rpx;
}

.info_goods_cover {
  width: 130rpx;
  height: auto;
  margin: 10rpx;
  border-radius: 22rpx;
}

.left {
  width: calc(100vw - 180rpx);
  padding: 10rpx 0;
  box-sizing: border-box;
  height: 150rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.desc {
  font-size: 24rpx;
  margin-top: 10rpx;
  /* 超出两行显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}


.van-cell {
  padding: 0 10rpx !important;
}

.sku-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  font-size: 24rpx;
}

.text {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 10rpx;
}

.horizontal {
  display: flex;
  flex-direction: column;
}

.weight {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  margin-bottom: 20rpx;
  color: #5a5959;
}

/* 列表价格展示样式 */
.list-sku-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  font-size: 24rpx;
  margin-top: 8rpx;
}

.list-price-row {
  display: flex;
  justify-content: space-between;
  gap: 10rpx;
}

.list-price-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  flex: 1;
}

.list-price-label {
  font-size: 22rpx;
  white-space: nowrap;
}


.list-weight-row {
  display: flex;
  gap: 10rpx;
}

.list-weight-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  /* flex: 1; */
}

.list-weight-label {
  color: #666;
  font-size: 22rpx;
  white-space: nowrap;
}

.list-weight-value {
  color: #333;
}

.titleName {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  font-size: 28rpx;
  font-weight: bold;
}

.order-time {
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
  color: #a6a6a6;
  padding-right: 20rpx;
}

.order-audit {
  font-size: 24rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
  margin-top: 10rpx;
  padding-right: 20rpx;
}

.seeOld {
  border: 1rpx solid orange;
  padding: 6rpx;
  color: orange;
  border-radius: 10rpx;
}

.info {
  background-color: #1989fa;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 6rpx 20rpx;
}

.order-con {
  display: flex;
}

.text-content {
  border: 1rpx solid #ececec;
  padding: 10rpx;
  box-sizing: border-box;
  height: 150rpx;
  border-radius: 10rpx;
}

.cancle {
  width: 30%;
  border: 1rpx solid #eee;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}

.sure {
  width: 40%;
  border: 1rpx solid #eee;
  background-color: #38abf8;
  color: #fff;
  padding: 10rpx 0;
  text-align: center;
  border-radius: 10rpx;
}


.reason {
  font-size: 24rpx;
  margin-top: 10rpx;
  color: #ec730f;
}

.skuname {
  display: flex;
  justify-content: space-between;
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

/* 标签样式 */
.cover-tags-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

.cover-tag-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  background-color: #fff;
  transition: all 0.3s ease;
}

.cover-tag-item.selected {
  border-color: #1989fa;
  background-color: #f0f8ff;
}

.cover-tag-image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

.cover-tag-title {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.word-tags-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
  padding: 20rpx;
}

.word-tag-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx 10rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  min-height: 60rpx;
  position: relative;
}

.word-tag-item.selected {
  border-color: #1989fa;
  box-shadow: 0 0 10rpx rgba(25, 137, 250, 0.3);
  background-color: #e6f3ff !important;
}

.word-tag-item.selected::after {
  content: '✓';
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  width: 30rpx;
  height: 30rpx;
  background-color: #1989fa;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.word-tag-title {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.tag-tips {
  padding: 20rpx;
  text-align: center;
}

.tag-confirm-btn {
  margin: 20rpx;
  padding: 20rpx;
  background-color: #1989fa;
  color: white;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
}

.tag-confirm-btn:active {
  background-color: #1976d2;
}