{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": true, "useStaticServer": true, "checkInvalidKey": true, "disableUseStrict": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "wx3832965418997e31"}