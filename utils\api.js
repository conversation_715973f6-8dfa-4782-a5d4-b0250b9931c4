import { ReqClient } from './request'

//登录
export const login = (data) => {
  return ReqClient('/api/user/login/pwd','POST', {
    ...data
  })
}

//发送验证码
export const send_code = (data) => {
  return ReqClient('/api/captcha/send','POST', {
    ...data
  })
}

// 登录code
export const login_code = (data) => {
  return ReqClient('/api/user/login','POST', {
    ...data
  })
}
// 规格id_code
export const sys_code = () => {
  return ReqClient('/api/sys/uuid','POST', {
    
  })
}

//上传图片获取签名
export const upload_sign = (data) => {
  return ReqClient(`/api/sys/oss/upload/sign/${data}`,'GET', )
}

//供应商认证基础资料
export const supplier_base_data = (data) => {
  return ReqClient('/api/supplier/','POST', {
    ...data
  })
}
//供应商认证主体资料
export const supplier_subject_data = (data) => {
  return ReqClient('/api/supplier/entity/','POST', {
    ...data
  })
}

//供应商认证银行账户信息
export const supplier_bank_data = (data) => {
  return ReqClient('/api/supplier/bank/account/','POST', {
    ...data
  })
}

export const supplier_check = (supplierid) => {
  return ReqClient(`/api/supplier/check/${supplierid}/`,'GET', )
}

export const supplier_search = (userId) => {
  return ReqClient(`/api/supplier/user/${userId}`,'GET',{
  } )
}

// 更新上一条接口地址
export const get_supplier_search = (data) => {
  return ReqClient(`/api/supplier/get/user`, 'POST', {
    ...data
  })
}

export const get_supplier  = (data) => {
  return ReqClient(`/api/supplier/get/user`,'POST',{
    ...data
  } )
}

//分类列表——一级
export const category_list_main = (data) => {
  return ReqClient('/api/category/list/first/'+ data,'GET', {
    ...data
  })
}

// 下一级级分类
export const class_list_next = (parent_id,data) => {
  return ReqClient(`/api/category/next/${parent_id}/`+ data,'GET', {
    ...data
  })
}
//新增商品
export const add_product = (data) => {
  return ReqClient('/api/product/create','POST', {
    ...data
  })
}

//修改商品
export const updata_product = (data) => {
  return ReqClient('/api/product/update','POST', {
    ...data
  })
}


//审核列表
export const audit_product = (data) => {
  return ReqClient('/api/product/list/supplier/audit','POST', {
    ...data
  })
}


//商品上架下架
export const sale_product = (data) => {
  return ReqClient('/api/product/sale/update','POST', {
    ...data
  })
}

//商品删除
export const delete_product = (data) => {
  return ReqClient('/api/product/delete','POST', {
    ...data
  })
}

//检查是否存在本产品待审核信息
export const check_audit = (data) => {
  return ReqClient('/api/product/update/check/audit','POST', {
    ...data
  })
}






//库存修改
export const stock_updata_product = (data) => {
  return ReqClient('/api/product/stock/update','POST', {
    ...data
  })
}

//价格修改
export const price_updata_product = (data) => {
  return ReqClient('/api/product/price/update','POST', {
    ...data
  })
}

export const search_category = (data) => {
  return ReqClient('/api/category/detail','POST', {
    ...data
  })
}

//  复制商品
export const duplicateProduct = (data) => {
  return ReqClient('/api/product/duplicate','POST', {
    ...data
  })
}



//商品详情
export const productDetail = (data) => {
  return ReqClient('/api/product/get','POST', {
    ...data
  })
}

//不在审核中的商品详情
export const productPureInfo = (data) => {
  return ReqClient('/api/product/get/pure','POST', {
    ...data
  })
}

//在审核中的商品详情
export const productAuditInfo = (data) => {
  return ReqClient('/api/product/audit/get','POST', {
    ...data
  })
}

//商品关联
export const checkLink = (data) => {
  return ReqClient('/api/product/link/check','POST', {
    ...data
  })
}

//商品单位
export const unit_product = () => {
  return ReqClient('/api/product/unit/list','GET',)
}

//查询-审核详情
export const audit_detail = (data) => {
  return ReqClient('/api/product/list/supplier/audit/get','POST', {
    ...data
  })
}




//水果等级
export const fruit_class = (data) => {
  return ReqClient('/api/product/fruit/class/list/category/','POST', {
    ...data
  })
}


//供应商商品列表
export const supplier_product = (data) => {
  return ReqClient('/api/product/list/supplier','POST', {
    ...data
  })
}
//认证(供应商)
export const suppliers = (data) => {
  return ReqClient('/api/supplier/','POST', {
    ...data
  })
}
//认证更新
export const suppliers_updata = (data) => {
  return ReqClient('/api/supplier/apply/update','POST', {
    ...data
  })
}

//认证信息查询
export const authentication = (data) => {
  return ReqClient('/api/authentication/get','POST', {
    ...data
  })
}

//关联商品信息
export const productLinkInfo = (data) => {
  return ReqClient('/api/product/link/info','POST', {
    ...data
  })
}
