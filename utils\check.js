import dayjs from "../libs/dayjs"


export function checkPrivacy(){
  if (wx.getPrivacySetting) {
    wx.getPrivacySetting({
      success: res => {
        if (res.needAuthorization) {
          wx.redirectTo({
            url: '/pages/intro/index',
          })
        } 
      },
      fail: () => {},
      complete: () => {},
    })
  }
}

 // 时间格式化
 export  function  dealTime(at) {
  return dayjs(at).format('YYYY-MM-DD HH:mm:ss')
}

  // 时间格式化
  export  function  dealTimes(at) {
    return dayjs(at).format('YYYY/MM/DD')
  }

   // 时间格式化
   export  function  dealTimeDay(at) {
    return dayjs(at).format('MM/DD HH:mm')
  }

  export  function isNotEmpty(value) {
    const types = typeof value;
    if (types === 'string' && value !== '') return true;
    if (types === 'object' && value !== null && JSON.stringify(value) !== "{}" && JSON.stringify(value) !== "[]")
      return true;
    if (types === 'number') {
      switch (value.toString()) {
        case "NaN":
          return false;
        default:
          return true;
      }
    };
    if (types === 'undefined') return false;
    if (types === 'boolean') return Boolean(value);
    if (types === 'function') return true;
    return false;
  }


