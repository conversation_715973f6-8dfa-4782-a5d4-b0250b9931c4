import dayjs from "../libs/dayjs";

export const tokenKey = "x_access_token";
export const refreshTokenKey = "x_refresh_token";
export const expiresTokenTimeKey = "x_expires_token_time";
export const userIDKey = "x_user_id";
export const userKey = "x_user";
export const roleKey = "x_role";
export const authKey = "x_auth";
export const loginListKey = "x_login_list";
export const rememberAccountListKey = "x_remember_account_list";

//  供应商
export const supplierIDKey = "supplierid";
export const supplierKey = "x_supplier";

// 中心仓
export const servicePointIDKey = "x_service_point_id";

//  服务仓
export const stationIDKey = "x_station_id";
export const stationInfoKey = "x_station_info";

export const categoryCoverProcess = "categoryCover/"; // 分类商品封面
export const displayProcess = "display/"; // 商品详情封面

//  配送方式
export const deliverTypeDoor = 1; // 送货到店
export const deliverTypeSelfGet = 2; // 自提
export const deliverTypeLogistics = 3; // 物流
export const deliverTypeInstant = 4;

export function dealFenToYuan(num) {
  var n = num;
  n = n / 100;
  let res = n.toString().match(new RegExp(/^\d+(?:\.\d{0,2})?/));
  return res;
}

// 时间格式化
export function dealTimess(at) {
  return dayjs(at).format("YYYY年M月DD日");
}

export function dealTimeFormat1(at) {
  return dayjs(at).format("YYYY-MM-DD HH:mm:ss");
}

export function dealTimeFormatDay(at) {
  return dayjs(at).format("MM-DD HH:mm");
}

export function dealTimeFormat3(at) {
  return dayjs(at).format("HH:mm:ss");
}

export function dealTimeFormat2(at) {
  return dayjs(at).format("YYYY-MM-DD");
}

export function dealTimeFormatMMDD(at) {
  return dayjs(at).format("MM-DD");
}

export function orderStatus(status) {
  switch (status) {
    case 1:
      return "已关闭";
    case 2:
      return "已取消";
    case 3:
      return "待备货";
    case 4:
      return "已备货待品控";
    case 5:
      return "已品控待分拣";
    case 6:
      return "已品控待发货";
    case 7:
      return "已发货运输中";
    case 8:
      return "待收货";
    case 9:
      return "已完成";
  }
}
