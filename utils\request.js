import {
  tokenKey,
  user<PERSON><PERSON><PERSON>
} from './dict';

const app = getApp()
const API_HOST = app.globalData.url
const util = require('./util');
export const ReqClient = (url, method, data) => {
  return new Promise((resolve, reject) => {
    //  wx.showLoading();
    if (method === 'GET') {
      var header = {
        'content-type': "application/x-www-form-urlencoded",
        'X-Env': app.globalData.env,
        'Authorization': wx.getStorageSync(tokenKey),
        "X-Request-Id": wx.getStorageSync(userIDKey) + "@" + util.uuid2(20),
      }
    } else if (method === 'POST') {
      var header = {
        'content-type': 'application/json',
        'X-Env': app.globalData.env,
        'Authorization': wx.getStorageSync(tokenKey),
        "X-Request-Id": wx.getStorageSync(userIDKey) + "@" + util.uuid2(20),
      }
    }
    wx.request({
      url: API_HOST + url,
      data,
      method,
      header: header,
      //  timeout: 6000,
      success: (res) => {
        //  wx.hideLoading();
        if (res.statusCode === 500) {
          wx.showModal({
            title: '提示',
            content: '网络服务异常！',
            showCancel: false
          })
          reject(res);
        } 
        
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res);
          } else {
            if (res.data.code === 2001) {
              wx.clearStorageSync()
              wx.showToast({
                title: '重新登录',
                icon:"error"
              })
            }
            //业务处理
            reject(res);
          }
        } else {
          wx.showModal({
            title: '错误信息',
            content: '操作失败！如需帮助请联系技术人员',
            showCancel: false
          })
        }
      },
      fail: (err) => {
        //  wx.hideLoading();
        wx.showModal({
          title: '错误信息',
          content: '网络不可用，请检查你的网络状态或稍后再试！',
          showCancel: false
        })
        reject(err);
      }
    })
  })
}