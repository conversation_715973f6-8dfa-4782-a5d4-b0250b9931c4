var division = function (num) {
  var nums = num / 1000
  return nums
}

var filtersData = function (value) {
  var time = getDate(value);
  var year = time.getFullYear();
  var month = time.getMonth() + 1;
  var date = time.getDate();
  var hour = time.getHours();
  var minute = time.getMinutes();
  var second = time.getSeconds();
  month = month < 10 ? "0" + month : month;
  date = date < 10 ? "0" + date : date;
  hour = hour < 10 ? "0" + hour : hour;
  minute = minute < 10 ? "0" + minute : minute;
  second = second < 10 ? "0" + second : second;
  // console.log(second,99,year + "-" + month + "-" + date + " " + hour+ ":" + minute+ ":" + second)
  return second, 99, year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second

}

var filtersDatas = function (value) {
  var time = getDate(value);
  var year = time.getFullYear();
  var month = time.getMonth() + 1;
  var date = time.getDate();
  var hour = time.getHours();
  var minute = time.getMinutes();
  var second = time.getSeconds();
  month = month < 10 ? "0" + month : month;
  date = date < 10 ? "0" + date : date;
  hour = hour < 10 ? "0" + hour : hour;
  minute = minute < 10 ? "0" + minute : minute;
  second = second < 10 ? "0" + second : second;
  // console.log(second,99,year + "-" + month + "-" + date + " " + hour+ ":" + minute+ ":" + second)
  return second, 99, year + "-" + month + "-" + date

}

//保留两位小数
var toFixedTwo = function (nums) {
  num = nums.toFixed(2);
  return num
}
// 保留一位小数
var toFixedOne = function (nums) {
  num = nums.toFixed(1);
  return num
}

var audit = function (status) {
  switch (status) {
    case 1:
      return '审核中';
    case 2:
      return '已通过';
    case 3:
      return '不通过';

  }
}

//  订单状态

var orderStatus = function (status) {
  // 待付款 支付失败  支付成功  交易成功-发生退款  支付已关闭

  // 待付款  支付已关闭  
  //待发货  待收货   待评价  已完成
  switch (status) {
    case 1:
      return '已关闭';
    case 2:
      return '已取消';
    case 3:
      return '待备货';
    case 4:
      return '已备货待品控';
    case 5:
      return '已品控待分拣';
    case 6:
      return '已品控待发货';
    case 7:
      return '已发货运输中';
    case 8:
      return '待收货';
    case 9:
      return '已完成';
  }

}

// 截取小数点前的值
var getBeforPoint = function (amount) {
  var amounts = amount.toString();
  var num = parseInt(amounts.substring(0, amounts.indexOf('.')));
  return num
}
// 截取小数点后的值
var getAfterPoint = function (amount) {
  var amounts = amount.toString();
  if (amounts.indexOf('.') <= -1) {
    return '.' + 00;
  }
  var num = parseInt(amounts.substring(amounts.indexOf('.') + 1, amounts.length));
  return '.' + num
}

// var timeDifference 

module.exports = {
  division: division,
  filtersData: filtersData,
  filtersDatas: filtersDatas,
  toFixedTwo: toFixedTwo,
  toFixedOne: toFixedOne,
  audit:audit,
  orderStatus:orderStatus,
  getBeforPoint: getBeforPoint,
  getAfterPoint: getAfterPoint,
}