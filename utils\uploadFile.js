import {
  upload_sign,
} from '../utils/api';
// data.host, tempFilePaths, newPath, data.policy, data.access_key_id, data.signature
const uploadFile = function(host,tempFilePaths,newPath,policy,access_key_id,signature) {
  return new Promise((resolve, reject) => {
  wx.uploadFile({
    url: host, //开发者服务器 url
    filePath: tempFilePaths, //要上传文件资源的路径
    name: 'file', //必须填file
    formData: {
      'key': newPath,
      'policy': policy,
      'OSSAccessKeyId': access_key_id,
      'signature': signature,
      'success_action_status': '200',
    },
    success: function(res) {
      resolve(res)
      if (res.statusCode != 200) {
        // fail(new Error('上传错误:' + JSON.stringify(res)))
        return;
      }
    },
    
    fail: function(err) {
      reject(err)
    },
  })
})
}

module.exports = uploadFile;