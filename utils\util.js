const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

//从路径截取图片名称
const siding = function(url) {
  let urls = url.split('?')[0]
  let file = urls.split('/')
  file =  file[file.length - 1]
  return file
}

// 截取图片后缀名
const substrImgType = function(name) {
  let  imgIndex = name.lastIndexOf(".");//获取分隔符最后出现的位置
  let  type = name.substr(imgIndex + 1);//从分隔符位置开始截取，得到的是如：jpg，png，jpeg等
  return type
}

const conversion = num =>{
   let nums = Number(num) * 1000

    return nums
}

const division = num =>{
  let nums = Number(num) / 1000

  return nums
}



//验证手机号
function phoneReg(phoneNum) {

  var reg = /^1[3|4|5|7|8][0-9]{9}$/; //验证规则 
  //var reg = /^1[0-9]{10}$/;//可扩展
  //var reg = /^1[3-578]\d{9}$/;同第一行
  if (!reg.test(phoneNum)) {
    wx.showToast({
      title: '请输入正确的手机号',
      icon: "none",
      duration: 1000
    })
    setTimeout(function () {
      wx.hideToast()
    }, 2000)
  }
}

function arr(oldVal, newVal, json) {
  for (var i in json) {
    for (var j in json[i]) {
      if (j == oldVal) {
        json[i][newVal] = json[i][j]//修改属性名为newVal
        delete json[i][oldVal]//删除oldVal
      }
    }
  }
};

//
function   uuid2(len, radix) {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  var uuid = [],
      i;
  radix = radix || chars.length;

  if (len) {
      // Compact form
      for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
  } else {
      // rfc4122, version 4 form
      var r;

      // rfc4122 requires these characters
      uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
      uuid[14] = '4';

      // Fill in random data.  At i==19 set the high bits of clock sequence as
      // per rfc4122, sec. 4.1.5
      for (i = 0; i < 36; i++) {
          if (!uuid[i]) {
              r = 0 | Math.random() * 16;
              uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
          }
      }
  }

  return uuid.join('');
};

function formatTimeTwo(number, format) {

  var formateArr = ['Y', 'M', 'D', 'h', 'm', 's'];
  var returnArr = [];
  var date = new Date(number);
  returnArr.push(date.getFullYear());
  returnArr.push(formatNumber(date.getMonth() + 1));
  returnArr.push(formatNumber(date.getDate()));

  returnArr.push(formatNumber(date.getHours()));
  returnArr.push(formatNumber(date.getMinutes()));
  returnArr.push(formatNumber(date.getSeconds()));

  for (var i in returnArr) {
    format = format.replace(formateArr[i], returnArr[i]);
  }
  return format;
}

function timestampToTime(unixtime) {
  var dateTime = new Date(parseInt(unixtime))
  var year = dateTime.getFullYear();
  var month = dateTime.getMonth() + 1;
  var day = dateTime.getDate();
  var hour = dateTime.getHours();
  var minute = dateTime.getMinutes();
  var second = dateTime.getSeconds();
  var now = new Date();
  var now_new = Date.parse(now.toDateString());
  var milliseconds = now_new - dateTime;
  var timeSpanStr = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
  console.log(timeSpanStr)
  return timeSpanStr;
}

function uniq(array){
  var temp = []; //一个新的临时数组
  for(var i = 0; i < array.length; i++){
      if(temp.indexOf(array[i]) == -1){
          temp.push(array[i]);
      }
  }
  return temp;
}

function getDistance(lat1, lng1, lat2, lng2) {
  lat1 = lat1 || 0;
  lng1 = lng1 || 0;
  lat2 = lat2 || 0;
  lng2 = lng2 || 0;

  var rad1 = lat1 * Math.PI / 180.0;
  var rad2 = lat2 * Math.PI / 180.0;
  var a = rad1 - rad2;
  var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
  var r = 6378137;
  var distance = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));

  return distance;
}

const deepClone = function(initalObj) {
  var obj = {};
  obj = JSON.parse(JSON.stringify(initalObj));
  return obj;
}
// 限制一位小数
const clearNumOne = (value) =>{
  let filterValue = value.toString();
  console.log(filterValue,99)
  filterValue = filterValue.replace(/^\./g, "");  //  不能以“.”开头
  filterValue = filterValue.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符  
  filterValue = filterValue.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
  filterValue = filterValue.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  filterValue = filterValue.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3');//只能输入两个小数  
  console.log(filterValue,1010)
  if (filterValue.indexOf(".") < 0 && filterValue != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
    filterValue = parseFloat(filterValue);
  }
  console.log(filterValue,1010)
  return filterValue;
}

// 限制两位小数
const clearNum = (value) =>{
  let filterValue = value.toString();
  filterValue = filterValue.replace(/^\./g, "");  //  不能以“.”开头
  filterValue = filterValue.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符  
  filterValue = filterValue.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
  filterValue = filterValue.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  filterValue = filterValue.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
  if (filterValue.indexOf(".") < 0 && filterValue != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
    filterValue = parseFloat(filterValue);
  }
  return filterValue;
}


//  处理金额  分->元 非四舍五入
export function dealFenToYuan (num)  {
  var n = num;
  n=n/100;
  let res = n.toString().match(new RegExp(/^\d+(?:\.\d{0,2})?/))
  return res
};

export const dealYuanToFen = (num) =>{
  var n = num;
  n=n*100;
  return parseInt(n)
};

function yuanToFen(yuanAmount) {
  if (typeof yuanAmount !== 'number' || isNaN(yuanAmount)) {
    yuanAmount = Number(yuanAmount)
  }
  return Math.round(yuanAmount * 100); 
}


module.exports = {
  formatTime,
  phoneReg,
  siding,
  substrImgType,
  arr,
  conversion,
  division,
  uuid2,
  formatTimeTwo:formatTimeTwo,
  timestampToTime:timestampToTime,
  uniq:uniq,
  getDistance:getDistance,
  deepClone:deepClone,
  clearNumOne:clearNumOne,
  clearNum:clearNum
}
